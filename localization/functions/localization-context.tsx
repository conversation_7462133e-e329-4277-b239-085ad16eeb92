"use client"

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react"

type LocalizationContextType = {
  locale: string
  setLocale: (locale: string) => void
}

const LocalizationContext = createContext<LocalizationContextType | undefined>(
  undefined,
)

function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null
  const match = document.cookie.match(new RegExp("(^| )" + name + "=([^;]+)"))
  return match ? decodeURIComponent(match[2]) : null
}

function detectClientLocale(): string {
  const localStorageLocale = localStorage.getItem("locale")
  if (localStorageLocale === "en" || localStorageLocale === "ja")
    return localStorageLocale

  const cookieLocale = getCookie("locale")
  if (cookieLocale === "id" || cookieLocale === "ja") return cookieLocale

  const pathLocale = window.location.pathname.split("/")[1]
  if (pathLocale === "id" || pathLocale === "ja") return pathLocale

  return "id"
}

export function LocalizationProvider({
  initialLocale,
  children,
}: {
  initialLocale: string
  children: ReactNode
}) {
  const [locale, setLocaleState] = useState(initialLocale)

  // useEffect(() => {
  //   const initial = detectClientLocale()
  //   setLocaleState(initial)
  // }, [])

  const setLocale = (newLocale: string) => {
    setLocaleState(newLocale)
    localStorage.setItem("locale", newLocale)
    document.cookie = `locale=${newLocale}; path=/; max-age=31536000`
    //window.location.reload() // or use `router.refresh()` if using App Router
  }

  return (
    <LocalizationContext.Provider value={{ locale, setLocale }}>
      {children}
    </LocalizationContext.Provider>
  )
}

export function useLocalizationContext() {
  const context = useContext(LocalizationContext)
  if (!context) {
    throw new Error(
      "useLocalizationContext must be used inside a LocalizationProvider",
    )
  }
  return context
}
