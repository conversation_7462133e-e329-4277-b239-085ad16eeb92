//@ts-ignore
import { describe, it, expect } from "bun:test"

import { matchPattern } from "@/lib/utils/wildcardPatternMatching"

describe("wildcardToRegex + matchPattern", () => {
  it("matches exact string without wildcard", () => {
    expect(matchPattern("GET /contacts", "GET /contacts")).toBe(true)
    expect(matchPattern("GET /contacts", "POST /contacts")).toBe(false)
  })

  it("matches string ending with wildcard", () => {
    expect(matchPattern("GET /contacts/*", "GET /contacts/123")).toBe(true)
    expect(matchPattern("GET /contacts/*", "GET /contacts/")).toBe(true)
    expect(matchPattern("GET /contacts/*", "GET /contacts")).toBe(false)
  })

  it("matches string starting with wildcard", () => {
    expect(matchPattern("* /contacts", "GET /contacts")).toBe(true)
    expect(matchPattern("* /contacts", "POST /contacts")).toBe(true)
    expect(matchPattern("* /contacts", "/contacts")).toBe(false)
  })

  it("matches full wildcard", () => {
    expect(matchPattern("*", "anything")).toBe(true)
    expect(matchPattern("*", "everything here")).toBe(true)
  })

  it("does not match mismatched patterns", () => {
    expect(matchPattern("GET /users/*", "GET /contacts/123")).toBe(false)
    expect(matchPattern("DELETE /contacts", "DELETE /contacts/1")).toBe(false)
  })

  it("handles complex patterns", () => {
    expect(matchPattern("*/contacts/*", "GET/contacts/123")).toBe(true)
    expect(matchPattern("*/contacts*", "POST/contacts2")).toBe(true)
    expect(matchPattern("PUT /contacts*", "PUT /contacts")).toBe(true)
    expect(matchPattern("PUT /contacts*", "PUT /contact")).toBe(false)
  })

  it("correctly escapes special regex characters", () => {
    expect(
      matchPattern("GET /docs/*.(pdf|doc)", "GET /docs/123.(pdf|doc)"),
    ).toBe(true)
    expect(matchPattern("GET /docs/*.(pdf|doc)", "GET /docs/123.pdf")).toBe(
      false,
    ) // because the literal ".*\\(pdf\\|doc\\)" does not allow .pdf
  })

  it("matches unicode and special characters", () => {
    expect(matchPattern("José María O'Connor", "José María O'Connor")).toBe(
      true,
    )
    expect(matchPattern("José María O'Connor", "José María O'Connor")).toBe(
      true,
    )
  })

  it("matches wildcard with unicode and special characters", () => {
    expect(
      matchPattern("* José María O'Connor", "GET José María O'Connor"),
    ).toBe(true)
    expect(matchPattern("José María O'*", "José María O'Connor")).toBe(true)
  })

  it("test", () => {
    expect(matchPattern("/api/v1/*", "/api/v1/contacts")).toBe(true)
    expect(matchPattern("/api/v1/*", "/api/v1/contacts/123")).toBe(true)
    expect(matchPattern("/api/v1/*", "/api/v1/contacts/123/456")).toBe(true)
    expect(matchPattern("/api/v1/*", "/api/v1/contacts/123/456/789")).toBe(true)
  })
})
