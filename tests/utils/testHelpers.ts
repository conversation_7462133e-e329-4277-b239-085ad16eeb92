import { ResponseWrapper } from "@/lib/types/responseWrapper"

// Test helper functions for API testing

export interface ApiTestResponse {
  status: number
  body: ResponseWrapper<any>
}

export function expectSuccessResponse(
  response: ApiTestResponse,
  expectedStatus: number = 200,
) {
  expect(response.status).toBe(expectedStatus)
  expect(response.body.status).toBe("success")
  expect(response.body.data).toBeDefined()
  expect(response.body.error).toBeUndefined()
}

export function expectErrorResponse(
  response: ApiTestResponse,
  expectedStatus: number,
  expectedErrorCodes?: string[],
) {
  expect(response.status).toBe(expectedStatus)
  expect(response.body.status).toBe("failed")
  expect(response.body.data).toBeUndefined()
  expect(response.body.error).toBeDefined()
  expect(Array.isArray(response.body.error)).toBe(true)

  if (expectedErrorCodes) {
    expect(response.body.errorCodes).toBeDefined()
    expectedErrorCodes.forEach((code) => {
      expect(response.body.errorCodes).toContain(code)
    })
  }
}

export function expectValidationError(
  response: ApiTestResponse,
  fieldErrors?: string[],
) {
  expectErrorResponse(response, 400, ["ERROR_VALIDATION_FAILED"])

  if (fieldErrors) {
    fieldErrors.forEach((error) => {
      expect(response.body.error?.some((err) => err.includes(error))).toBe(true)
    })
  }
}

export function expectNotFoundError(response: ApiTestResponse) {
  expectErrorResponse(response, 404, ["ERROR_NOT_FOUND"])
}

export function createValidAiRuleData() {
  return {
    name: "Test Rule",
    description: "A test rule for validation",
    conditions: ["User says test", "User mentions validation"],
    actions: ["Show test response"],
    tags: ["test"],
    isActive: true,
  }
}

export function createValidContactData() {
  return {
    name: "Test Contact",
    phone: "+**********",
    email: "<EMAIL>",
    tags: ["test"],
    notes: [{ text: "Test note", createdAt: new Date().toISOString() }],
  }
}

export function createValidMessageData() {
  return {
    conversationId: "test-conversation-123",
    text: "Hello, this is a test message",
    session: "test-session-1",
    provider: "waha",
  }
}

export function createValidTypingData() {
  return {
    status: "typing",
    conversationId: "test-conversation-123",
    session: "test-session-1",
    provider: "waha",
  }
}

export function createInvalidData() {
  return {
    empty: {},
    missingRequired: { description: "Missing required fields" },
    invalidTypes: { name: 123, conditions: "not an array" },
    emptyArrays: { name: "Test", conditions: [], actions: [] },
    invalidEmail: { name: "Test", email: "invalid-email" },
    invalidPhone: { name: "Test", phone: "" },
  }
}

// Mock URLSearchParams for testing
export function createMockSearchParams(
  params: Record<string, string>,
): URLSearchParams {
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    searchParams.set(key, value)
  })
  return searchParams
}

// Async test wrapper for better error handling
export async function runAsyncTest<T>(testFn: () => Promise<T>): Promise<T> {
  try {
    return await testFn()
  } catch (error) {
    console.error("Test error:", error)
    throw error
  }
}

// Date comparison helpers
export function expectRecentDate(
  date: Date | string,
  withinSeconds: number = 5,
) {
  const testDate = typeof date === "string" ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.abs(now.getTime() - testDate.getTime()) / 1000
  expect(diffInSeconds).toBeLessThan(withinSeconds)
}

export function expectValidId(id: string) {
  expect(id).toBeDefined()
  expect(typeof id).toBe("string")
  expect(id.length).toBeGreaterThan(0)
}

// Array helpers
export function expectArrayContainsObject<T>(
  array: T[],
  predicate: (item: T) => boolean,
  message?: string,
) {
  const found = array.some(predicate)
  expect(found).toBe(true)
  if (message) {
    expect(found, message).toBe(true)
  }
}

export function expectArrayLength<T>(array: T[], expectedLength: number) {
  expect(Array.isArray(array)).toBe(true)
  expect(array.length).toBe(expectedLength)
}
