//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleResendVerification } from "@/app/api/v1/auth/resend-verification/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Resend Verification API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/auth/resend-verification", () => {
    it("should successfully resend verification for unverified user", async () => {
      // Setup: Register a user (creates initial verification token)
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Resend User",
      }

      await businessLogic.register(userData)

      // Get initial verification token
      const initialToken = await dbRepository.getFirstEmailVerificationToken()

      // Test: Resend verification
      const resendData = {
        email: "<EMAIL>",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      // Verify API response
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Verification email sent")

      // Verify new verification token was created
      expect(await dbRepository.getEmailVerificationCount()).toBe(1)

      // Verify new token is different from initial token
      const newToken = await dbRepository.getFirstEmailVerificationToken()
      expect(newToken).not.toBe(initialToken)
    })

    it("should handle resend for already verified user", async () => {
      // Setup: Register and verify a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Verified User",
      }

      await businessLogic.register(userData)

      // Verify the user
      const verificationToken =
        (await dbRepository.getFirstEmailVerificationToken()) || ""
      await businessLogic.verifyEmail(verificationToken)

      // Test: Try to resend verification
      const resendData = {
        email: "<EMAIL>",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Email is already verified")
    })

    it("should handle resend for non-existent user", async () => {
      const resendData = {
        email: "<EMAIL>",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      // Current implementation returns 400 for non-existent users
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User not found")

      // No verification token should be created
      expect(await dbRepository.getEmailVerificationCount()).toBe(0)
    })

    it("should fail with missing email", async () => {
      const resendData = {}

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty email", async () => {
      const resendData = {
        email: "",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with invalid email format", async () => {
      const resendData = {
        email: "invalid-email",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should handle case-sensitive email lookup", async () => {
      // Setup: Register a user with mixed case email
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Case User",
      }

      await businessLogic.register(userData)

      // Test: Resend with lowercase email (different case)
      const resendData = {
        email: "<EMAIL>",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      // Current implementation treats emails as case-sensitive
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User not found")
    })

    it("should replace existing verification token", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Replace User",
      }

      await businessLogic.register(userData)

      // Get initial token
      const initialToken =
        (await dbRepository.getFirstEmailVerificationToken()) || ""

      // First resend
      const resendData = {
        email: "<EMAIL>",
      }

      const result1 = await implHandleResendVerification(
        resendData,
        businessLogic,
      )
      expect(result1.status).toBe(200)

      const firstResendToken =
        (await dbRepository.getFirstEmailVerificationToken()) || ""

      // Second resend
      const result2 = await implHandleResendVerification(
        resendData,
        businessLogic,
      )
      expect(result2.status).toBe(200)

      const secondResendToken =
        (await dbRepository.getFirstEmailVerificationToken()) || ""

      // Should still have only one token, but it should be different each time
      expect(await dbRepository.getEmailVerificationCount()).toBe(1)
      expect(initialToken).not.toBe(firstResendToken)
      expect(firstResendToken).not.toBe(secondResendToken)
    })

    it("should handle multiple users requesting resend", async () => {
      // Setup: Register multiple users
      const user1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 1",
      }

      const user2: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 2",
      }

      await businessLogic.register(user1)
      await businessLogic.register(user2)

      // Both request resend verification
      const result1 = await implHandleResendVerification(
        { email: "<EMAIL>" },
        businessLogic,
      )
      const result2 = await implHandleResendVerification(
        { email: "<EMAIL>" },
        businessLogic,
      )

      expect(result1.status).toBe(200)
      expect(result2.status).toBe(200)
      expect(await dbRepository.getEmailVerificationCount()).toBe(2)
    })

    it("should handle whitespace in email", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Trim User",
      }

      await businessLogic.register(userData)

      // Test: Resend with whitespace
      const resendData = {
        email: "  <EMAIL>  ",
      }

      const result = await implHandleResendVerification(
        resendData,
        businessLogic,
      )

      // Current implementation doesn't trim whitespace
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should generate unique verification tokens", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Unique User",
      }

      await businessLogic.register(userData)

      // Request multiple resends and collect tokens
      const tokens = new Set()

      for (let i = 0; i < 5; i++) {
        await implHandleResendVerification(
          { email: "<EMAIL>" },
          businessLogic,
        )
        const currentToken =
          (await dbRepository.getFirstEmailVerificationToken()) || ""
        tokens.add(currentToken)
      }

      // All tokens should be unique (though only the last one is valid)
      expect(tokens.size).toBe(5)
    })

    it("should handle rate limiting gracefully", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Rate Limit User",
      }

      await businessLogic.register(userData)

      // Send multiple rapid requests
      const promises = []
      for (let i = 0; i < 10; i++) {
        promises.push(
          implHandleResendVerification(
            { email: "<EMAIL>" },
            businessLogic,
          ),
        )
      }

      const results = await Promise.all(promises)

      // All should succeed (in this simple implementation)
      results.forEach((result) => {
        expect(result.status).toBe(200)
      })

      // Current implementation doesn't implement rate limiting
      expect(await dbRepository.getEmailVerificationCount()).toBe(10)
    })
  })
})
