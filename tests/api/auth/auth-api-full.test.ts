//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleLogin } from "@/app/api/v1/auth/login/impl"
import { implHandleRefreshToken } from "@/app/api/v1/auth/refresh_token/impl"
import { implHandleRegister } from "@/app/api/v1/auth/register/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { Login, RefreshTokenInput, UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Full Authentication API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Register API", () => {
    it("should successfully register a new user and conform to API specification", async () => {
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "New User",
      }

      const res = await implHandleRegister(userData, businessLogic)

      // Verify API response format
      expect(res.status).toBe(201)
      expect(res.body.status).toBe("success")
      expect(res.body.data).toBeDefined()
      expect(res.body.data.token).toBeDefined()
      expect(res.body.data.refresh_token).toBeDefined()
      expect(res.body.data.user).toBeDefined()

      // Verify user data in response
      expect(res.body.data.user.email).toBe("<EMAIL>")
      expect(res.body.data.user.name).toBe("New User")
      expect(res.body.data.user.id).toBeDefined()
      expect(res.body.data.user.createdAt).toBeDefined()
      expect(res.body.data.user.updatedAt).toBeDefined()

      // Verify data persistence in repository
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(1)

      // Verify token is valid
      const validatedUser = await businessLogic.validateToken(
        res.body.data.token,
      )
      expect(validatedUser).toBeDefined()
      expect(validatedUser!.email).toBe("<EMAIL>")
    })

    it("should return 409 for duplicate email registration and conform to API specification", async () => {
      // Setup: Register a user first
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "First User",
      }

      await businessLogic.register(userData)

      // Test: Try to register with same email
      const duplicateUserData: UserRegister = {
        email: "<EMAIL>",
        password: "differentpassword",
        name: "Second User",
      }

      const res = await implHandleRegister(duplicateUserData, businessLogic)

      // Verify API response format for error
      expect(res.status).toBe(409)
      expect(res.body.status).toBe("failed")
      expect(res.body.data).toBeUndefined()
      expect(res.body.error).toBeDefined()
      expect(res.body.error?.[0]).toContain(
        "User with this email already exists",
      )
      expect(res.body.errorCodes).toBeDefined()
      expect(res.body.errorCodes).toContain("ERROR_USER_EXISTS")

      // Verify only one user was created
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(1) // Only the first registration token
    })

    it("should return 400 for invalid registration data and conform to API specification", async () => {
      // Test: Register with invalid data (missing required fields)
      const invalidUserData = {
        email: "invalid-email", // Invalid email format
        // password is missing
        name: "",
      }

      const res = await implHandleRegister(invalidUserData, businessLogic)

      // Verify API response format for validation error
      expect(res.status).toBe(400)
      expect(res.body.status).toBe("failed")
      expect(res.body.data).toBeUndefined()
      expect(res.body.error).toBeDefined()
      expect(res.body.error!.length).toBeGreaterThan(0)
      expect(res.body.errorCodes).toBeDefined()
      expect(res.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")

      // Verify no data was created
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getTokenCount()).toBe(0)
    })
  })

  describe("Login API", () => {
    it("should successfully login with valid credentials and conform to API specification", async () => {
      // Setup: Register a user first
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Login User",
      }

      await businessLogic.register(userData)

      // Test: Login with the registered user
      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123",
      }

      const res = await implHandleLogin(loginData, businessLogic)

      // Verify API response format
      expect(res.status).toBe(200)
      expect(res.body.status).toBe("success")
      expect(res.body.data).toBeDefined()
      expect(res.body.data.token).toBeDefined()
      expect(res.body.data.refresh_token).toBeDefined()
      expect(res.body.data.user).toBeDefined()

      // Verify user data in response
      expect(res.body.data.user.email).toBe("<EMAIL>")
      expect(res.body.data.user.name).toBe("Login User")

      // Verify data persistence in repository
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(2) // One from register, one from login
    })

    it("should return 401 for invalid credentials and conform to API specification", async () => {
      // Setup: Register a user first
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Login User",
      }

      await businessLogic.register(userData)

      // Test: Login with wrong password
      const loginData: Login = {
        email: "<EMAIL>",
        password: "wrongpassword",
      }

      const res = await implHandleLogin(loginData, businessLogic)

      // Verify API response format for error
      expect(res.status).toBe(401)
      expect(res.body.status).toBe("failed")
      expect(res.body.data).toBeUndefined()
      expect(res.body.error).toBeDefined()
      expect(res.body.error?.[0]).toContain("Invalid email or password")
      expect(res.body.errorCodes).toBeDefined()
      expect(res.body.errorCodes).toContain("ERROR_INVALID_CREDENTIALS")

      // Verify no additional tokens were created
      expect(await dbRepository.getTokenCount()).toBe(1) // Only the registration token
    })
  })

  describe("Refresh Token API", () => {
    it("should successfully refresh token with valid refresh token and conform to API specification", async () => {
      // Setup: Register and login to get tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Refresh User",
      }

      const registerResult = await businessLogic.register(userData)

      // Test: Refresh the token
      const refreshData: RefreshTokenInput = {
        token: registerResult.token,
        refresh_token: registerResult.refresh_token,
      }

      const res = await implHandleRefreshToken(refreshData, businessLogic)

      // Verify API response format
      expect(res.status).toBe(200)
      expect(res.body.status).toBe("success")
      expect(res.body.data).toBeDefined()
      expect(res.body.data.token).toBeDefined()
      expect(res.body.data.refresh_token).toBeDefined()
      expect(res.body.data.user).toBeDefined()

      // Verify new tokens are different from original
      expect(res.body.data.token).not.toBe(registerResult.token)
      expect(res.body.data.refresh_token).not.toBe(registerResult.refresh_token)

      // Verify user data in response
      expect(res.body.data.user.email).toBe("<EMAIL>")
      expect(res.body.data.user.name).toBe("Refresh User")

      // Verify data persistence in repository
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(1) // Token should be updated, not added

      // Verify new token is valid
      const validatedUser = await businessLogic.validateToken(
        res.body.data.token,
      )
      expect(validatedUser).toBeDefined()
      expect(validatedUser!.email).toBe("<EMAIL>")
    })

    it("should return 401 for invalid refresh token and conform to API specification", async () => {
      // Test: Try to refresh with invalid tokens
      const invalidRefreshData: RefreshTokenInput = {
        token: "invalid-token",
        refresh_token: "invalid-refresh-token",
      }

      const res = await implHandleRefreshToken(
        invalidRefreshData,
        businessLogic,
      )

      // Verify API response format for error
      expect(res.status).toBe(401)
      expect(res.body.status).toBe("failed")
      expect(res.body.data).toBeUndefined()
      expect(res.body.error).toBeDefined()
      expect(res.body.error?.[0]).toContain("Invalid or expired refresh token")
      expect(res.body.errorCodes).toBeDefined()
      expect(res.body.errorCodes).toContain("ERROR_INVALID_TOKEN")

      // Verify no data was created
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getTokenCount()).toBe(0)
    })

    it("should return 400 for invalid request body and conform to API specification", async () => {
      // Test: Refresh with invalid data (missing fields)
      const invalidRefreshData = {
        token: "some-token",
        // refresh_token is missing
      }

      const res = await implHandleRefreshToken(
        invalidRefreshData,
        businessLogic,
      )

      // Verify API response format for validation error
      expect(res.status).toBe(400)
      expect(res.body.status).toBe("failed")
      expect(res.body.data).toBeUndefined()
      expect(res.body.error).toBeDefined()
      expect(res.body.error!.length).toBeGreaterThan(0)
      expect(res.body.errorCodes).toBeDefined()
      expect(res.body.errorCodes).toContain("ERROR_VALIDATION_FAILED")

      // Verify no data was created
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getTokenCount()).toBe(0)
    })
  })

  describe("Complete Authentication Flow Integration", () => {
    it("should handle complete register -> login -> refresh -> validate flow", async () => {
      // Step 1: Register a new user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Flow Test User",
      }

      const registerRes = await implHandleRegister(userData, businessLogic)
      expect(registerRes.status).toBe(201)
      expect(registerRes.body.status).toBe("success")

      const registerToken = registerRes.body.data.token
      const registerRefreshToken = registerRes.body.data.refresh_token

      // Step 2: Login with the registered user
      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123",
      }

      const loginRes = await implHandleLogin(loginData, businessLogic)
      expect(loginRes.status).toBe(200)
      expect(loginRes.body.status).toBe("success")

      const loginToken = loginRes.body.data.token
      const loginRefreshToken = loginRes.body.data.refresh_token

      // Verify login tokens are different from register tokens
      expect(loginToken).not.toBe(registerToken)
      expect(loginRefreshToken).not.toBe(registerRefreshToken)

      // Step 3: Refresh the login token
      const refreshData: RefreshTokenInput = {
        token: loginToken,
        refresh_token: loginRefreshToken,
      }

      const refreshRes = await implHandleRefreshToken(
        refreshData,
        businessLogic,
      )
      expect(refreshRes.status).toBe(200)
      expect(refreshRes.body.status).toBe("success")

      const newToken = refreshRes.body.data.token
      const newRefreshToken = refreshRes.body.data.refresh_token

      // Verify refreshed tokens are different from login tokens
      expect(newToken).not.toBe(loginToken)
      expect(newRefreshToken).not.toBe(loginRefreshToken)

      // Step 4: Validate the new token works
      const validatedUser = await businessLogic.validateToken(newToken)
      expect(validatedUser).toBeDefined()
      expect(validatedUser!.email).toBe("<EMAIL>")
      expect(validatedUser!.name).toBe("Flow Test User")

      // Step 5: Verify old tokens are invalidated
      const invalidatedUser = await businessLogic.validateToken(loginToken)
      expect(invalidatedUser).toBeNull()

      // Verify final repository state
      expect(await dbRepository.getUserCount()).toBe(1)
      expect(await dbRepository.getTokenCount()).toBe(2) // Register token + refreshed login token
    })

    it("should handle logout and token invalidation", async () => {
      // Setup: Register and login
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Logout Test User",
      }

      const registerRes = await implHandleRegister(userData, businessLogic)
      const token = registerRes.body.data.token

      // Verify token is valid before logout
      const userBeforeLogout = await businessLogic.validateToken(token)
      expect(userBeforeLogout).toBeDefined()

      // Logout
      await businessLogic.logout(token)

      // Verify token is invalid after logout
      const invalidatedUser = await businessLogic.validateToken(token)
      expect(invalidatedUser).toBeNull()

      // Verify repository state
      expect(await dbRepository.getUserCount()).toBe(1) // User still exists
      expect(await dbRepository.getTokenCount()).toBe(0) // No tokens should exist
    })

    it("should handle user deletion and cascade token cleanup", async () => {
      // Setup: Register user and create multiple tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Delete Test User",
      }

      const registerRes = await implHandleRegister(userData, businessLogic)
      const userId = registerRes.body.data.user.id

      // Create additional tokens through login
      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123",
      }
      await implHandleLogin(loginData, businessLogic)

      // Verify multiple tokens exist
      expect(await dbRepository.getTokenCount()).toBe(2)

      // Delete user
      await businessLogic.deleteUser(userId)

      // Verify user and all tokens are deleted
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getTokenCount()).toBe(0)

      // Verify tokens are invalid
      const invalidatedUser = await businessLogic.validateToken(
        registerRes.body.data.token,
      )
      expect(invalidatedUser).toBeNull()
    })
  })
})
