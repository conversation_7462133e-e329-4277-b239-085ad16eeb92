//@ts-ignore
import { beforeEach, describe, expect, it, test } from "bun:test"
import { implHandleLogout } from "@/app/api/v1/auth/logout/impl"
import { MongoAuthDBRepository } from "@/lib/repositories/auth"
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic"
import type { UserRegister } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper"

describe("Logout API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface
  let dbRepository: TestAuthDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth")
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver)
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AuthBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/auth/logout", () => {
    it("should successfully logout with valid token", async () => {
      // Setup: Register a user to get a token
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Logout User",
      }

      const registerResult = await businessLogic.register(userData)
      const token = registerResult.token

      // Verify token works before logout
      const validatedUser1 = await businessLogic.validateToken(token)
      expect(validatedUser1).toBeTruthy()

      // Test: Logout
      const logoutData = {
        token: token,
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      // Verify API response
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")

      // Verify token no longer works
      const validatedUser2 = await businessLogic.validateToken(token)
      expect(validatedUser2).toBeNull()
    })

    it("should handle logout with invalid token gracefully", async () => {
      const logoutData = {
        token: "invalid-token",
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      // The current implementation doesn't validate tokens, so it returns success
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")
    })

    it("should fail logout with missing token", async () => {
      const logoutData = {}

      const result = await implHandleLogout(logoutData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail logout with empty token", async () => {
      const logoutData = {
        token: "",
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should handle logout with already invalidated token gracefully", async () => {
      // Setup: Register a user and get token
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Invalidated User",
      }

      const registerResult = await businessLogic.register(userData)
      const token = registerResult.token

      // First logout (should succeed)
      const logoutData = {
        token: token,
      }

      const result1 = await implHandleLogout(logoutData, businessLogic)
      expect(result1.status).toBe(200)

      // Try to logout again with same token (current implementation allows this)
      const result2 = await implHandleLogout(logoutData, businessLogic)
      expect(result2.status).toBe(200)
      expect(result2.body.data.message).toContain("Logged out successfully")
    })

    it("should handle logout with expired token gracefully", async () => {
      // This test would require mocking time or creating an expired token
      // For now, we'll test with an invalid format that simulates expiry
      const logoutData = {
        token: "expired.jwt.token",
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      // Current implementation doesn't validate token format
      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")
    })

    it("should only invalidate specific token, not all user tokens", async () => {
      // Setup: Register a user and login multiple times to get multiple tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Multiple User",
      }

      const registerResult = await businessLogic.register(userData)
      const token1 = registerResult.token

      // Login again to get second token
      const loginResult = await businessLogic.login({
        email: "<EMAIL>",
        password: "password123",
      })
      const token2 = loginResult.token

      // Verify both tokens work
      const validatedUser1 = await businessLogic.validateToken(token1)
      const validatedUser2 = await businessLogic.validateToken(token2)
      expect(validatedUser1).toBeTruthy()
      expect(validatedUser2).toBeTruthy()

      // Logout with first token
      const logoutData = {
        token: token1,
      }

      const result = await implHandleLogout(logoutData, businessLogic)
      expect(result.status).toBe(200)

      // Verify first token no longer works
      const validatedUser3 = await businessLogic.validateToken(token1)
      expect(validatedUser3).toBeNull()

      // Verify second token still works
      const validatedUser4 = await businessLogic.validateToken(token2)
      expect(validatedUser4).toBeTruthy()
    })

    it("should handle logout for different users independently", async () => {
      // Setup: Register two users
      const user1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 1",
      }

      const user2: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 2",
      }

      const registerResult1 = await businessLogic.register(user1)
      const registerResult2 = await businessLogic.register(user2)

      const token1 = registerResult1.token
      const token2 = registerResult2.token

      // Logout user 1
      const logoutData1 = {
        token: token1,
      }

      const result1 = await implHandleLogout(logoutData1, businessLogic)
      expect(result1.status).toBe(200)

      // Verify user 1 token no longer works
      const validatedUser1 = await businessLogic.validateToken(token1)
      expect(validatedUser1).toBeNull()

      // Verify user 2 token still works
      const validatedUser2 = await businessLogic.validateToken(token2)
      expect(validatedUser2).toBeTruthy()
    })

    it("should handle malformed token gracefully", async () => {
      const logoutData = {
        token: "malformed.token.here",
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")
    })

    it("should handle very long token gracefully", async () => {
      const longToken = "a".repeat(10000) // Very long token
      const logoutData = {
        token: longToken,
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")
    })

    it("should handle special characters in token", async () => {
      const logoutData = {
        token: "token-with-special-chars!@#$%^&*()",
      }

      const result = await implHandleLogout(logoutData, businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.message).toContain("Logged out successfully")
    })

    it("should maintain token count consistency", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Consistency User",
      }

      const registerResult = await businessLogic.register(userData)
      const token = registerResult.token

      // Check initial token count
      const initialTokenCount = await dbRepository.getTokenCount()
      expect(initialTokenCount).toBe(1)

      // Logout
      const logoutData = {
        token: token,
      }

      await implHandleLogout(logoutData, businessLogic)

      // Check token count after logout
      const finalTokenCount = await dbRepository.getTokenCount()
      expect(finalTokenCount).toBe(0) // Token should be removed
    })
  })
})
