import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { AuthDBRepository } from "@/lib/repositories/auth"
import { User } from "@/lib/types/base"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"

type MethodNames = keyof AuthDBRepository

export class TestAuthDBRepositoryWrapper implements AuthDBRepository {
  private callCounts: Record<MethodNames, number> = {} as Record<
    MethodNames,
    number
  >

  constructor(
    private readonly db: AuthDBRepository,
    private readonly driver: InMemoryMongoDriver,
  ) {
    const methods = Object.getOwnPropertyNames(
      Object.getPrototypeOf(db),
    ) as MethodNames[]
    for (const method of methods) {
      if (typeof db[method] === "function") {
        this.callCounts[method] = 0
      }
    }
  }

  getCallCount(method: MethodNames): number {
    return this.callCounts[method] ?? 0
  }

  // --- UserDBRepository ---

  async findUserByEmail(email: string): Promise<User | null> {
    this.callCounts.findUserByEmail++
    return this.db.findUserByEmail(email)
  }

  async findUserById(id: string): Promise<User | null> {
    this.callCounts.findUserById++
    return this.db.findUserById(id)
  }

  async findUserWithPasswordByEmail(
    email: string,
  ): Promise<(User & { password: string }) | null> {
    this.callCounts.findUserWithPasswordByEmail++
    return this.db.findUserWithPasswordByEmail(email)
  }

  async createUser(
    userData: Omit<User, "id" | "createdAt" | "updatedAt"> & {
      password: string
    },
  ): Promise<User> {
    this.callCounts.createUser++
    return this.db.createUser(userData)
  }

  async updateUser(
    id: string,
    userData: Partial<User & { password?: string }>,
  ): Promise<User | null> {
    this.callCounts.updateUser++
    return this.db.updateUser(id, userData)
  }

  async deleteUser(id: string): Promise<boolean> {
    this.callCounts.deleteUser++
    return this.db.deleteUser(id)
  }

  async userExistsByEmail(email: string): Promise<boolean> {
    this.callCounts.userExistsByEmail++
    return this.db.userExistsByEmail(email)
  }

  async userExistsById(id: string): Promise<boolean> {
    this.callCounts.userExistsById++
    return this.db.userExistsById(id)
  }

  // --- TokenDBRepository ---

  async createToken(tokenData: {
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  }): Promise<void> {
    this.callCounts.createToken++
    await this.db.createToken(tokenData)
  }

  async findTokenByValue(token: string): Promise<{
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  } | null> {
    this.callCounts.findTokenByValue++
    return this.db.findTokenByValue(token)
  }

  async findTokenByRefreshToken(refreshToken: string): Promise<{
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  } | null> {
    this.callCounts.findTokenByRefreshToken++
    return this.db.findTokenByRefreshToken(refreshToken)
  }

  async updateToken(
    refreshToken: string,
    newTokenData: {
      token: string
      refresh_token: string
      updatedAt: Date
      expiresAt: Date
    },
  ): Promise<void> {
    this.callCounts.updateToken++
    await this.db.updateToken(refreshToken, newTokenData)
  }

  async deleteToken(token: string): Promise<void> {
    this.callCounts.deleteToken++
    await this.db.deleteToken(token)
  }

  async deleteTokenByRefreshToken(refreshToken: string): Promise<void> {
    this.callCounts.deleteTokenByRefreshToken++
    await this.db.deleteTokenByRefreshToken(refreshToken)
  }

  async deleteTokensByUserId(userId: string): Promise<void> {
    this.callCounts.deleteTokensByUserId++
    await this.db.deleteTokensByUserId(userId)
  }

  // --- PasswordResetDBRepository ---

  async createPasswordResetRequest(data: {
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  }): Promise<void> {
    this.callCounts.createPasswordResetRequest++
    await this.db.createPasswordResetRequest(data)
  }

  async findPasswordResetByToken(token: string): Promise<{
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  } | null> {
    this.callCounts.findPasswordResetByToken++
    return this.db.findPasswordResetByToken(token)
  }

  async deletePasswordResetByToken(token: string): Promise<void> {
    this.callCounts.deletePasswordResetByToken++
    await this.db.deletePasswordResetByToken(token)
  }

  async deletePasswordResetsByUserId(userId: string): Promise<void> {
    this.callCounts.deletePasswordResetsByUserId++
    await this.db.deletePasswordResetsByUserId(userId)
  }

  // --- EmailVerificationDBRepository ---

  async createEmailVerificationRequest(data: {
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  }): Promise<void> {
    this.callCounts.createEmailVerificationRequest++
    await this.db.createEmailVerificationRequest(data)
  }

  async findEmailVerificationByToken(token: string): Promise<{
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  } | null> {
    this.callCounts.findEmailVerificationByToken++
    return this.db.findEmailVerificationByToken(token)
  }

  async deleteEmailVerificationByToken(token: string): Promise<void> {
    this.callCounts.deleteEmailVerificationByToken++
    await this.db.deleteEmailVerificationByToken(token)
  }

  async deleteEmailVerificationsByUserId(userId: string): Promise<void> {
    this.callCounts.deleteEmailVerificationsByUserId++
    await this.db.deleteEmailVerificationsByUserId(userId)
  }

  // --- Test Utility ---

  async clear(): Promise<void> {
    await this.driver.clear()
  }

  getCalledMethods(): MethodNames[] {
    return Object.entries(this.callCounts)
      .filter(([_, count]) => count > 0)
      .map(([name]) => name as MethodNames)
  }

  async getUserCount(): Promise<number> {
    const usersCollection = this.driver.getCollection(MONGO_COLLECTIONS.USERS)
    return usersCollection.countDocuments()
  }

  async getTokenCount(): Promise<number> {
    const tokensCollection = this.driver.getCollection(
      MONGO_COLLECTIONS.USER_TOKENS,
    )
    return tokensCollection.countDocuments()
  }

  async getPasswordResetCount(): Promise<number> {
    const resetsCollection = this.driver.getCollection(
      MONGO_COLLECTIONS.PASSWORD_RESETS,
    )
    return resetsCollection.countDocuments()
  }

  async getEmailVerificationCount(): Promise<number> {
    const verificationsCollection = this.driver.getCollection(
      MONGO_COLLECTIONS.EMAIL_VERIFICATIONS,
    )
    return verificationsCollection.countDocuments()
  }

  async getFirstEmailVerificationToken(): Promise<string | null> {
    const verifications = await this.driver
      .getCollection(MONGO_COLLECTIONS.EMAIL_VERIFICATIONS)
      .find({})
      .toArray()

    return verifications.length > 0 ? verifications[0].token : null
  }
}
