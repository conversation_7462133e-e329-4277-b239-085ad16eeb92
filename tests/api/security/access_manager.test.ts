// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import {
  AccessRule,
  CrudAction,
  GroupRoleResourceAccessManager,
  RoleStore,
  RoleStoreRedisRepository,
  RuleStore,
  RuleStoreRedisRepository,
} from "@/lib/repositories/AccessManager"
import { MockRedisDriver, TestAuditLogger } from "./access_manager.helper"

describe("GroupRoleResourceAccessManager (Wildcard Only)", () => {
  let accessManager: GroupRoleResourceAccessManager
  let ruleStore: RuleStore
  let roleStore: RoleStore
  let auditLog: TestAuditLogger

  beforeEach(() => {
    const redis = new MockRedisDriver()
    ruleStore = new RuleStoreRedisRepository(redis)
    roleStore = new RoleStoreRedisRepository(redis)
    auditLog = new TestAuditLogger()
    accessManager = new GroupRoleResourceAccessManager(
      "group:org:company1",
      roleStore,
      ruleStore,
      auditLog,
    )
  })

  it("should allow access with wildcard role and resource", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "* /contacts/*",
        group: "group:org:company1",
        role: "*",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "GET /contacts/1",
      action: "read",
    })

    expect(result).toBe(true)
  })

  it("should allow access with wildcard resource and exact role", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "*contact-1",
        group: "group:org:company1",
        role: "cs",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "some-contact-1",
      action: "read",
    })

    expect(result).toBe(true)
  })

  const actions: CrudAction[] = ["create", "read", "update", "delete"]

  actions.forEach((action) => {
    it(`should allow ${action} action with full wildcard resource`, async () => {
      await ruleStore.updateRules("group:org:company1", [
        {
          resource: "*",
          group: "group:org:company1",
          role: "admin",
          actions: [action],
        },
      ])

      await roleStore.setUserRoles("admin-user", "group:org:company1", [
        { name: "admin", level: 10 },
      ])

      const result = await accessManager.isAllowed({
        userId: "admin-user",
        resource: "random-resource",
        action,
      })

      expect(result).toBe(true)
    })
  })

  it("should deny access with incorrect role", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "*-contact",
        group: "group:org:company1",
        role: "cs",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "guest", level: 0 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "abc-contact",
      action: "read",
    })

    expect(result).toBe(false)
  })

  it("should deny access for wrong action", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "doc-*",
        group: "group:org:company1",
        role: "cs",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "doc-42",
      action: "delete",
    })

    expect(result).toBe(false)
  })

  it("should deny access when no rules exist", async () => {
    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "any-doc",
      action: "read",
    })

    expect(result).toBe(false)
  })

  it("should allow access after wildcard rule added", async () => {
    await accessManager.addAccessRule({
      group: "group:org:company1",
      resource: "public-*",
      role: "auditor",
      actions: ["read"],
    })

    await roleStore.setUserRoles("auditor-1", "group:org:company1", [
      { name: "auditor", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "auditor-1",
      resource: "public-resource-1",
      action: "read",
    })

    expect(result).toBe(true)
  })

  it("should remove wildcard rule and deny access", async () => {
    await accessManager.addAccessRule({
      group: "group:org:company1",
      resource: "secure-*",
      role: "auditor",
      actions: ["read"],
    })

    await roleStore.setUserRoles("auditor-1", "group:org:company1", [
      { name: "auditor", level: 1 },
    ])

    const before = await accessManager.isAllowed({
      userId: "auditor-1",
      resource: "secure-123",
      action: "read",
    })
    expect(before).toBe(true)

    await accessManager.removeAccessRule({
      group: "group:org:company1",
      resource: "secure-*",
      role: "auditor",
      actions: ["read"],
    })

    const after = await accessManager.isAllowed({
      userId: "auditor-1",
      resource: "secure-123",
      action: "read",
    })
    expect(after).toBe(false)
  })

  it("should override wildcard with specific rule", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "*",
        group: "group:org:company1",
        role: "admin",
        actions: ["read"],
      },
      {
        resource: "contact-override",
        group: "group:org:company1",
        role: "cs",
        actions: ["update"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "contact-override",
      action: "update",
    })

    expect(result).toBe(true)
  })

  it("should deny access if rule uses colon param like /contacts/:id", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "GET /contacts/:id", // invalid in wildcard-only logic
        group: "group:org:company1",
        role: "*",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "GET /contacts/123",
      action: "read",
    })

    expect(result).toBe(false) // it must not match
  })
  it("should allow access with wildcard rule like GET /contacts/*", async () => {
    await ruleStore.updateRules("group:org:company1", [
      {
        resource: "GET /contacts/*",
        group: "group:org:company1",
        role: "*",
        actions: ["read"],
      },
    ])

    await roleStore.setUserRoles("user-1", "group:org:company1", [
      { name: "cs", level: 1 },
    ])

    const result = await accessManager.isAllowed({
      userId: "user-1",
      resource: "GET /contacts/123",
      action: "read",
    })

    expect(result).toBe(true) // should pass
  })
})
