import {
  AccessRule,
  AuditLog,
  CrudAction,
  RoleStore,
  RuleStore,
  UserRole,
} from "@/lib/repositories/AccessManager"
import { InMemoryDriver } from "@/lib/repositories/RedisDriver"
import { AnyCnameRecord } from "node:dns"

// Mock in-memory store for rules
// export class InMemoryRuleStore implements RuleStore {
//   private store: Map<string, AccessRule[]> = new Map();

//   async updateRules(groupId: string, rules: AccessRule[]) {
//     this.store.set(groupId, rules);
//   }

//   async add(
//     groupId: string,
//     rule: {
//       resource: string;
//       organization: string;
//       role: string;
//       actions: CrudAction[] | ["*"];
//     }
//   ): Promise<string> {
//     const existing = this.store.get(groupId) || [];
//     existing.push({
//       resource: rule.resource,
//       organization: rule.organization,
//       role: rule.role,
//       actions: rule.actions,
//     });
//     this.store.set(groupId, existing);
//     // Return the groupId as ruleId for simplicity
//     return groupId;
//   }

//   async remove(
//     groupId: string,
//     ruleToRemove: {
//       resource: string;
//       organization: string;
//       role: string;
//       actions: CrudAction[] | ["*"];
//     }
//   ): Promise<void> {
//     const existing = this.store.get(groupId) || [];
//     const filtered = existing.filter((r) => {
//       // Filter out rule if all fields match exactly
//       if (
//         r.resource === ruleToRemove.resource &&
//         r.role === ruleToRemove.role &&
//         r.actions.length === ruleToRemove.actions.length &&
//         r.actions.every((a) =>
//           ruleToRemove.actions.map((e) => e.toString()).includes(a.toString())
//         )
//       ) {
//         return false;
//       }
//       return true;
//     });
//     this.store.set(groupId, filtered);
//   }

//   async findRulesForResource(
//     resource: string
//   ): Promise<Array<AccessRule & { organization: string }>> {
//     const all: Array<AccessRule & { organization: string }> = [];
//     for (const [org, rules] of this.store.entries()) {
//       for (const rule of rules) {
//         if (rule.resource === "*" || rule.resource === resource) {
//           all.push({ ...rule, organization: org });
//         }
//       }
//     }
//     return all;
//   }
// }

// // Mock RoleStore
// export class InMemoryRoleStore implements RoleStore {
//   // Map userId -> groupId -> roles[]
//   private roleMap: Map<string, Map<string, { name: string; level: number }[]>> =
//     new Map();

//   setUserRoles(
//     userId: string,
//     groupId: string,
//     roles: { name: string; level: number }[]
//   ) {
//     if (!this.roleMap.has(userId)) {
//       this.roleMap.set(userId, new Map());
//     }
//     this.roleMap.get(userId)!.set(groupId, roles);
//   }

//   async getUserRolesInGroup(userId: string, groupId: string) {
//     return this.roleMap.get(userId)?.get(groupId) || [];
//   }

//   async getGroupRoles(groupId: string) {
//     // You can mock org roles if needed; currently returns empty
//     return [];
//   }
// }

export class MockRedisDriver implements InMemoryDriver {
  private store = new Map<string, any>()

  async get<T = any>(key: string): Promise<T | null> {
    return this.store.has(key) ? this.store.get(key) : null
  }

  async set<T = any>(key: string, value: T): Promise<void> {
    this.store.set(key, value)
  }

  async del(key: string): Promise<void> {
    this.store.delete(key)
  }

  async keys(pattern: string): Promise<string[]> {
    throw new Error("Method not implemented.")
  }
}

export class TestAuditLogger implements AuditLog {
  private results: Array<any> = []

  logAccessCheck(info: {
    userId: string
    resource: string
    action: CrudAction
    matchedRule?: AccessRule
    userRole?: UserRole
    result: boolean
  }): void {
    this.results.push(info as any)
  }

  log(...message: any[]) {
    this.results.push(...message)
  }

  getLogs(): Array<any> {
    return this.results
  }

  clear(): void {
    this.results = []
  }
}
