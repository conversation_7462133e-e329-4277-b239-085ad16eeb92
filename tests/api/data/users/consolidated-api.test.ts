//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface"
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic"
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository"
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateUser,
  implHandleGetAllUsers,
  implHandleDeleteUser,
} from "@/app/api/v1/users/impl"
import {
  createMultipleUsers,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated User API Tests", () => {
  let businessLogic: UserBusinessLogicInterface
  let dbRepository: TestUserDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User")
    await driver.connect()
    const originalDb = new MongoUserRepository(driver)
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver)
    businessLogic = new UserBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testUsers = createMultipleUsers()

  describe("implHandleGetAllUsers - Consolidated Function", () => {
    beforeEach(async () => {
      for (const usersData of testUsers) {
        await implHandleCreateUser(usersData, businessLogic)
      }
    })

    it("should get all users when no parameters provided", async () => {
      const result = await implHandleGetAllUsers(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search users by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe User and Bob Johnson User

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testUsers[0].STRING_FIELD) // John Doe User
      expect(STRING_FIELDs).toContain(testUsers[2].STRING_FIELD) // Bob Johnson User
    })

    it("should search users by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All users have "processing" in ARRAY_FIELD2
    })

    it("should filter users by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe User and Bob Johnson User

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testUsers[0].STRING_FIELD) // John Doe User
      expect(STRING_FIELDs).toContain(testUsers[2].STRING_FIELD) // Bob Johnson User
    })

    it("should filter users by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe User and Jane Smith User

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testUsers[0].STRING_FIELD) // John Doe User
      expect(STRING_FIELDs).toContain(testUsers[1].STRING_FIELD) // Jane Smith User
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs![0]).toBe(testUsers[3].STRING_FIELD) // Alice Brown User
      expect(STRING_FIELDs![1]).toBe(testUsers[2].STRING_FIELD) // Bob Johnson User
      expect(STRING_FIELDs![2]).toBe(testUsers[1].STRING_FIELD) // Jane Smith User
      expect(STRING_FIELDs![3]).toBe(testUsers[0].STRING_FIELD) // John Doe User
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP users (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllUsers(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted users when specified", async () => {
      // Get all users first to get one to delete
      const allResult = await implHandleGetAllUsers(businessLogic)
      expect(allResult.status).toBe(200)
      const usersToDelete = allResult.body.data?.items[0]
      expect(usersToDelete).toBeDefined()

      // Soft delete one users
      const deleteResult = await implHandleDeleteUser(
        usersToDelete!.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllUsers(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllUsers(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
