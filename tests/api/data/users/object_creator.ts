import {
  UserCreateInput,
  UserUpdateInput,
} from "@/lib/repositories/users/interface"

/**
 * Factory functions for creating test User objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createUser(variant: number): UserCreateInput {
  const baseUsers: Record<number, UserCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support User",
      STRING_FIELD2: "User for handling customer support requests",
      ARRAY_FIELD2: [
        "user_message_contains('help')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin",
    },
    2: {
      STRING_FIELD: "Simple User",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin",
    },
    3: {
      STRING_FIELD: "Test User",
      STRING_FIELD2: "A test user with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    4: {
      STRING_FIELD: "Tagged User",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin",
    },
    5: {
      STRING_FIELD: "John Doe User",
      STRING_FIELD2: "User for John Doe processing",
      ARRAY_FIELD2: [
        "user_STRING_FIELD_contains('john')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin",
    },
    6: {
      STRING_FIELD: "Jane Smith User",
      STRING_FIELD2: "User for Jane Smith processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin",
    },
    7: {
      STRING_FIELD: "Bob Johnson User",
      STRING_FIELD2: "User for Bob Johnson processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin",
    },
    8: {
      STRING_FIELD: "Alice Brown User",
      STRING_FIELD2: "User for Alice Brown processing",
      ARRAY_FIELD2: [
        "user_STRING_FIELD_contains('alice')",
        "lead_qualification",
      ],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin",
    },
  }

  if (!baseUsers[variant]) {
    throw new Error(
      `User variant ${variant} not found. Available variants: ${Object.keys(baseUsers).join(", ")}`,
    )
  }

  return { ...baseUsers[variant] }
}

// Specialized creator functions for specific test scenarios
export function createMinimalUser(): UserCreateInput {
  return createUser(2)
}

export function createFullUser(): UserCreateInput {
  return createUser(1)
}

export function createUserWithDescription(): UserCreateInput {
  return createUser(3)
}

export function createUserWithTags(): UserCreateInput {
  return createUser(4)
}

// Creator for multiple users (useful for bulk operations and search tests)
export function createMultipleUsers(): UserCreateInput[] {
  return [
    createUser(5), // John Doe User
    createUser(6), // Jane Smith User
    createUser(7), // Bob Johnson User
    createUser(8), // Alice Brown User
  ]
}

// Creator for simple test users (useful for basic CRUD operations)
export function createSimpleUsers(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "A",
      ARRAY_FIELD2: ["1"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "B",
      ARRAY_FIELD2: ["2"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "C",
      ARRAY_FIELD2: ["3"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creator for users with specific tags (useful for filtering tests)
export function createUsersWithTags(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "John Doe",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
      tags: ["Customer", "VIP"],
    },
    {
      STRING_FIELD: "Jane Smith",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
      tags: ["Lead", "Potential"],
    },
    {
      STRING_FIELD: "Bob Johnson",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
      tags: ["Customer"],
    },
    {
      STRING_FIELD: "Alice Brown",
      ARRAY_FIELD2: ["a"],
      ARRAY_FIELD: ["d"],
      createdBy: "admin",
      tags: ["VIP"],
    },
  ]
}

// Update data creators
export function createUserUpdate(variant: number): UserUpdateInput {
  const baseUpdates: Record<number, UserUpdateInput> = {
    1: {
      STRING_FIELD: "Updated User",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin",
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin",
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin",
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin",
    },
    5: {
      isActive: false,
      updatedBy: "admin",
    },
  }

  if (!baseUpdates[variant]) {
    throw new Error(
      `User update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(", ")}`,
    )
  }

  return { ...baseUpdates[variant] }
}

// Specialized update creators
export function createFullUserUpdate(): UserUpdateInput {
  return createUserUpdate(1)
}

export function createNameOnlyUpdate(): UserUpdateInput {
  return createUserUpdate(2)
}

export function createDescriptionOnlyUpdate(): UserUpdateInput {
  return createUserUpdate(3)
}

export function createTagsOnlyUpdate(): UserUpdateInput {
  return createUserUpdate(4)
}

export function createStatusOnlyUpdate(): UserUpdateInput {
  return createUserUpdate(5)
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(
  type:
    | "empty-STRING_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "empty-object",
): any {
  const invalidUpdates = {
    "empty-STRING_FIELD": {
      STRING_FIELD: "",
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      ARRAY_FIELD2: [],
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      ARRAY_FIELD: [],
      updatedBy: "admin",
    },
    "empty-object": {},
  }

  return invalidUpdates[type]
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): UserUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin",
  }
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(
  existingName: string,
): UserUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin",
  }
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): UserUpdateInput {
  return {
    STRING_FIELD: "Simple User", // Same as createMinimalUser
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin",
  }
}

// User for soft delete testing
export function createUserForSoftDelete(): UserCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// Update for soft deleted user testing
export function createUpdateForSoftDeleted(): UserUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin",
  }
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): UserUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin",
  }
}

// User for trimming test
export function createUserForTrimming(): UserCreateInput {
  return {
    STRING_FIELD: "Original User",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// User for active status testing
export function createActiveUser(): UserCreateInput {
  return {
    STRING_FIELD: "Active User",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin",
  }
}

// Update for status change testing
export function createStatusChangeUpdate(): UserUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin",
  }
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllUsers
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" }
}

export function createSearchByDescriptionParams() {
  return { search: "processing" }
}

export function createEmptySearchParams() {
  return { search: "" }
}

export function createWhitespaceSearchParams() {
  return { search: "   " }
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" }
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }],
  }
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }],
  }
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2,
  }
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sort: [{ field: "STRING_FIELD", direction: "ASC" as const }],
  }
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP",
  }
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true }
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" }
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" }
}

export function createUndefinedSearchParams() {
  return { search: undefined }
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// User for retry delete testing
export function createRetryDeleteUser(): UserCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin",
  }
}

// ========================================
// CREATORS FOR SPECIAL CASES (User-specific)
// ========================================

// User with special characters and unicode
export function createSpecialCharacterUser(): UserCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin",
  }
}

// User with very long ARRAY_FIELD2 (User-specific test)
export function createLongContentUser(): UserCreateInput {
  return {
    STRING_FIELD:
      "Very Long User Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2:
      "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in user STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600",
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist",
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin",
  }
}

// User with edge case ARRAY_FIELD2 (User-specific)
export function createEdgeCaseConditionsUser(): UserCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true",
    ],
    ARRAY_FIELD: ["apply_regional_users", "send_age_appropriate_ARRAY_FIELD2"],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin",
  }
}

// User with complex ARRAY_FIELD (User-specific)
export function createComplexActionsUser(): UserCreateInput {
  return {
    STRING_FIELD: "Complex Actions User",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=user.ARRAY_FIELD)",
      "analytics.track('complex_user_triggered', {user_id: this.id})",
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin",
  }
}

// User with empty optional fields (User-specific edge case)
export function createEmptyOptionalFieldsUser(): UserCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin",
  }
}

// User for testing AI-specific business logic
export function createAiLogicUser(): UserCreateInput {
  return {
    STRING_FIELD: "AI Decision User",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100",
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model",
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin",
  }
}

// Creators for delete test scenarios
export function createComplexUser(): UserCreateInput {
  return {
    STRING_FIELD: "Complex User",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["user.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin",
  }
}

export function createMinimalDeleteUser(): UserCreateInput {
  return {
    STRING_FIELD: "Minimal User",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin",
  }
}

// Users for testing deletion effects on other users
export function createUsersForDeletionTest(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "Keep This One",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Delete This One",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Keep This Too",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creators for bulk operations testing
export function createExistingUser(): UserCreateInput {
  return {
    STRING_FIELD: "Existing User",
    STRING_FIELD2: "An existing user",
    ARRAY_FIELD2: ["User says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin",
  }
}

export function createDuplicateUsersForBulk(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing User", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another user with same STRING_FIELD",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "New User",
      STRING_FIELD2: "A new user",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Users for bulk update testing
export function createUsersForBulkUpdate(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "User 1",
      STRING_FIELD2: "First user",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "User 2",
      STRING_FIELD2: "Second user",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated User 1",
      STRING_FIELD2: "Updated first user",
      updatedBy: "admin",
    },
    {
      STRING_FIELD: "Updated User 2",
      STRING_FIELD2: "Updated second user",
      updatedBy: "admin",
    },
  ]
}

// Users for bulk delete testing
export function createUsersForBulkDelete(): UserCreateInput[] {
  return [
    {
      STRING_FIELD: "User 1",
      STRING_FIELD2: "First user",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "User 2",
      STRING_FIELD2: "Second user",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "User 3",
      STRING_FIELD2: "Third user",
      ARRAY_FIELD2: ["User asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin",
    },
  ]
}

// Invalid data creators for validation tests
export function createInvalidUser(
  type:
    | "missing-STRING_FIELD"
    | "missing-ARRAY_FIELD2"
    | "missing-ARRAY_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "missing-ARRAY_FIELD2",
): any {
  const invalidUsers = {
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "John Doe",
    },
    "missing-STRING_FIELD": {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid User",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD": {
      STRING_FIELD: "Invalid User",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid User",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      STRING_FIELD: "Invalid User",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin",
    },
  }

  return invalidUsers[type]
}

// Creator for users with special characteristics
export function createUserWithWhitespace(): UserCreateInput {
  return {
    STRING_FIELD: "  Trimmed User  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createUserWithManyTags(): UserCreateInput {
  return {
    STRING_FIELD: "Multi-tag User",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin",
  }
}

export function createUserWithoutDescription(): UserCreateInput {
  return {
    STRING_FIELD: "User without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createUserWithEmptyTags(): UserCreateInput {
  return {
    STRING_FIELD: "User with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin",
  }
}

// Duplicate user creator for conflict testing
export function createDuplicateUser(): UserCreateInput {
  return {
    STRING_FIELD: "Duplicate User",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createSecondDuplicateUser(): UserCreateInput {
  return {
    STRING_FIELD: "Duplicate User", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}

// Test user with specific STRING_FIELD for soft delete tests
export function createTestUser(): UserCreateInput {
  return {
    STRING_FIELD: "Test User",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createTestUser2(): UserCreateInput {
  return {
    STRING_FIELD: "Test User",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}
