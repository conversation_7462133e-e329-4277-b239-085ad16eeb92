//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface"
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic"
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository"
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateUser,
  implHandleGetUser,
  implHandleDeleteUser,
  implHandleUpdateUser,
  implHandleGetAllUsers,
  implHandleRestoreUser,
} from "@/app/api/v1/users/impl"
import {
  createUser,
  createUserUpdate,
  createUserWithDescription,
  createTestUser,
  createTestUser2,
} from "./object_creator"

describe("User Soft Delete Tests", () => {
  let businessLogic: UserBusinessLogicInterface
  let dbRepository: TestUserDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User")
    await driver.connect()
    const originalDb = new MongoUserRepository(driver)
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver)
    businessLogic = new UserBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a users by default", async () => {
      const userData = createUserWithDescription()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // User should not be accessible by default
      const getResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(200)
      expect(getDeletedResult.body.data).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getUserCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const userData = createUserWithDescription()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
        true,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // User should not be accessible at all
      const getResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
      const getDeletedResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(404)

      // Count should be 0 in both cases
      expect(await dbRepository.getUserCount()).toBe(0)
      expect(await dbRepository.getUserCount(true)).toBe(0)
    })

    it("should not include soft deleted users in getAll by default", async () => {
      const userData1 = createUser(1)
      const userData2 = createUser(2)

      const createResult1 = await implHandleCreateUser(userData1, businessLogic)
      const createResult2 = await implHandleCreateUser(userData2, businessLogic)
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one users
      const deleteResult = await implHandleDeleteUser(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllUsers(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(1)
      expect(result.body.data?.total).toBe(1)
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id)
    })

    it("should include soft deleted users when specified", async () => {
      const userData1 = createUser(1)
      const userData2 = createUser(2)

      const createResult1 = await implHandleCreateUser(userData1, businessLogic)
      const createResult2 = await implHandleCreateUser(userData2, businessLogic)
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one users
      const deleteResult = await implHandleDeleteUser(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllUsers(businessLogic, {
        includeDeleted: true,
      })

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(2)

      const deletedUser = result.body.data?.items.find(
        (c: any) => c.id === createResult1.body.data.id,
      )
      expect(deletedUser).toBeDefined()
      expect(deletedUser?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted users", async () => {
      const userData = createUser(3)
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const userUpdate = createUserUpdate(1)

      const result = await implHandleUpdateUser(
        createResult.body.data.id,
        userUpdate,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should not include soft deleted users in search", async () => {
      const userData1 = createUser(3) // "Test User"
      const createResult = await implHandleCreateUser(userData1, businessLogic)
      expect(createResult.status).toBe(201)

      // Soft delete the user
      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Search should not find the soft deleted user
      const searchResult = await implHandleGetAllUsers(businessLogic, {
        search: "Test",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted users", async () => {
      const userData = createUserWithDescription()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      // Soft delete the users
      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // Restore the users
      const restoreResult = await implHandleRestoreUser(
        createResult.body.data.id,
        businessLogic,
      )

      expect(restoreResult.status).toBe(200)
      expect(restoreResult.body.status).toBe("success")

      // User should be accessible again
      const restoredResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoredResult.status).toBe(200)
      expect(restoredResult.body.data?.deletedAt).toBeUndefined()

      // Count should include the restored users
      expect(await dbRepository.getUserCount()).toBe(1)
    })

    it("should fail to restore a non-existent users", async () => {
      const restoreResult = await implHandleRestoreUser(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a users that was never deleted", async () => {
      const userData = createUser(3)
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const restoreResult = await implHandleRestoreUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a hard deleted users", async () => {
      const userData = createUser(3)
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      // Hard delete the users
      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail with empty users ID", async () => {
      const restoreResult = await implHandleRestoreUser("", businessLogic)
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("User ID is required")
    })

    it("should fail with whitespace-only users ID", async () => {
      const restoreResult = await implHandleRestoreUser("   ", businessLogic)
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("User ID is required")
    })

    it("should update updatedAt when restoring", async () => {
      const userData = createUser(3)
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const originalUpdatedAt = createResult.body.data.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      const deleteResult = await implHandleDeleteUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(200)

      const getResult = await implHandleGetUser(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(200)
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating users with STRING_FIELD of soft deleted users", async () => {
      // Create and soft delete a users
      const userData1 = createTestUser()
      const createResult1 = await implHandleCreateUser(userData1, businessLogic)
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteUser(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Should be able to create new users with same STRING_FIELD
      const userData2 = createTestUser2()
      const createResult2 = await implHandleCreateUser(userData2, businessLogic)

      expect(createResult2.status).toBe(201)
      expect(createResult2.body.data.STRING_FIELD).toBe(userData2.STRING_FIELD)
      expect(await dbRepository.getUserCount()).toBe(1)
    })

    it("should prevent creating users with STRING_FIELD of active users", async () => {
      const userData1 = createTestUser()
      const createResult1 = await implHandleCreateUser(userData1, businessLogic)
      expect(createResult1.status).toBe(201)

      const userData2 = createTestUser2()
      const createResult2 = await implHandleCreateUser(userData2, businessLogic)

      expect(createResult2.status).toBe(409)
      expect(createResult2.body.status).toBe("failed")
      expect(createResult2.body.error).toContain(
        "User with the same STRING_FIELD already exists",
      )
    })

    it("should prevent restoring users if STRING_FIELD is now taken", async () => {
      // Create and soft delete a users
      const userData1 = createTestUser()
      const createResult1 = await implHandleCreateUser(userData1, businessLogic)
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteUser(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Create new users with same STRING_FIELD
      const userData2 = createTestUser2()
      const createResult2 = await implHandleCreateUser(userData2, businessLogic)
      expect(createResult2.status).toBe(201)

      // Should not be able to restore the first users
      const restoreResult = await implHandleRestoreUser(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })
  })
})
