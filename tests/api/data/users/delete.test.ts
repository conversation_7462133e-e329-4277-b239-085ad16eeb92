// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface"
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic"
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository"
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateUser,
  implHandleGetUser,
  implHandleDeleteUser,
} from "@/app/api/v1/users/impl"
import {
  createUser,
  createSimpleUsers,
  createComplexUser,
  createMinimalDeleteUser,
  createUsersForDeletionTest,
  createRetryDeleteUser,
} from "./object_creator"

describe("Delete User API Tests", () => {
  let businessLogic: UserBusinessLogicInterface
  let dbRepository: TestUserDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User")
    await driver.connect()
    const originalDb = new MongoUserRepository(driver)
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver)
    businessLogic = new UserBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/users/:id", () => {
    it("should successfully delete an existing user", async () => {
      const usersData = createUser(5) // John Doe User
      const createResult = await implHandleCreateUser(usersData, businessLogic)
      const usersId = createResult.body.data.id

      const getResult = await implHandleGetUser(usersId, businessLogic)
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteUser(usersId, businessLogic)

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe("User deleted successfully")

      const getAfterDelete = await implHandleGetUser(usersId, businessLogic)
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify user count decreases after deletion", async () => {
      const usersData = createSimpleUsers()

      const usersIds: string[] = []
      for (const data of usersData) {
        const result = await implHandleCreateUser(data, businessLogic)
        usersIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getUserCount()).toBe(3)

      const deleteResult = await implHandleDeleteUser(
        usersIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getUserCount()).toBe(2)

      const getDeleted = await implHandleGetUser(usersIds[1], businessLogic)
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent user", async () => {
      const result = await implHandleDeleteUser(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User not found")
    })

    it("should fail with empty user ID", async () => {
      const result = await implHandleDeleteUser("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const usersData = createComplexUser()
      const result = await implHandleCreateUser(usersData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteUser(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetUser(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const usersData = createMinimalDeleteUser()
      const result = await implHandleCreateUser(usersData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteUser(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetUser(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple users", async () => {
      const usersData = createSimpleUsers()

      const ids: string[] = []
      for (const data of usersData) {
        const res = await implHandleCreateUser(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteUser(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getUserCount()).toBe(0)
    })

    it("should not affect other users when deleting one", async () => {
      const usersData = createUsersForDeletionTest()

      const ids: string[] = []
      for (const data of usersData) {
        const res = await implHandleCreateUser(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteUser(ids[1], businessLogic)

      const getDeleted = await implHandleGetUser(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetUser(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetUser(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getUserCount()).toBe(2)
    })

    it("should handle attempting to delete the same user twice", async () => {
      const usersData = createRetryDeleteUser()
      const result = await implHandleCreateUser(usersData, businessLogic)
      const id = result.body.data?.id

      const first = await implHandleDeleteUser(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteUser(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
