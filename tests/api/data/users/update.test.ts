//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface"
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic"
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository"
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateUser,
  implHandleUpdateUser,
  implHandleDeleteUser,
} from "@/app/api/v1/users/impl"
import {
  createFullUser,
  createMinimalUser,
  createFullUserUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createUserForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createUserForTrimming,
  createActiveUser,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update User API Tests", () => {
  let businessLogic: UserBusinessLogicInterface
  let dbRepository: TestUserDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User")
    await driver.connect()
    const originalDb = new MongoUserRepository(driver)
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver)
    businessLogic = new UserBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/users/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createFullUserUpdate()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(updateData.variables)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(createData.variables)
    })

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
    })

    it("should fail to update non-existent user", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateUser(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User not found")
    })

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-STRING_FIELD")
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-ARRAY_FIELD")
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateUser("", updateData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User ID is required")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first user
      const createData1 = createMinimalUser()
      await implHandleCreateUser(createData1, businessLogic)

      // Create second user
      const createData2 = createFullUser()
      const createResult2 = await implHandleCreateUser(
        createData2,
        businessLogic,
      )

      // Try to update second user with first user's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD)
      const result = await implHandleUpdateUser(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another User with this STRING_FIELD exists",
      )
    })

    it("should allow updating user with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
    })

    it("should fail to update soft-deleted user", async () => {
      const createData = createUserForSoftDelete()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      // Soft delete the user
      const deleteResult = await implHandleDeleteUser(usersId, businessLogic)
      expect(deleteResult.status).toBe(200)

      // Try to update the soft-deleted user
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("User not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createUserForTrimming()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description")
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"])
      expect(result.body.data?.variables).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-variables")
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveUser()
      const createResult = await implHandleCreateUser(createData, businessLogic)
      const usersId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateUser(
        usersId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
