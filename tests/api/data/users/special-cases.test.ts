//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface"
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic"
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository"
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateUser,
  implHandleGetUser,
  implHandleUpdateUser,
  implHandleDeleteUser,
} from "@/app/api/v1/users/impl"
import {
  createSpecialCharacterUser,
  createLongContentUser,
  createEdgeCaseConditionsUser,
  createComplexActionsUser,
  createEmptyOptionalFieldsUser,
  createAiLogicUser,
} from "./object_creator"

/**
 * Special Cases Tests for Users
 *
 * This file contains tests for User-specific functionality that doesn't exist
 * in other features like Users or Users. These tests focus on:
 * - AI-specific business logic (ARRAY_FIELD, variables, AI decision making)
 * - Complex user processing scenarios
 * - Edge cases unique to user engines
 * - Special character and unicode handling in user contexts
 * - Performance and limits testing for user ARRAY_FIELD2
 *
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting User-specific complexity.
 */

describe("User Special Cases Tests", () => {
  let businessLogic: UserBusinessLogicInterface
  let dbRepository: TestUserDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User")
    await driver.connect()
    const originalDb = new MongoUserRepository(driver)
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver)
    businessLogic = new UserBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const userData = createSpecialCharacterUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.STRING_FIELD).toBe(userData.STRING_FIELD)
      expect(createResult.body.data?.ARRAY_FIELD2).toBe(userData.ARRAY_FIELD2)
      expect(createResult.body.data?.tags).toContain("🚀")
      expect(createResult.body.data?.tags).toContain("Test@Tag")
    })

    it("should search users with special characters", async () => {
      const userData = createSpecialCharacterUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllUsers(businessLogic, {
        search: "José",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].STRING_FIELD).toBe("José María O'Connor")
    })

    it("should update users with special characters", async () => {
      const userData = createSpecialCharacterUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      const userId = createResult.body.data.id

      const updateResult = await implHandleUpdateUser(
        userId,
        {
          STRING_FIELD: "Updated José María 🎯",
          ARRAY_FIELD2: "Updated with more symbols ⭐ & emojis 🚀",
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.STRING_FIELD).toBe("Updated José María 🎯")
      expect(updateResult.body.data?.ARRAY_FIELD2).toBe(
        "Updated with more symbols ⭐ & emojis 🚀",
      )
    })

    it("should delete users with special characters", async () => {
      const userData = createSpecialCharacterUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      const userId = createResult.body.data.id

      const deleteResult = await implHandleDeleteUser(userId, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetUser(userId, businessLogic)
      expect(getResult.status).toBe(404)
    })
  })

  describe("Content Length and Performance", () => {
    it("should handle very long ARRAY_FIELD2 in all fields", async () => {
      const userData = createLongContentUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.STRING_FIELD.length).toBeGreaterThan(50)
      expect(createResult.body.data?.ARRAY_FIELD2.length).toBeGreaterThan(200)
      expect(createResult.body.data?.ARRAY_FIELD.length).toBe(3)
      expect(createResult.body.data?.variables.length).toBe(4)
    })

    it("should search through long ARRAY_FIELD2 efficiently", async () => {
      const userData = createLongContentUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllUsers(businessLogic, {
        search: "extensive",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].ARRAY_FIELD2).toContain("extensive")
    })
  })

  describe("Complex User Logic (User-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const userData = createEdgeCaseConditionsUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "user.age >= 18 && user.age <= 65",
      )
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "user.location.country === 'US' || user.location.country === 'CA'",
      )
    })

    it("should handle complex action definitions", async () => {
      const userData = createComplexActionsUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.variables).toContain(
        "webhook.call('https://api.example.com/notify')",
      )
      expect(createResult.body.data?.variables).toContain(
        "database.update('user_stats', {last_interaction: now()})",
      )
    })

    it("should handle AI-specific business logic", async () => {
      const userData = createAiLogicUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "ai.confidence > 0.8",
      )
      expect(createResult.body.data?.variables).toContain(
        "ai.respond_with_confidence",
      )
      expect(createResult.body.data?.tags).toContain("AI")
      expect(createResult.body.data?.tags).toContain("MachineLearning")
    })
  })

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const userData = createEmptyOptionalFieldsUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD2).toBe("")
      expect(createResult.body.data?.tags).toEqual([])
    })

    it("should validate user activation logic", async () => {
      const userData = createAiLogicUser()
      userData.isActive = false

      const createResult = await implHandleCreateUser(userData, businessLogic)
      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.isActive).toBe(false)

      // Test activation toggle
      const updateResult = await implHandleUpdateUser(
        createResult.body.data.id,
        {
          isActive: true,
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.isActive).toBe(true)
    })

    it("should handle user priority and execution order concepts", async () => {
      // This test demonstrates User-specific concepts that don't exist in Users/Users
      const users = [
        createAiLogicUser(),
        createEdgeCaseConditionsUser(),
        createComplexActionsUser(),
      ]

      const createdUsers = []
      for (const user of users) {
        const result = await implHandleCreateUser(user, businessLogic)
        createdUsers.push(result.body.data)
      }

      expect(createdUsers).toHaveLength(3)

      // Verify all users are created with proper timestamps for execution order
      for (let i = 1; i < createdUsers.length; i++) {
        expect(
          new Date(createdUsers[i].createdAt).getTime(),
        ).toBeGreaterThanOrEqual(
          new Date(createdUsers[i - 1].createdAt).getTime(),
        )
      }
    })
  })

  describe("User Engine Specific Functionality", () => {
    it("should handle user condition parsing and validation", async () => {
      const userData = createEdgeCaseConditionsUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)

      // Test that ARRAY_FIELD are stored as-is for later parsing by user engine
      const ARRAY_FIELD = createResult.body.data?.ARRAY_FIELD
      expect(ARRAY_FIELD).toBeDefined()
      expect(ARRAY_FIELD?.some((c) => c.includes("&&"))).toBe(true)
      expect(ARRAY_FIELD?.some((c) => c.includes("||"))).toBe(true)
    })

    it("should handle action execution metadata", async () => {
      const userData = createComplexActionsUser()
      const createResult = await implHandleCreateUser(userData, businessLogic)

      expect(createResult.status).toBe(201)

      // Test that variables contain execution metadata
      const variables = createResult.body.data?.variables
      expect(variables?.some((a) => a.includes("webhook.call"))).toBe(true)
      expect(variables?.some((a) => a.includes("database.update"))).toBe(true)
      expect(variables?.some((a) => a.includes("ARRAY_FIELD.send"))).toBe(true)
    })
  })
})
