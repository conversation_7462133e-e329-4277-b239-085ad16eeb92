//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic"
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository"
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateContact,
  implHandleGetContact,
  implHandleUpdateContact,
  implHandleDeleteContact,
} from "@/app/api/v1/contacts/impl"
import {
  createSpecialCharacterContact,
  createLongContentContact,
  createEdgeCaseConditionsContact,
  createComplexActionsContact,
  createEmptyOptionalFieldsContact,
  createAiLogicContact,
} from "./object_creator"

/**
 * Special Cases Tests for Contacts
 *
 * This file contains tests for Contact-specific functionality that doesn't exist
 * in other features like Contacts or MessageTemplates. These tests focus on:
 * - AI-specific business logic (phone, email, AI decision making)
 * - Complex contact processing scenarios
 * - Edge cases unique to contact engines
 * - Special character and unicode handling in contact contexts
 * - Performance and limits testing for contact content
 *
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting Contact-specific complexity.
 */

describe("Contact Special Cases Tests", () => {
  let businessLogic: ContactBusinessLogicInterface
  let dbRepository: TestContactDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact")
    await driver.connect()
    const originalDb = new MongoContactRepository(driver)
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ContactBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const contactData = createSpecialCharacterContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.name).toBe(contactData.name)
      expect(createResult.body.data?.description).toBe(contactData.description)
      expect(createResult.body.data?.tags).toContain("🚀")
      expect(createResult.body.data?.tags).toContain("Test@Tag")
    })

    it("should search contacts with special characters", async () => {
      const contactData = createSpecialCharacterContact()
      await implHandleCreateContact(contactData, businessLogic)

      const searchResult = await businessLogic.search("José")
      expect(searchResult).toHaveLength(1)
      expect(searchResult[0].name).toBe("José María O'Connor")
    })

    it("should update contacts with special characters", async () => {
      const contactData = createSpecialCharacterContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )
      const contactId = createResult.body.data.id

      const updateResult = await implHandleUpdateContact(
        contactId,
        {
          name: "Updated José María 🎯",
          description: "Updated with more symbols ⭐ & emojis 🚀",
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.name).toBe("Updated José María 🎯")
      expect(updateResult.body.data?.description).toBe(
        "Updated with more symbols ⭐ & emojis 🚀",
      )
    })

    it("should delete contacts with special characters", async () => {
      const contactData = createSpecialCharacterContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )
      const contactId = createResult.body.data.id

      const deleteResult = await implHandleDeleteContact(
        contactId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetContact(contactId, businessLogic)
      expect(getResult.status).toBe(404)
    })
  })

  describe("Content Length and Performance", () => {
    it("should handle very long content in all fields", async () => {
      const contactData = createLongContentContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.name.length).toBeGreaterThan(50)
      expect(createResult.body.data?.description.length).toBeGreaterThan(200)
      expect(createResult.body.data?.phone.length).toBe(3)
      expect(createResult.body.data?.email.length).toBe(4)
    })

    it("should search through long content efficiently", async () => {
      const contactData = createLongContentContact()
      await implHandleCreateContact(contactData, businessLogic)

      const searchResult = await businessLogic.search("extensive")
      expect(searchResult).toHaveLength(1)
      expect(searchResult[0].description).toContain("extensive")
    })
  })

  describe("Complex Contact Logic (Contact-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const contactData = createEdgeCaseConditionsContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.phone).toContain(
        "user.age >= 18 && user.age <= 65",
      )
      expect(createResult.body.data?.phone).toContain(
        "user.location.country === 'US' || user.location.country === 'CA'",
      )
    })

    it("should handle complex action definitions", async () => {
      const contactData = createComplexActionsContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.email).toContain(
        "webhook.call('https://api.example.com/notify')",
      )
      expect(createResult.body.data?.email).toContain(
        "database.update('user_stats', {last_interaction: now()})",
      )
    })

    it("should handle AI-specific business logic", async () => {
      const contactData = createAiLogicContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.phone).toContain("ai.confidence > 0.8")
      expect(createResult.body.data?.email).toContain(
        "ai.respond_with_confidence",
      )
      expect(createResult.body.data?.tags).toContain("AI")
      expect(createResult.body.data?.tags).toContain("MachineLearning")
    })
  })

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const contactData = createEmptyOptionalFieldsContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.description).toBe("")
      expect(createResult.body.data?.tags).toEqual([])
    })

    it("should validate contact activation logic", async () => {
      const contactData = createAiLogicContact()
      contactData.isActive = false

      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.isActive).toBe(false)

      // Test activation toggle
      const updateResult = await implHandleUpdateContact(
        createResult.body.data.id,
        {
          isActive: true,
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.isActive).toBe(true)
    })

    it("should handle contact priority and execution order concepts", async () => {
      // This test demonstrates Contact-specific concepts that don't exist in Contacts/MessageTemplates
      const contacts = [
        createAiLogicContact(),
        createEdgeCaseConditionsContact(),
        createComplexActionsContact(),
      ]

      const createdContacts = []
      for (const contact of contacts) {
        const result = await implHandleCreateContact(contact, businessLogic)
        createdContacts.push(result.body.data)
      }

      expect(createdContacts).toHaveLength(3)

      // Verify all contacts are created with proper timestamps for execution order
      for (let i = 1; i < createdContacts.length; i++) {
        expect(
          new Date(createdContacts[i].createdAt).getTime(),
        ).toBeGreaterThanOrEqual(
          new Date(createdContacts[i - 1].createdAt).getTime(),
        )
      }
    })
  })

  describe("Contact Engine Specific Functionality", () => {
    it("should handle contact condition parsing and validation", async () => {
      const contactData = createEdgeCaseConditionsContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that phone are stored as-is for later parsing by contact engine
      const phone = createResult.body.data?.phone
      expect(phone).toBeDefined()
      expect(phone?.some((c) => c.includes("&&"))).toBe(true)
      expect(phone?.some((c) => c.includes("||"))).toBe(true)
    })

    it("should handle action execution metadata", async () => {
      const contactData = createComplexActionsContact()
      const createResult = await implHandleCreateContact(
        contactData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that email contain execution metadata
      const email = createResult.body.data?.email
      expect(email?.some((a) => a.includes("webhook.call"))).toBe(true)
      expect(email?.some((a) => a.includes("database.update"))).toBe(true)
      expect(email?.some((a) => a.includes("email.send"))).toBe(true)
    })
  })
})
