//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic"
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository"
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  createContact,
  createContactUpdate,
  createContactWithDescription,
  createTestContact,
  createTestContact2,
} from "./object_creator"

describe("Contact Soft Delete Tests", () => {
  let businessLogic: ContactBusinessLogicInterface
  let dbRepository: TestContactDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact")
    await driver.connect()
    const originalDb = new MongoContactRepository(driver)
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ContactBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a contacts by default", async () => {
      const contactData = createContactWithDescription()
      const contacts = await businessLogic.create(contactData)

      const deleted = await businessLogic.delete(contacts.id)

      expect(deleted).toBe(true)

      // Contact should not be accessible by default
      expect(await businessLogic.getById(contacts.id)).toBeNull()

      // But should be accessible when including deleted
      expect(await businessLogic.getById(contacts.id, true)).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getContactCount()).toBe(0)
      expect(await dbRepository.getContactCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const contactData = createContactWithDescription()
      const contacts = await businessLogic.create(contactData)

      const deleted = await businessLogic.delete(contacts.id, true)

      expect(deleted).toBe(true)

      // Contact should not be accessible at all
      expect(await businessLogic.getById(contacts.id)).toBeNull()
      expect(await businessLogic.getById(contacts.id, true)).toBeNull()

      // Count should be 0 in both cases
      expect(await dbRepository.getContactCount()).toBe(0)
      expect(await dbRepository.getContactCount(true)).toBe(0)
    })

    it("should not include soft deleted contacts in getAll by default", async () => {
      const contactData1 = createContact(1)
      const contactData2 = createContact(2)

      const contacts1 = await businessLogic.create(contactData1)
      const contacts2 = await businessLogic.create(contactData2)

      // Soft delete one contacts
      await businessLogic.delete(contacts1.id)

      const result = await businessLogic.getAll()

      expect(result.items).toHaveLength(1)
      expect(result.total).toBe(1)
      expect(result.items[0].id).toBe(contacts2.id)
    })

    it("should include soft deleted contacts when specified", async () => {
      const contactData1 = createContact(1)
      const contactData2 = createContact(2)

      const contacts1 = await businessLogic.create(contactData1)
      const contacts2 = await businessLogic.create(contactData2)

      // Soft delete one contacts
      await businessLogic.delete(contacts1.id)

      const result = await businessLogic.getAll({ includeDeleted: true })

      expect(result.items).toHaveLength(2)
      expect(result.total).toBe(2)

      const deletedContact = result.items.find((c) => c.id === contacts1.id)
      expect(deletedContact).not.toBeNull()
      expect(deletedContact?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted contacts", async () => {
      const contactData = createContact(3)
      const contacts = await businessLogic.create(contactData)

      await businessLogic.delete(contacts.id)

      const contactUpdate = createContactUpdate(1)

      const result = await businessLogic.update(contacts.id, contactUpdate)

      await expect(result).toBeNull()
    })

    it("should not include soft deleted contacts in search", async () => {
      const contactData1 = createContact(3) // "Test Contact"
      const contacts1 = await businessLogic.create(contactData1)

      // Soft delete the contact
      await businessLogic.delete(contacts1.id)

      // Search should not find the soft deleted contact
      const searchResults = await businessLogic.search("Test")
      expect(searchResults).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted contacts", async () => {
      const contactData = createContactWithDescription()
      const contacts = await businessLogic.create(contactData)

      // Soft delete the contacts
      await businessLogic.delete(contacts.id)
      expect(await businessLogic.getById(contacts.id)).toBeNull()

      // Restore the contacts
      const restored = await businessLogic.restore(contacts.id)

      expect(restored).toBe(true)

      // Contact should be accessible again
      const restoredContact = await businessLogic.getById(contacts.id)
      expect(restoredContact).not.toBeNull()
      expect(restoredContact?.deletedAt).toBeUndefined()

      // Count should include the restored contacts
      expect(await dbRepository.getContactCount()).toBe(1)
    })

    it("should fail to restore a non-existent contacts", async () => {
      const restored = await businessLogic.restore("507f1f77bcf86cd799439011")
      expect(restored).toBe(false)
    })

    it("should fail to restore a contacts that was never deleted", async () => {
      const contactData = createContact(3)
      const contacts = await businessLogic.create(contactData)

      const restored = await businessLogic.restore(contacts.id)
      expect(restored).toBe(false)
    })

    it("should fail to restore a hard deleted contacts", async () => {
      const contactData = createContact(3)
      const contacts = await businessLogic.create(contactData)

      // Hard delete the contacts
      await businessLogic.delete(contacts.id, true)

      const restored = await businessLogic.restore(contacts.id)
      expect(restored).toBe(false)
    })

    it("should fail with empty contacts ID", async () => {
      await expect(businessLogic.restore("")).rejects.toThrow(
        "Contact ID is required",
      )
    })

    it("should fail with whitespace-only contacts ID", async () => {
      await expect(businessLogic.restore("   ")).rejects.toThrow(
        "Contact ID is required",
      )
    })

    it("should update updatedAt when restoring", async () => {
      const contactData = createContact(3)
      const contacts = await businessLogic.create(contactData)

      const originalUpdatedAt = contacts.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      await businessLogic.delete(contacts.id)
      await businessLogic.restore(contacts.id)

      const restoredContact = await businessLogic.getById(contacts.id)
      expect(restoredContact?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating contacts with name of soft deleted contacts", async () => {
      // Create and soft delete a contacts
      const contactData1 = createTestContact()
      const contacts1 = await businessLogic.create(contactData1)
      await businessLogic.delete(contacts1.id)

      // Should be able to create new contacts with same name
      const contactData2 = createTestContact2()
      const contacts2 = await businessLogic.create(contactData2)

      expect(contacts2.name).toBe(contactData2.name)
      expect(await dbRepository.getContactCount()).toBe(1)
    })

    it("should prevent creating contacts with name of active contacts", async () => {
      const contactData1 = createTestContact()
      await businessLogic.create(contactData1)

      const contactData2 = createTestContact2()
      await expect(businessLogic.create(contactData2)).rejects.toThrow(
        "Contact with the same name already exists",
      )
    })

    it("should prevent restoring contacts if name is now taken", async () => {
      // Create and soft delete a contacts
      const contactData1 = createTestContact()
      const contacts1 = await businessLogic.create(contactData1)
      await businessLogic.delete(contacts1.id)

      // Create new contacts with same name
      const contactData2 = createTestContact2()
      await businessLogic.create(contactData2)

      // Should not be able to restore the first contacts
      const restored = await businessLogic.restore(contacts1.id)
      expect(restored).toBe(false)
    })
  })
})
