import {
  ContactCreateInput,
  ContactUpdateInput,
} from "@/lib/repositories/contacts/interface"

/**
 * Factory functions for creating test Contact objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createContact(variant: number): ContactCreateInput {
  const baseContacts: Record<number, ContactCreateInput> = {
    1: {
      name: "Customer Support Contact",
      description: "Contact for handling customer support requests",
      phone: [
        "user_message_contains('help')",
        "time_between('09:00', '17:00')",
      ],
      email: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin",
    },
    2: {
      name: "Simple Contact",
      phone: ["always_true"],
      email: ["log_message"],
      createdBy: "admin",
    },
    3: {
      name: "Test Contact",
      description: "A test contact with description",
      phone: ["test_condition"],
      email: ["test_action"],
      createdBy: "admin",
    },
    4: {
      name: "Tagged Contact",
      phone: ["test_condition"],
      email: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin",
    },
    5: {
      name: "<PERSON> Doe Contact",
      description: "Contact for <PERSON>e processing",
      phone: ["user_name_contains('john')", "time_between('09:00', '17:00')"],
      email: ["assign_to_support", "send_email"],
      tags: ["Customer", "VIP"],
      createdBy: "admin",
    },
    6: {
      name: "Jane Smith Contact",
      description: "Contact for Jane Smith processing",
      phone: ["user_name_contains('jane')", "priority_high"],
      email: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin",
    },
    7: {
      name: "Bob Johnson Contact",
      description: "Contact for Bob Johnson processing",
      phone: ["user_name_contains('bob')", "vip_customer"],
      email: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin",
    },
    8: {
      name: "Alice Brown Contact",
      description: "Contact for Alice Brown processing",
      phone: ["user_name_contains('alice')", "lead_qualification"],
      email: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin",
    },
  }

  if (!baseContacts[variant]) {
    throw new Error(
      `Contact variant ${variant} not found. Available variants: ${Object.keys(baseContacts).join(", ")}`,
    )
  }

  return { ...baseContacts[variant] }
}

// Specialized creator functions for specific test scenarios
export function createMinimalContact(): ContactCreateInput {
  return createContact(2)
}

export function createFullContact(): ContactCreateInput {
  return createContact(1)
}

export function createContactWithDescription(): ContactCreateInput {
  return createContact(3)
}

export function createContactWithTags(): ContactCreateInput {
  return createContact(4)
}

// Creator for multiple contacts (useful for bulk operations and search tests)
export function createMultipleContacts(): ContactCreateInput[] {
  return [
    createContact(5), // John Doe Contact
    createContact(6), // Jane Smith Contact
    createContact(7), // Bob Johnson Contact
    createContact(8), // Alice Brown Contact
  ]
}

// Creator for simple test contacts (useful for basic CRUD operations)
export function createSimpleContacts(): ContactCreateInput[] {
  return [
    { name: "A", phone: ["1"], email: ["a"], createdBy: "admin" },
    { name: "B", phone: ["2"], email: ["b"], createdBy: "admin" },
    { name: "C", phone: ["3"], email: ["c"], createdBy: "admin" },
  ]
}

// Creator for contacts with specific tags (useful for filtering tests)
export function createContactsWithTags(): ContactCreateInput[] {
  return [
    {
      name: "John Doe",
      phone: ["x"],
      email: ["a"],
      createdBy: "admin",
      tags: ["Customer", "VIP"],
    },
    {
      name: "Jane Smith",
      phone: ["y"],
      email: ["b"],
      createdBy: "admin",
      tags: ["Lead", "Potential"],
    },
    {
      name: "Bob Johnson",
      phone: ["z"],
      email: ["c"],
      createdBy: "admin",
      tags: ["Customer"],
    },
    {
      name: "Alice Brown",
      phone: ["a"],
      email: ["d"],
      createdBy: "admin",
      tags: ["VIP"],
    },
  ]
}

// Update data creators
export function createContactUpdate(variant: number): ContactUpdateInput {
  const baseUpdates: Record<number, ContactUpdateInput> = {
    1: {
      name: "Updated Contact",
      description: "Updated description",
      phone: ["updated_condition"],
      email: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin",
    },
    2: {
      name: "New Name",
      updatedBy: "admin",
    },
    3: {
      description: "Updated description only",
      updatedBy: "admin",
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin",
    },
    5: {
      isActive: false,
      updatedBy: "admin",
    },
  }

  if (!baseUpdates[variant]) {
    throw new Error(
      `Contact update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(", ")}`,
    )
  }

  return { ...baseUpdates[variant] }
}

// Specialized update creators
export function createFullContactUpdate(): ContactUpdateInput {
  return createContactUpdate(1)
}

export function createNameOnlyUpdate(): ContactUpdateInput {
  return createContactUpdate(2)
}

export function createDescriptionOnlyUpdate(): ContactUpdateInput {
  return createContactUpdate(3)
}

export function createTagsOnlyUpdate(): ContactUpdateInput {
  return createContactUpdate(4)
}

export function createStatusOnlyUpdate(): ContactUpdateInput {
  return createContactUpdate(5)
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(
  type: "empty-name" | "empty-phone" | "empty-email" | "empty-object",
): any {
  const invalidUpdates = {
    "empty-name": {
      name: "",
      updatedBy: "admin",
    },
    "empty-phone": {
      phone: [],
      updatedBy: "admin",
    },
    "empty-email": {
      email: [],
      updatedBy: "admin",
    },
    "empty-object": {},
  }

  return invalidUpdates[type]
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): ContactUpdateInput {
  return {
    name: "   Trimmed Name   ",
    updatedBy: "admin",
  }
}

// Update for duplicate name testing
export function createDuplicateNameUpdate(
  existingName: string,
): ContactUpdateInput {
  return {
    name: existingName,
    updatedBy: "admin",
  }
}

// Update with same name (no change scenario)
export function createSameNameUpdate(): ContactUpdateInput {
  return {
    name: "Simple Contact", // Same as createMinimalContact
    description: "Updated description",
    updatedBy: "admin",
  }
}

// Contact for soft delete testing
export function createContactForSoftDelete(): ContactCreateInput {
  return {
    name: "To Be Deleted",
    phone: ["cond"],
    email: ["act"],
    createdBy: "admin",
  }
}

// Update for soft deleted contact testing
export function createUpdateForSoftDeleted(): ContactUpdateInput {
  return {
    name: "Should Not Work",
    updatedBy: "admin",
  }
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): ContactUpdateInput {
  return {
    name: "   Trimmed Name   ",
    description: "   Trimmed Description   ",
    phone: ["   trimmed_condition   "],
    email: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin",
  }
}

// Contact for trimming test
export function createContactForTrimming(): ContactCreateInput {
  return {
    name: "Original Contact",
    phone: ["cond"],
    email: ["act"],
    createdBy: "admin",
  }
}

// Contact for active status testing
export function createActiveContact(): ContactCreateInput {
  return {
    name: "Active Contact",
    phone: ["cond"],
    email: ["act"],
    isActive: true,
    createdBy: "admin",
  }
}

// Update for status change testing
export function createStatusChangeUpdate(): ContactUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin",
  }
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllContacts
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" }
}

export function createSearchByDescriptionParams() {
  return { search: "processing" }
}

export function createEmptySearchParams() {
  return { search: "" }
}

export function createWhitespaceSearchParams() {
  return { search: "   " }
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" }
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }],
  }
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }],
  }
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2,
  }
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sort: [{ field: "name", direction: "ASC" as const }],
  }
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP",
  }
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true }
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" }
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" }
}

export function createUndefinedSearchParams() {
  return { search: undefined }
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Contact for retry delete testing
export function createRetryDeleteContact(): ContactCreateInput {
  return {
    name: "Retry Delete",
    phone: ["attempt"],
    email: ["log"],
    createdBy: "admin",
  }
}

// ========================================
// CREATORS FOR SPECIAL CASES (Contact-specific)
// ========================================

// Contact with special characters and unicode
export function createSpecialCharacterContact(): ContactCreateInput {
  return {
    name: "José María O'Connor",
    description: "Handles unicode 🎉 & symbols",
    phone: ["name.includes('José')"],
    email: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin",
  }
}

// Contact with very long content (Contact-specific test)
export function createLongContentContact(): ContactCreateInput {
  return {
    name: "Very Long Contact Name That Exceeds Normal Length Expectations And Tests System Limits",
    description:
      "This is a very long description that tests how the system handles extensive text content in contact descriptions. It includes multiple sentences and should test the limits of what the system can handle in terms of content length and processing.",
    phone: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600",
    ],
    email: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist",
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin",
  }
}

// Contact with edge case phone (Contact-specific)
export function createEdgeCaseConditionsContact(): ContactCreateInput {
  return {
    name: "Edge Case Conditions",
    description: "Tests complex condition parsing",
    phone: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true",
    ],
    email: ["apply_regional_contacts", "send_age_appropriate_content"],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin",
  }
}

// Contact with complex email (Contact-specific)
export function createComplexActionsContact(): ContactCreateInput {
  return {
    name: "Complex Actions Contact",
    description: "Tests complex action execution",
    phone: ["trigger_complex_workflow"],
    email: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "email.send(template='complex_notification', to=user.email)",
      "analytics.track('complex_contact_triggered', {contact_id: this.id})",
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin",
  }
}

// Contact with empty optional fields (Contact-specific edge case)
export function createEmptyOptionalFieldsContact(): ContactCreateInput {
  return {
    name: "Empty Optional Fields",
    phone: ["basic_condition"],
    email: ["basic_action"],
    description: "",
    tags: [],
    createdBy: "admin",
  }
}

// Contact for testing AI-specific business logic
export function createAiLogicContact(): ContactCreateInput {
  return {
    name: "AI Decision Contact",
    description: "Tests AI-specific decision making logic",
    phone: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100",
    ],
    email: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model",
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin",
  }
}

// Creators for delete test scenarios
export function createComplexContact(): ContactCreateInput {
  return {
    name: "Complex Contact",
    description: "Full field test",
    phone: ["user.role == 'admin'"],
    email: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin",
  }
}

export function createMinimalDeleteContact(): ContactCreateInput {
  return {
    name: "Minimal Contact",
    phone: ["is.loggedIn"],
    email: ["alert"],
    createdBy: "admin",
  }
}

// Contacts for testing deletion effects on other contacts
export function createContactsForDeletionTest(): ContactCreateInput[] {
  return [
    { name: "Keep This One", phone: ["x"], email: ["a"], createdBy: "admin" },
    { name: "Delete This One", phone: ["y"], email: ["b"], createdBy: "admin" },
    { name: "Keep This Too", phone: ["z"], email: ["c"], createdBy: "admin" },
  ]
}

// Creators for bulk operations testing
export function createExistingContact(): ContactCreateInput {
  return {
    name: "Existing Contact",
    description: "An existing contact",
    phone: ["User says test"],
    email: ["Show test response"],
    createdBy: "admin",
  }
}

export function createDuplicateContactsForBulk(): ContactCreateInput[] {
  return [
    {
      name: "Existing Contact", // Duplicate name
      description: "Another contact with same name",
      phone: ["User says hello"],
      email: ["Show greeting"],
      createdBy: "admin",
    },
    {
      name: "New Contact",
      description: "A new contact",
      phone: ["User says goodbye"],
      email: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Contacts for bulk update testing
export function createContactsForBulkUpdate(): ContactCreateInput[] {
  return [
    {
      name: "Contact 1",
      description: "First contact",
      phone: ["User says hello"],
      email: ["Show greeting"],
      createdBy: "admin",
    },
    {
      name: "Contact 2",
      description: "Second contact",
      phone: ["User says goodbye"],
      email: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      name: "Updated Contact 1",
      description: "Updated first contact",
      updatedBy: "admin",
    },
    {
      name: "Updated Contact 2",
      description: "Updated second contact",
      updatedBy: "admin",
    },
  ]
}

// Contacts for bulk delete testing
export function createContactsForBulkDelete(): ContactCreateInput[] {
  return [
    {
      name: "Contact 1",
      description: "First contact",
      phone: ["User says hello"],
      email: ["Show greeting"],
      createdBy: "admin",
    },
    {
      name: "Contact 2",
      description: "Second contact",
      phone: ["User says goodbye"],
      email: ["Show farewell"],
      createdBy: "admin",
    },
    {
      name: "Contact 3",
      description: "Third contact",
      phone: ["User asks question"],
      email: ["Show help"],
      createdBy: "admin",
    },
  ]
}

// Invalid data creators for validation tests
export function createInvalidContact(
  type:
    | "missing-name"
    | "missing-phone"
    | "missing-email"
    | "empty-phone"
    | "empty-email"
    | "missing-phone",
): any {
  const invalidContacts = {
    "missing-phone": {
      name: "John Doe",
    },
    "missing-name": {
      phone: ["test_condition"],
      email: ["test_action"],
      createdBy: "admin",
    },
    "missing-phone": {
      name: "Invalid Contact",
      email: ["test_action"],
      createdBy: "admin",
    },
    "missing-email": {
      name: "Invalid Contact",
      phone: ["test_condition"],
      createdBy: "admin",
    },
    "empty-phone": {
      name: "Invalid Contact",
      phone: [],
      email: ["test_action"],
      createdBy: "admin",
    },
    "empty-email": {
      name: "Invalid Contact",
      phone: ["test_condition"],
      email: [],
      createdBy: "admin",
    },
  }

  return invalidContacts[type]
}

// Creator for contacts with special characteristics
export function createContactWithWhitespace(): ContactCreateInput {
  return {
    name: "  Trimmed Contact  ",
    phone: ["test_condition"],
    email: ["test_action"],
    createdBy: "admin",
  }
}

export function createContactWithManyTags(): ContactCreateInput {
  return {
    name: "Multi-tag Contact",
    phone: ["test_condition"],
    email: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin",
  }
}

export function createContactWithoutDescription(): ContactCreateInput {
  return {
    name: "Contact without description",
    phone: ["test_condition"],
    email: ["test_action"],
    createdBy: "admin",
  }
}

export function createContactWithEmptyTags(): ContactCreateInput {
  return {
    name: "Contact with empty tags",
    phone: ["test_condition"],
    email: ["test_action"],
    tags: [],
    createdBy: "admin",
  }
}

// Duplicate contact creator for conflict testing
export function createDuplicateContact(): ContactCreateInput {
  return {
    name: "Duplicate Contact",
    phone: ["condition1"],
    email: ["action1"],
    createdBy: "admin",
  }
}

export function createSecondDuplicateContact(): ContactCreateInput {
  return {
    name: "Duplicate Contact", // Same name as above
    phone: ["condition2"],
    email: ["action2"],
    createdBy: "admin",
  }
}

// Test contact with specific name for soft delete tests
export function createTestContact(): ContactCreateInput {
  return {
    name: "Test Contact",
    phone: ["condition1"],
    email: ["action1"],
    createdBy: "admin",
  }
}

export function createTestContact2(): ContactCreateInput {
  return {
    name: "Test Contact",
    phone: ["condition2"],
    email: ["action2"],
    createdBy: "admin",
  }
}
