// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic"
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository"
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateContact,
  implHandleGetContact,
  implHandleGetAllContacts,
} from "@/app/api/v1/contacts/impl"
import {
  createContact,
  createSimpleContacts,
  createContactsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read Contact API Tests", () => {
  let businessLogic: ContactBusinessLogicInterface
  let dbRepository: TestContactDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact")
    await driver.connect()
    const originalDb = new MongoContactRepository(driver)
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ContactBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/contacts/:id", () => {
    it("should successfully get contact by ID", async () => {
      const contact = createContact(5) // John Doe Contact

      const createResult = await implHandleCreateContact(contact, businessLogic)
      const id = createResult.body.data.id

      const result = await implHandleGetContact(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.name).toBe(contact.name)
    })

    it("should fail to get non-existent contact", async () => {
      const result = await implHandleGetContact(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty contact ID", async () => {
      const result = await implHandleGetContact("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only contact ID", async () => {
      const result = await implHandleGetContact("   ", businessLogic)
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/contacts", () => {
    it("should get all contacts", async () => {
      const contacts = createSimpleContacts()
      for (const r of contacts) await implHandleCreateContact(r, businessLogic)

      const result = await implHandleGetAllContacts(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no contacts exist", async () => {
      const result = await implHandleGetAllContacts(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/contacts/search", () => {
    beforeEach(async () => {
      const data = createContactsWithTags()
      for (const r of data) await implHandleCreateContact(r, businessLogic)
    })

    it("should search by name", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/contacts/filters", () => {
    beforeEach(async () => {
      const data = createContactsWithTags()
      for (const r of data) await implHandleCreateContact(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllContacts(businessLogic, params)
      expect(result.status).toBe(400)
    })
  })
})
