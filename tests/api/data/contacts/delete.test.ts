// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic"
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository"
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateContact,
  implHandleGetContact,
  implHandleDeleteContact,
} from "@/app/api/v1/contacts/impl"
import {
  createContact,
  createSimpleContacts,
  createComplexContact,
  createMinimalDeleteContact,
  createContactsForDeletionTest,
  createRetryDeleteContact,
} from "./object_creator"

describe("Delete Contact API Tests", () => {
  let businessLogic: ContactBusinessLogicInterface
  let dbRepository: TestContactDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact")
    await driver.connect()
    const originalDb = new MongoContactRepository(driver)
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ContactBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/contacts/:id", () => {
    it("should successfully delete an existing contact", async () => {
      const contactsData = createContact(5) // John Doe Contact
      const createResult = await implHandleCreateContact(
        contactsData,
        businessLogic,
      )
      const contactsId = createResult.body.data.id

      const getResult = await implHandleGetContact(contactsId, businessLogic)
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteContact(
        contactsId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "Contact deleted successfully",
      )

      const getAfterDelete = await implHandleGetContact(
        contactsId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify contact count decreases after deletion", async () => {
      const contactsData = createSimpleContacts()

      const contactsIds: string[] = []
      for (const data of contactsData) {
        const result = await implHandleCreateContact(data, businessLogic)
        contactsIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getContactCount()).toBe(3)

      const deleteResult = await implHandleDeleteContact(
        contactsIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getContactCount()).toBe(2)

      const getDeleted = await implHandleGetContact(
        contactsIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent contact", async () => {
      const result = await implHandleDeleteContact(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Contact not found")
    })

    it("should fail with empty contact ID", async () => {
      const result = await implHandleDeleteContact("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Contact ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const contactsData = createComplexContact()
      const result = await implHandleCreateContact(contactsData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteContact(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetContact(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const contactsData = createMinimalDeleteContact()
      const result = await implHandleCreateContact(contactsData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteContact(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetContact(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple contacts", async () => {
      const contactsData = createSimpleContacts()

      const ids: string[] = []
      for (const data of contactsData) {
        const res = await implHandleCreateContact(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteContact(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getContactCount()).toBe(0)
    })

    it("should not affect other contacts when deleting one", async () => {
      const contactsData = createContactsForDeletionTest()

      const ids: string[] = []
      for (const data of contactsData) {
        const res = await implHandleCreateContact(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteContact(ids[1], businessLogic)

      const getDeleted = await implHandleGetContact(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetContact(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetContact(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getContactCount()).toBe(2)
    })

    it("should handle attempting to delete the same contact twice", async () => {
      const contactsData = createRetryDeleteContact()
      const result = await implHandleCreateContact(contactsData, businessLogic)
      const id = result.body.data?.id

      const first = await implHandleDeleteContact(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteContact(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
