// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface"
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic"
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository"
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTeam,
  implHandleGetTeam,
  implHandleDeleteTeam,
} from "@/app/api/v1/teams/impl"
import {
  createTeam,
  createSimpleTeams,
  createComplexTeam,
  createMinimalDeleteTeam,
  createTeamsForDeletionTest,
  createRetryDeleteTeam,
} from "./object_creator"

describe("Delete Team API Tests", () => {
  let businessLogic: TeamBusinessLogicInterface
  let dbRepository: TestTeamDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Team")
    await driver.connect()
    const originalDb = new MongoTeamRepository(driver)
    dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver)
    businessLogic = new TeamBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/teams/:id", () => {
    it("should successfully delete an existing team", async () => {
      const teamsData = createTeam(5) // John Doe Team
      const createResult = await implHandleCreateTeam(teamsData, businessLogic)
      const teamsId = createResult.body.data.id

      const getResult = await implHandleGetTeam(teamsId, businessLogic)
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteTeam(teamsId, businessLogic)

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe("Team deleted successfully")

      const getAfterDelete = await implHandleGetTeam(teamsId, businessLogic)
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify team count decreases after deletion", async () => {
      const teamsData = createSimpleTeams()

      const teamsIds: string[] = []
      for (const data of teamsData) {
        const result = await implHandleCreateTeam(data, businessLogic)
        teamsIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getTeamCount()).toBe(3)

      const deleteResult = await implHandleDeleteTeam(
        teamsIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getTeamCount()).toBe(2)

      const getDeleted = await implHandleGetTeam(teamsIds[1], businessLogic)
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent team", async () => {
      const result = await implHandleDeleteTeam(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Team not found")
    })

    it("should fail with empty team ID", async () => {
      const result = await implHandleDeleteTeam("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Team ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const teamsData = createComplexTeam()
      const result = await implHandleCreateTeam(teamsData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteTeam(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetTeam(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const teamsData = createMinimalDeleteTeam()
      const result = await implHandleCreateTeam(teamsData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteTeam(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetTeam(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple teams", async () => {
      const teamsData = createSimpleTeams()

      const ids: string[] = []
      for (const data of teamsData) {
        const res = await implHandleCreateTeam(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteTeam(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getTeamCount()).toBe(0)
    })

    it("should not affect other teams when deleting one", async () => {
      const teamsData = createTeamsForDeletionTest()

      const ids: string[] = []
      for (const data of teamsData) {
        const res = await implHandleCreateTeam(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteTeam(ids[1], businessLogic)

      const getDeleted = await implHandleGetTeam(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetTeam(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetTeam(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getTeamCount()).toBe(2)
    })

    it("should handle attempting to delete the same team twice", async () => {
      const teamsData = createRetryDeleteTeam()
      const result = await implHandleCreateTeam(teamsData, businessLogic)
      const id = result.body.data?.id

      const first = await implHandleDeleteTeam(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteTeam(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
