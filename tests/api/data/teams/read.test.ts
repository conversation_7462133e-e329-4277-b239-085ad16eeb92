// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface"
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic"
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository"
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTeam,
  implHandleGetTeam,
  implHandleGetAllTeams,
} from "@/app/api/v1/teams/impl"
import {
  createTeam,
  createSimpleTeams,
  createTeamsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read Team API Tests", () => {
  let businessLogic: TeamBusinessLogicInterface
  let dbRepository: TestTeamDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Team")
    await driver.connect()
    const originalDb = new MongoTeamRepository(driver)
    dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver)
    businessLogic = new TeamBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/teams/:id", () => {
    it("should successfully get team by ID", async () => {
      const team = createTeam(5) // John Doe Team

      const createResult = await implHandleCreateTeam(team, businessLogic)
      const id = createResult.body.data.id

      const result = await implHandleGetTeam(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.STRING_FIELD).toBe(team.STRING_FIELD)
    })

    it("should fail to get non-existent team", async () => {
      const result = await implHandleGetTeam(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty team ID", async () => {
      const result = await implHandleGetTeam("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only team ID", async () => {
      const result = await implHandleGetTeam("   ", businessLogic)
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/teams", () => {
    it("should get all teams", async () => {
      const teams = createSimpleTeams()
      for (const r of teams) await implHandleCreateTeam(r, businessLogic)

      const result = await implHandleGetAllTeams(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no teams exist", async () => {
      const result = await implHandleGetAllTeams(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/teams/search", () => {
    beforeEach(async () => {
      const data = createTeamsWithTags()
      for (const r of data) await implHandleCreateTeam(r, businessLogic)
    })

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/teams/filters", () => {
    beforeEach(async () => {
      const data = createTeamsWithTags()
      for (const r of data) await implHandleCreateTeam(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllTeams(businessLogic, params)
      expect(result.status).toBe(400)
    })
  })
})
