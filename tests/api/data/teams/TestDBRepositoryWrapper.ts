import {
  TeamDbQueryParams,
  TeamDBRepository,
} from "@/lib/repositories/teams/DBRepository"
import {
  Team,
  TeamCreateInput,
  TeamUpdateInput,
} from "@/lib/repositories/teams/interface"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"

type MethodNames = keyof TeamDBRepository

export class TestTeamDBRepositoryWrapper implements TeamDBRepository {
  private callCounts: Record<MethodNames, number> = {
    getById: 0,
    getAll: 0,
    getCount: 0,
    create: 0,
    update: 0,
    delete: 0,
    restore: 0,
    bulkCreate: 0,
    bulkUpdate: 0,
    bulkDelete: 0,
    clear: 0,
  }

  constructor(
    private db: TeamDBRepository,
    private driver: InMemoryMongoDriver,
  ) {}

  getCallCount(method: MethodNames): number {
    return this.callCounts[method]
  }

  async getById(id: string, includeDeleted = false): Promise<Team | null> {
    this.callCounts.getById++
    return this.db.getById(id, includeDeleted)
  }

  async getAll(
    params: TeamDbQueryParams,
  ): Promise<{ items: Team[]; total: number }> {
    this.callCounts.getAll++
    return this.db.getAll(params)
  }

  async getCount(
    params: TeamDbQueryParams | undefined,
  ): Promise<{ total: number }> {
    this.callCounts.getCount++
    return this.db.getCount(params)
  }

  async create(data: TeamCreateInput): Promise<Team> {
    this.callCounts.create++
    return this.db.create(data)
  }

  async update(id: string, data: TeamUpdateInput): Promise<Team | null> {
    this.callCounts.update++
    return this.db.update(id, data)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.callCounts.delete++
    return this.db.delete(id, hardDelete)
  }

  async restore(id: string): Promise<boolean> {
    this.callCounts.restore++
    return this.db.restore(id)
  }

  async bulkCreate(data: TeamCreateInput[]): Promise<Team[]> {
    this.callCounts.bulkCreate++
    return this.db.bulkCreate(data)
  }

  async bulkUpdate(
    updates: { id: string; data: TeamUpdateInput }[],
  ): Promise<number> {
    this.callCounts.bulkUpdate++
    return this.db.bulkUpdate(updates)
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    this.callCounts.bulkDelete++
    return this.db.bulkDelete(ids, hardDelete)
  }

  async clear(): Promise<void> {
    await this.driver.clear()
  }

  async getTeamCount(includeDeleted = false): Promise<number> {
    const { total } = await this.getAll({
      includeDeleted,
      limit: 0,
    })
    return total
  }
}
