//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface"
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic"
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository"
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTeam,
  implHandleGetAllTeams,
  implHandleDeleteTeam,
} from "@/app/api/v1/teams/impl"
import {
  createMultipleTeams,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated Team API Tests", () => {
  let businessLogic: TeamBusinessLogicInterface
  let dbRepository: TestTeamDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Team")
    await driver.connect()
    const originalDb = new MongoTeamRepository(driver)
    dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver)
    businessLogic = new TeamBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testTeams = createMultipleTeams()

  describe("implHandleGetAllTeams - Consolidated Function", () => {
    beforeEach(async () => {
      for (const teamsData of testTeams) {
        await implHandleCreateTeam(teamsData, businessLogic)
      }
    })

    it("should get all teams when no parameters provided", async () => {
      const result = await implHandleGetAllTeams(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search teams by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Team and Bob Johnson Team

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testTeams[0].STRING_FIELD) // John Doe Team
      expect(STRING_FIELDs).toContain(testTeams[2].STRING_FIELD) // Bob Johnson Team
    })

    it("should search teams by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All teams have "processing" in ARRAY_FIELD2
    })

    it("should filter teams by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Team and Bob Johnson Team

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testTeams[0].STRING_FIELD) // John Doe Team
      expect(STRING_FIELDs).toContain(testTeams[2].STRING_FIELD) // Bob Johnson Team
    })

    it("should filter teams by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Team and Jane Smith Team

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testTeams[0].STRING_FIELD) // John Doe Team
      expect(STRING_FIELDs).toContain(testTeams[1].STRING_FIELD) // Jane Smith Team
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs![0]).toBe(testTeams[3].STRING_FIELD) // Alice Brown Team
      expect(STRING_FIELDs![1]).toBe(testTeams[2].STRING_FIELD) // Bob Johnson Team
      expect(STRING_FIELDs![2]).toBe(testTeams[1].STRING_FIELD) // Jane Smith Team
      expect(STRING_FIELDs![3]).toBe(testTeams[0].STRING_FIELD) // John Doe Team
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP teams (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllTeams(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted teams when specified", async () => {
      // Get all teams first to get one to delete
      const allResult = await implHandleGetAllTeams(businessLogic)
      expect(allResult.status).toBe(200)
      const teamsToDelete = allResult.body.data?.items[0]
      expect(teamsToDelete).toBeDefined()

      // Soft delete one teams
      const deleteResult = await implHandleDeleteTeam(
        teamsToDelete!.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllTeams(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllTeams(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
