import {
  TeamCreateInput,
  TeamUpdateInput,
} from "@/lib/repositories/teams/interface"

/**
 * Factory functions for creating test Team objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createTeam(variant: number): TeamCreateInput {
  const baseTeams: Record<number, TeamCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support Team",
      STRING_FIELD2: "Team for handling customer support requests",
      ARRAY_FIELD2: [
        "team_message_contains('help')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin",
    },
    2: {
      STRING_FIELD: "Simple Team",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin",
    },
    3: {
      STRING_FIELD: "Test Team",
      STRING_FIELD2: "A test team with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    4: {
      STRING_FIELD: "Tagged Team",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin",
    },
    5: {
      STRING_FIELD: "John Doe Team",
      STRING_FIELD2: "Team for John Doe processing",
      ARRAY_FIELD2: [
        "team_STRING_FIELD_contains('john')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin",
    },
    6: {
      STRING_FIELD: "Jane Smith Team",
      STRING_FIELD2: "Team for Jane Smith processing",
      ARRAY_FIELD2: ["team_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin",
    },
    7: {
      STRING_FIELD: "Bob Johnson Team",
      STRING_FIELD2: "Team for Bob Johnson processing",
      ARRAY_FIELD2: ["team_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin",
    },
    8: {
      STRING_FIELD: "Alice Brown Team",
      STRING_FIELD2: "Team for Alice Brown processing",
      ARRAY_FIELD2: [
        "team_STRING_FIELD_contains('alice')",
        "lead_qualification",
      ],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin",
    },
  }

  if (!baseTeams[variant]) {
    throw new Error(
      `Team variant ${variant} not found. Available variants: ${Object.keys(baseTeams).join(", ")}`,
    )
  }

  return { ...baseTeams[variant] }
}

// Specialized creator functions for specific test scenarios
export function createMinimalTeam(): TeamCreateInput {
  return createTeam(2)
}

export function createFullTeam(): TeamCreateInput {
  return createTeam(1)
}

export function createTeamWithDescription(): TeamCreateInput {
  return createTeam(3)
}

export function createTeamWithTags(): TeamCreateInput {
  return createTeam(4)
}

// Creator for multiple teams (useful for bulk operations and search tests)
export function createMultipleTeams(): TeamCreateInput[] {
  return [
    createTeam(5), // John Doe Team
    createTeam(6), // Jane Smith Team
    createTeam(7), // Bob Johnson Team
    createTeam(8), // Alice Brown Team
  ]
}

// Creator for simple test teams (useful for basic CRUD operations)
export function createSimpleTeams(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "A",
      ARRAY_FIELD2: ["1"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "B",
      ARRAY_FIELD2: ["2"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "C",
      ARRAY_FIELD2: ["3"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creator for teams with specific tags (useful for filtering tests)
export function createTeamsWithTags(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "John Doe",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
      tags: ["Customer", "VIP"],
    },
    {
      STRING_FIELD: "Jane Smith",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
      tags: ["Lead", "Potential"],
    },
    {
      STRING_FIELD: "Bob Johnson",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
      tags: ["Customer"],
    },
    {
      STRING_FIELD: "Alice Brown",
      ARRAY_FIELD2: ["a"],
      ARRAY_FIELD: ["d"],
      createdBy: "admin",
      tags: ["VIP"],
    },
  ]
}

// Update data creators
export function createTeamUpdate(variant: number): TeamUpdateInput {
  const baseUpdates: Record<number, TeamUpdateInput> = {
    1: {
      STRING_FIELD: "Updated Team",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin",
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin",
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin",
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin",
    },
    5: {
      isActive: false,
      updatedBy: "admin",
    },
  }

  if (!baseUpdates[variant]) {
    throw new Error(
      `Team update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(", ")}`,
    )
  }

  return { ...baseUpdates[variant] }
}

// Specialized update creators
export function createFullTeamUpdate(): TeamUpdateInput {
  return createTeamUpdate(1)
}

export function createNameOnlyUpdate(): TeamUpdateInput {
  return createTeamUpdate(2)
}

export function createDescriptionOnlyUpdate(): TeamUpdateInput {
  return createTeamUpdate(3)
}

export function createTagsOnlyUpdate(): TeamUpdateInput {
  return createTeamUpdate(4)
}

export function createStatusOnlyUpdate(): TeamUpdateInput {
  return createTeamUpdate(5)
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(
  type:
    | "empty-STRING_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "empty-object",
): any {
  const invalidUpdates = {
    "empty-STRING_FIELD": {
      STRING_FIELD: "",
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      ARRAY_FIELD2: [],
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      ARRAY_FIELD: [],
      updatedBy: "admin",
    },
    "empty-object": {},
  }

  return invalidUpdates[type]
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): TeamUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin",
  }
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(
  existingName: string,
): TeamUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin",
  }
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): TeamUpdateInput {
  return {
    STRING_FIELD: "Simple Team", // Same as createMinimalTeam
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin",
  }
}

// Team for soft delete testing
export function createTeamForSoftDelete(): TeamCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// Update for soft deleted team testing
export function createUpdateForSoftDeleted(): TeamUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin",
  }
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): TeamUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin",
  }
}

// Team for trimming test
export function createTeamForTrimming(): TeamCreateInput {
  return {
    STRING_FIELD: "Original Team",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// Team for active status testing
export function createActiveTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Active Team",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin",
  }
}

// Update for status change testing
export function createStatusChangeUpdate(): TeamUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin",
  }
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllTeams
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" }
}

export function createSearchByDescriptionParams() {
  return { search: "processing" }
}

export function createEmptySearchParams() {
  return { search: "" }
}

export function createWhitespaceSearchParams() {
  return { search: "   " }
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" }
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }],
  }
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }],
  }
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2,
  }
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sort: [{ field: "STRING_FIELD", direction: "ASC" as const }],
  }
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP",
  }
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true }
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" }
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" }
}

export function createUndefinedSearchParams() {
  return { search: undefined }
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Team for retry delete testing
export function createRetryDeleteTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin",
  }
}

// ========================================
// CREATORS FOR SPECIAL CASES (Team-specific)
// ========================================

// Team with special characters and unicode
export function createSpecialCharacterTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin",
  }
}

// Team with very long ARRAY_FIELD2 (Team-specific test)
export function createLongContentTeam(): TeamCreateInput {
  return {
    STRING_FIELD:
      "Very Long Team Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2:
      "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in team STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "team.message.length > 1000",
      "team.message.includes('very long query with lots of details')",
      "team.session.duration > 3600",
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist",
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin",
  }
}

// Team with edge case ARRAY_FIELD2 (Team-specific)
export function createEdgeCaseConditionsTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "team.age >= 18 && team.age <= 65",
      "team.location.country === 'US' || team.location.country === 'CA'",
      "team.preferences.notifications === true",
    ],
    ARRAY_FIELD: ["apply_regional_teams", "send_age_appropriate_ARRAY_FIELD2"],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin",
  }
}

// Team with complex ARRAY_FIELD (Team-specific)
export function createComplexActionsTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Complex Actions Team",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('team_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=team.ARRAY_FIELD)",
      "analytics.track('complex_team_triggered', {team_id: this.id})",
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin",
  }
}

// Team with empty optional fields (Team-specific edge case)
export function createEmptyOptionalFieldsTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin",
  }
}

// Team for testing AI-specific business logic
export function createAiLogicTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "AI Decision Team",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100",
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model",
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin",
  }
}

// Creators for delete test scenarios
export function createComplexTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Complex Team",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["team.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin",
  }
}

export function createMinimalDeleteTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Minimal Team",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin",
  }
}

// Teams for testing deletion effects on other teams
export function createTeamsForDeletionTest(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "Keep This One",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Delete This One",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Keep This Too",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creators for bulk operations testing
export function createExistingTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Existing Team",
    STRING_FIELD2: "An existing team",
    ARRAY_FIELD2: ["Team says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin",
  }
}

export function createDuplicateTeamsForBulk(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing Team", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another team with same STRING_FIELD",
      ARRAY_FIELD2: ["Team says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "New Team",
      STRING_FIELD2: "A new team",
      ARRAY_FIELD2: ["Team says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Teams for bulk update testing
export function createTeamsForBulkUpdate(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "Team 1",
      STRING_FIELD2: "First team",
      ARRAY_FIELD2: ["Team says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Team 2",
      STRING_FIELD2: "Second team",
      ARRAY_FIELD2: ["Team says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated Team 1",
      STRING_FIELD2: "Updated first team",
      updatedBy: "admin",
    },
    {
      STRING_FIELD: "Updated Team 2",
      STRING_FIELD2: "Updated second team",
      updatedBy: "admin",
    },
  ]
}

// Teams for bulk delete testing
export function createTeamsForBulkDelete(): TeamCreateInput[] {
  return [
    {
      STRING_FIELD: "Team 1",
      STRING_FIELD2: "First team",
      ARRAY_FIELD2: ["Team says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Team 2",
      STRING_FIELD2: "Second team",
      ARRAY_FIELD2: ["Team says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Team 3",
      STRING_FIELD2: "Third team",
      ARRAY_FIELD2: ["Team asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin",
    },
  ]
}

// Invalid data creators for validation tests
export function createInvalidTeam(
  type:
    | "missing-STRING_FIELD"
    | "missing-ARRAY_FIELD2"
    | "missing-ARRAY_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "missing-ARRAY_FIELD2",
): any {
  const invalidTeams = {
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "John Doe",
    },
    "missing-STRING_FIELD": {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid Team",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD": {
      STRING_FIELD: "Invalid Team",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid Team",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      STRING_FIELD: "Invalid Team",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin",
    },
  }

  return invalidTeams[type]
}

// Creator for teams with special characteristics
export function createTeamWithWhitespace(): TeamCreateInput {
  return {
    STRING_FIELD: "  Trimmed Team  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createTeamWithManyTags(): TeamCreateInput {
  return {
    STRING_FIELD: "Multi-tag Team",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin",
  }
}

export function createTeamWithoutDescription(): TeamCreateInput {
  return {
    STRING_FIELD: "Team without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createTeamWithEmptyTags(): TeamCreateInput {
  return {
    STRING_FIELD: "Team with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin",
  }
}

// Duplicate team creator for conflict testing
export function createDuplicateTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Duplicate Team",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createSecondDuplicateTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Duplicate Team", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}

// Test team with specific STRING_FIELD for soft delete tests
export function createTestTeam(): TeamCreateInput {
  return {
    STRING_FIELD: "Test Team",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createTestTeam2(): TeamCreateInput {
  return {
    STRING_FIELD: "Test Team",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}
