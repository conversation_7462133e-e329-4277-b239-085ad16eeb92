//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleUpdateConversation,
  implHandleDeleteConversation,
} from "@/app/api/v1/conversations/impl"
import {
  createFullConversation,
  createMinimalConversation,
  createFullConversationUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createConversationForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createConversationForTrimming,
  createActiveConversation,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update Conversation API Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/conversations/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createFullConversationUpdate()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(updateData.variables)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(createData.variables)
    })

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
    })

    it("should fail to update non-existent conversation", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversation(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Conversation not found")
    })

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-STRING_FIELD")
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-ARRAY_FIELD")
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversation(
        "",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Conversation ID is required")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first conversation
      const createData1 = createMinimalConversation()
      await implHandleCreateConversation(createData1, businessLogic)

      // Create second conversation
      const createData2 = createFullConversation()
      const createResult2 = await implHandleCreateConversation(
        createData2,
        businessLogic,
      )

      // Try to update second conversation with first conversation's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD)
      const result = await implHandleUpdateConversation(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another Conversation with this STRING_FIELD exists",
      )
    })

    it("should allow updating conversation with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
    })

    it("should fail to update soft-deleted conversation", async () => {
      const createData = createConversationForSoftDelete()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      // Soft delete the conversation
      const deleteResult = await implHandleDeleteConversation(
        conversationsId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Try to update the soft-deleted conversation
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Conversation not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createConversationForTrimming()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description")
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"])
      expect(result.body.data?.variables).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-variables")
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveConversation()
      const createResult = await implHandleCreateConversation(
        createData,
        businessLogic,
      )
      const conversationsId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateConversation(
        conversationsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
