//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleGetConversation,
  implHandleDeleteConversation,
  implHandleUpdateConversation,
  implHandleGetAllConversations,
  implHandleRestoreConversation,
} from "@/app/api/v1/conversations/impl"
import {
  createConversation,
  createConversationUpdate,
  createConversationWithDescription,
  createTestConversation,
  createTestConversation2,
} from "./object_creator"

describe("Conversation Soft Delete Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a conversations by default", async () => {
      const conversationData = createConversationWithDescription()
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // Conversation should not be accessible by default
      const getResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(200)
      expect(getDeletedResult.body.data).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getConversationCount()).toBe(0)
      expect(await dbRepository.getConversationCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const conversationData = createConversationWithDescription()
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
        true,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // Conversation should not be accessible at all
      const getResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
      const getDeletedResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(404)

      // Count should be 0 in both cases
      expect(await dbRepository.getConversationCount()).toBe(0)
      expect(await dbRepository.getConversationCount(true)).toBe(0)
    })

    it("should not include soft deleted conversations in getAll by default", async () => {
      const conversationData1 = createConversation(1)
      const conversationData2 = createConversation(2)

      const createResult1 = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one conversations
      const deleteResult = await implHandleDeleteConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllConversations(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(1)
      expect(result.body.data?.total).toBe(1)
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id)
    })

    it("should include soft deleted conversations when specified", async () => {
      const conversationData1 = createConversation(1)
      const conversationData2 = createConversation(2)

      const createResult1 = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversation(
        conversationData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one conversations
      const deleteResult = await implHandleDeleteConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllConversations(businessLogic, {
        includeDeleted: true,
      })

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(2)

      const deletedConversation = result.body.data?.items.find(
        (c: any) => c.id === createResult1.body.data.id,
      )
      expect(deletedConversation).toBeDefined()
      expect(deletedConversation?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted conversations", async () => {
      const conversationData = createConversation(3)
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const conversationUpdate = createConversationUpdate(1)

      const result = await implHandleUpdateConversation(
        createResult.body.data.id,
        conversationUpdate,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should not include soft deleted conversations in search", async () => {
      const conversationData1 = createConversation(3) // "Test Conversation"
      const createResult = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the conversation
      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Search should not find the soft deleted conversation
      const searchResult = await implHandleGetAllConversations(businessLogic, {
        search: "Test",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted conversations", async () => {
      const conversationData = createConversationWithDescription()
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the conversations
      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // Restore the conversations
      const restoreResult = await implHandleRestoreConversation(
        createResult.body.data.id,
        businessLogic,
      )

      expect(restoreResult.status).toBe(200)
      expect(restoreResult.body.status).toBe("success")

      // Conversation should be accessible again
      const restoredResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoredResult.status).toBe(200)
      expect(restoredResult.body.data?.deletedAt).toBeUndefined()

      // Count should include the restored conversations
      expect(await dbRepository.getConversationCount()).toBe(1)
    })

    it("should fail to restore a non-existent conversations", async () => {
      const restoreResult = await implHandleRestoreConversation(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a conversations that was never deleted", async () => {
      const conversationData = createConversation(3)
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const restoreResult = await implHandleRestoreConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a hard deleted conversations", async () => {
      const conversationData = createConversation(3)
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete the conversations
      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail with empty conversations ID", async () => {
      const restoreResult = await implHandleRestoreConversation(
        "",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("Conversation ID is required")
    })

    it("should fail with whitespace-only conversations ID", async () => {
      const restoreResult = await implHandleRestoreConversation(
        "   ",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("Conversation ID is required")
    })

    it("should update updatedAt when restoring", async () => {
      const conversationData = createConversation(3)
      const createResult = await implHandleCreateConversation(
        conversationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const originalUpdatedAt = createResult.body.data.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      const deleteResult = await implHandleDeleteConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(200)

      const getResult = await implHandleGetConversation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(200)
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating conversations with STRING_FIELD of soft deleted conversations", async () => {
      // Create and soft delete a conversations
      const conversationData1 = createTestConversation()
      const createResult1 = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Should be able to create new conversations with same STRING_FIELD
      const conversationData2 = createTestConversation2()
      const createResult2 = await implHandleCreateConversation(
        conversationData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(201)
      expect(createResult2.body.data.STRING_FIELD).toBe(
        conversationData2.STRING_FIELD,
      )
      expect(await dbRepository.getConversationCount()).toBe(1)
    })

    it("should prevent creating conversations with STRING_FIELD of active conversations", async () => {
      const conversationData1 = createTestConversation()
      const createResult1 = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const conversationData2 = createTestConversation2()
      const createResult2 = await implHandleCreateConversation(
        conversationData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(409)
      expect(createResult2.body.status).toBe("failed")
      expect(createResult2.body.error).toContain(
        "Conversation with the same STRING_FIELD already exists",
      )
    })

    it("should prevent restoring conversations if STRING_FIELD is now taken", async () => {
      // Create and soft delete a conversations
      const conversationData1 = createTestConversation()
      const createResult1 = await implHandleCreateConversation(
        conversationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Create new conversations with same STRING_FIELD
      const conversationData2 = createTestConversation2()
      const createResult2 = await implHandleCreateConversation(
        conversationData2,
        businessLogic,
      )
      expect(createResult2.status).toBe(201)

      // Should not be able to restore the first conversations
      const restoreResult = await implHandleRestoreConversation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })
  })
})
