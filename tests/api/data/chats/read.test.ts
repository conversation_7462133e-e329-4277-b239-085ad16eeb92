// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversation,
  implHandleGetConversation,
  implHandleGetAllConversations,
} from "@/app/api/v1/conversations/impl"
import {
  createConversation,
  createSimpleConversations,
  createConversationsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read Conversation API Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/conversations/:id", () => {
    it("should successfully get conversation by ID", async () => {
      const conversation ConversationeateConversation(5) // JConversationDoe Conversation

      const createResult = await implHandleCreateConversation(conversation, businessLogic)
      const id = createResult.body.data.id

      const result = await implHandleGetConversation(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.STRING_FIELD).toBe(conversation.STRING_FIELD)
    })

    it("should fail to get non-existent conversation", async () => {
      const result = await implHandleGetConversation(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty conversation ID", async () => {
      const result = await implHandleGetConversation("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only conversation ID", async () => {
      const result = await implHandleGetConversation("   ", businessLogic)
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/conversations", () => {
    it("should get all conversations", async () => {
      const conversations = creaConversationmpleConversations()
      for (const r of conversations) await implHandConversationeateConversation(r, businessLogic)

      const result = await implHandleGetAllConversations(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no conversations exist", async () => {
      const result = await implHandleGetAllConversations(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/conversations/search", () => {
    beforeEach(async () => {
      const data = createConversationsWithTags()
      for (const r of data) await implHandleCreateConversation(r, businessLogic)
    })

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/conversations/filters", () => {
    beforeEach(async () => {
      const data = createConversationsWithTags()
      for (const r of data) await implHandleCreateConversation(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllConversations(businessLogic, params)
      expect(result.status).toBe(400)
    })
  })
})
