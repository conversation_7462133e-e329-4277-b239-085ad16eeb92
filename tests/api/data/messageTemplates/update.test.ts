//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateMessageTemplate,
  implHandleUpdateMessageTemplate,
} from "@/app/api/v1/message-templates/impl"
import {
  createFullMessageTemplate,
  createMinimalMessageTemplate,
  createFullMessageTemplateUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createMessageTemplateForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createMessageTemplateForTrimming,
  createActiveMessageTemplate,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update MessageTemplate API Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/message-templates/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createFullMessageTemplateUpdate()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.title).toBe(updateData.title)
      expect(result.body.data?.content).toBe(updateData.content)
      expect(result.body.data?.category).toEqual(updateData.category)
      expect(result.body.data?.variables).toEqual(updateData.variables)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the title", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.title).toBe(updateData.title)
      expect(result.body.data?.category).toEqual(createData.category)
      expect(result.body.data?.variables).toEqual(createData.variables)
    })

    it("should trim title when updating", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.title).toBe("Trimmed Name")
    })

    it("should fail to update non-existent messageTemplate", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateMessageTemplate(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("MessageTemplate not found")
    })

    it("should fail with invalid input: empty title", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-title")
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty category", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-category")
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateMessageTemplate(
        "",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("MessageTemplate ID is required")
    })

    it("should fail with duplicate title", async () => {
      // Create first messageTemplate
      const createData1 = createMinimalMessageTemplate()
      await implHandleCreateMessageTemplate(createData1, businessLogic)

      // Create second messageTemplate
      const createData2 = createFullMessageTemplate()
      const createResult2 = await implHandleCreateMessageTemplate(
        createData2,
        businessLogic,
      )

      // Try to update second messageTemplate with first messageTemplate's title
      const updateData = createDuplicateNameUpdate(createData1.title)
      const result = await implHandleUpdateMessageTemplate(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another MessageTemplate with this title exists",
      )
    })

    it("should allow updating messageTemplate with same title (no change)", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.title).toBe(updateData.title)
      expect(result.body.data?.content).toBe(updateData.content)
    })

    it("should fail to update soft-deleted messageTemplate", async () => {
      const createData = createMessageTemplateForSoftDelete()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      // Soft delete the messageTemplate
      await businessLogic.delete(messageTemplatesId)

      // Try to update the soft-deleted messageTemplate
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("MessageTemplate not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createMessageTemplateForTrimming()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.title).toBe("Trimmed Name")
      expect(result.body.data?.content).toBe("Trimmed Description")
      expect(result.body.data?.category).toEqual(["trimmed_condition"])
      expect(result.body.data?.variables).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-variables")
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        createData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateMessageTemplate(
        messageTemplatesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
