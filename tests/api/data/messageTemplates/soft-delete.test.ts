//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  createMessageTemplate,
  createMessageTemplateUpdate,
  createMessageTemplateWithDescription,
  createTestMessageTemplate,
  createTestMessageTemplate2,
} from "./object_creator"

describe("MessageTemplate Soft Delete Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a messageTemplates by default", async () => {
      const messageTemplateData = createMessageTemplateWithDescription()
      const messageTemplates = await businessLogic.create(messageTemplateData)

      const deleted = await businessLogic.delete(messageTemplates.id)

      expect(deleted).toBe(true)

      // MessageTemplate should not be accessible by default
      expect(await businessLogic.getById(messageTemplates.id)).toBeNull()

      // But should be accessible when including deleted
      expect(
        await businessLogic.getById(messageTemplates.id, true),
      ).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getMessageTemplateCount()).toBe(0)
      expect(await dbRepository.getMessageTemplateCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const messageTemplateData = createMessageTemplateWithDescription()
      const messageTemplates = await businessLogic.create(messageTemplateData)

      const deleted = await businessLogic.delete(messageTemplates.id, true)

      expect(deleted).toBe(true)

      // MessageTemplate should not be accessible at all
      expect(await businessLogic.getById(messageTemplates.id)).toBeNull()
      expect(await businessLogic.getById(messageTemplates.id, true)).toBeNull()

      // Count should be 0 in both cases
      expect(await dbRepository.getMessageTemplateCount()).toBe(0)
      expect(await dbRepository.getMessageTemplateCount(true)).toBe(0)
    })

    it("should not include soft deleted messageTemplates in getAll by default", async () => {
      const messageTemplateData1 = createMessageTemplate(1)
      const messageTemplateData2 = createMessageTemplate(2)

      const messageTemplates1 = await businessLogic.create(messageTemplateData1)
      const messageTemplates2 = await businessLogic.create(messageTemplateData2)

      // Soft delete one messageTemplates
      await businessLogic.delete(messageTemplates1.id)

      const result = await businessLogic.getAll()

      expect(result.items).toHaveLength(1)
      expect(result.total).toBe(1)
      expect(result.items[0].id).toBe(messageTemplates2.id)
    })

    it("should include soft deleted messageTemplates when specified", async () => {
      const messageTemplateData1 = createMessageTemplate(1)
      const messageTemplateData2 = createMessageTemplate(2)

      const messageTemplates1 = await businessLogic.create(messageTemplateData1)
      const messageTemplates2 = await businessLogic.create(messageTemplateData2)

      // Soft delete one messageTemplates
      await businessLogic.delete(messageTemplates1.id)

      const result = await businessLogic.getAll({ includeDeleted: true })

      expect(result.items).toHaveLength(2)
      expect(result.total).toBe(2)

      const deletedMessageTemplate = result.items.find(
        (c) => c.id === messageTemplates1.id,
      )
      expect(deletedMessageTemplate).not.toBeNull()
      expect(deletedMessageTemplate?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted messageTemplates", async () => {
      const messageTemplateData = createMessageTemplate(3)
      const messageTemplates = await businessLogic.create(messageTemplateData)

      await businessLogic.delete(messageTemplates.id)

      const messageTemplateUpdate = createMessageTemplateUpdate(1)

      const result = await businessLogic.update(
        messageTemplates.id,
        messageTemplateUpdate,
      )

      await expect(result).toBeNull()
    })

    it("should not include soft deleted messageTemplates in search", async () => {
      const messageTemplateData1 = createMessageTemplate(3) // "Test MessageTemplate"
      const messageTemplates1 = await businessLogic.create(messageTemplateData1)

      // Soft delete the messageTemplate
      await businessLogic.delete(messageTemplates1.id)

      // Search should not find the soft deleted messageTemplate
      const searchResults = await businessLogic.search("Test")
      expect(searchResults).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted messageTemplates", async () => {
      const messageTemplateData = createMessageTemplateWithDescription()
      const messageTemplates = await businessLogic.create(messageTemplateData)

      // Soft delete the messageTemplates
      await businessLogic.delete(messageTemplates.id)
      expect(await businessLogic.getById(messageTemplates.id)).toBeNull()

      // Restore the messageTemplates
      const restored = await businessLogic.restore(messageTemplates.id)

      expect(restored).toBe(true)

      // MessageTemplate should be accessible again
      const restoredMessageTemplate = await businessLogic.getById(
        messageTemplates.id,
      )
      expect(restoredMessageTemplate).not.toBeNull()
      expect(restoredMessageTemplate?.deletedAt).toBeUndefined()

      // Count should include the restored messageTemplates
      expect(await dbRepository.getMessageTemplateCount()).toBe(1)
    })

    it("should fail to restore a non-existent messageTemplates", async () => {
      const restored = await businessLogic.restore("507f1f77bcf86cd799439011")
      expect(restored).toBe(false)
    })

    it("should fail to restore a messageTemplates that was never deleted", async () => {
      const messageTemplateData = createMessageTemplate(3)
      const messageTemplates = await businessLogic.create(messageTemplateData)

      const restored = await businessLogic.restore(messageTemplates.id)
      expect(restored).toBe(false)
    })

    it("should fail to restore a hard deleted messageTemplates", async () => {
      const messageTemplateData = createMessageTemplate(3)
      const messageTemplates = await businessLogic.create(messageTemplateData)

      // Hard delete the messageTemplates
      await businessLogic.delete(messageTemplates.id, true)

      const restored = await businessLogic.restore(messageTemplates.id)
      expect(restored).toBe(false)
    })

    it("should fail with empty messageTemplates ID", async () => {
      await expect(businessLogic.restore("")).rejects.toThrow(
        "MessageTemplate ID is required",
      )
    })

    it("should fail with whitespace-only messageTemplates ID", async () => {
      await expect(businessLogic.restore("   ")).rejects.toThrow(
        "MessageTemplate ID is required",
      )
    })

    it("should update updatedAt when restoring", async () => {
      const messageTemplateData = createMessageTemplate(3)
      const messageTemplates = await businessLogic.create(messageTemplateData)

      const originalUpdatedAt = messageTemplates.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      await businessLogic.delete(messageTemplates.id)
      await businessLogic.restore(messageTemplates.id)

      const restoredMessageTemplate = await businessLogic.getById(
        messageTemplates.id,
      )
      expect(restoredMessageTemplate?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating messageTemplates with title of soft deleted messageTemplates", async () => {
      // Create and soft delete a messageTemplates
      const messageTemplateData1 = createTestMessageTemplate()
      const messageTemplates1 = await businessLogic.create(messageTemplateData1)
      await businessLogic.delete(messageTemplates1.id)

      // Should be able to create new messageTemplates with same title
      const messageTemplateData2 = createTestMessageTemplate2()
      const messageTemplates2 = await businessLogic.create(messageTemplateData2)

      expect(messageTemplates2.title).toBe(messageTemplateData2.title)
      expect(await dbRepository.getMessageTemplateCount()).toBe(1)
    })

    it("should prevent creating messageTemplates with title of active messageTemplates", async () => {
      const messageTemplateData1 = createTestMessageTemplate()
      await businessLogic.create(messageTemplateData1)

      const messageTemplateData2 = createTestMessageTemplate2()
      await expect(businessLogic.create(messageTemplateData2)).rejects.toThrow(
        "MessageTemplate with the same title already exists",
      )
    })

    it("should prevent restoring messageTemplates if title is now taken", async () => {
      // Create and soft delete a messageTemplates
      const messageTemplateData1 = createTestMessageTemplate()
      const messageTemplates1 = await businessLogic.create(messageTemplateData1)
      await businessLogic.delete(messageTemplates1.id)

      // Create new messageTemplates with same title
      const messageTemplateData2 = createTestMessageTemplate2()
      await businessLogic.create(messageTemplateData2)

      // Should not be able to restore the first messageTemplates
      const restored = await businessLogic.restore(messageTemplates1.id)
      expect(restored).toBe(false)
    })
  })
})
