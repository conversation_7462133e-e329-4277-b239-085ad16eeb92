// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateMessageTemplate,
  implHandleGetMessageTemplate,
  implHandleDeleteMessageTemplate,
} from "@/app/api/v1/message-templates/impl"
import {
  createMessageTemplate,
  createSimpleMessageTemplates,
  createComplexMessageTemplate,
  createMinimalDeleteMessageTemplate,
  createMessageTemplatesForDeletionTest,
  createRetryDeleteMessageTemplate,
} from "./object_creator"

describe("Delete MessageTemplate API Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/message-templates/:id", () => {
    it("should successfully delete an existing messageTemplate", async () => {
      const messageTemplatesData = createMessageTemplate(5) // John Doe MessageTemplate
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplatesData,
        businessLogic,
      )
      const messageTemplatesId = createResult.body.data.id

      const getResult = await implHandleGetMessageTemplate(
        messageTemplatesId,
        businessLogic,
      )
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteMessageTemplate(
        messageTemplatesId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "MessageTemplate deleted successfully",
      )

      const getAfterDelete = await implHandleGetMessageTemplate(
        messageTemplatesId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify messageTemplate count decreases after deletion", async () => {
      const messageTemplatesData = createSimpleMessageTemplates()

      const messageTemplatesIds: string[] = []
      for (const data of messageTemplatesData) {
        const result = await implHandleCreateMessageTemplate(
          data,
          businessLogic,
        )
        messageTemplatesIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getMessageTemplateCount()).toBe(3)

      const deleteResult = await implHandleDeleteMessageTemplate(
        messageTemplatesIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getMessageTemplateCount()).toBe(2)

      const getDeleted = await implHandleGetMessageTemplate(
        messageTemplatesIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent messageTemplate", async () => {
      const result = await implHandleDeleteMessageTemplate(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("MessageTemplate not found")
    })

    it("should fail with empty messageTemplate ID", async () => {
      const result = await implHandleDeleteMessageTemplate("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("MessageTemplate ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const messageTemplatesData = createComplexMessageTemplate()
      const result = await implHandleCreateMessageTemplate(
        messageTemplatesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteMessageTemplate(
        id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetMessageTemplate(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const messageTemplatesData = createMinimalDeleteMessageTemplate()
      const result = await implHandleCreateMessageTemplate(
        messageTemplatesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteMessageTemplate(
        id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetMessageTemplate(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple messageTemplates", async () => {
      const messageTemplatesData = createSimpleMessageTemplates()

      const ids: string[] = []
      for (const data of messageTemplatesData) {
        const res = await implHandleCreateMessageTemplate(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteMessageTemplate(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getMessageTemplateCount()).toBe(0)
    })

    it("should not affect other messageTemplates when deleting one", async () => {
      const messageTemplatesData = createMessageTemplatesForDeletionTest()

      const ids: string[] = []
      for (const data of messageTemplatesData) {
        const res = await implHandleCreateMessageTemplate(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteMessageTemplate(ids[1], businessLogic)

      const getDeleted = await implHandleGetMessageTemplate(
        ids[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetMessageTemplate(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetMessageTemplate(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getMessageTemplateCount()).toBe(2)
    })

    it("should handle attempting to delete the same messageTemplate twice", async () => {
      const messageTemplatesData = createRetryDeleteMessageTemplate()
      const result = await implHandleCreateMessageTemplate(
        messageTemplatesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const first = await implHandleDeleteMessageTemplate(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteMessageTemplate(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
