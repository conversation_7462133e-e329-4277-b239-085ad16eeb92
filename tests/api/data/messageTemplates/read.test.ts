// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateMessageTemplate,
  implHandleGetMessageTemplate,
  implHandleGetAllMessageTemplates,
} from "@/app/api/v1/message-templates/impl"
import {
  createMessageTemplate,
  createSimpleMessageTemplates,
  createMessageTemplatesWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read MessageTemplate API Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/message-templates/:id", () => {
    it("should successfully get messageTemplate by ID", async () => {
      const messageTemplate = createMessageTemplate(5) // John Doe MessageTemplate

      const createResult = await implHandleCreateMessageTemplate(
        messageTemplate,
        businessLogic,
      )
      const id = createResult.body.data.id

      const result = await implHandleGetMessageTemplate(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.title).toBe(messageTemplate.title)
    })

    it("should fail to get non-existent messageTemplate", async () => {
      const result = await implHandleGetMessageTemplate(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty messageTemplate ID", async () => {
      const result = await implHandleGetMessageTemplate("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only messageTemplate ID", async () => {
      const result = await implHandleGetMessageTemplate("   ", businessLogic)
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/message-templates", () => {
    it("should get all messageTemplates", async () => {
      const messageTemplates = createSimpleMessageTemplates()
      for (const r of messageTemplates)
        await implHandleCreateMessageTemplate(r, businessLogic)

      const result = await implHandleGetAllMessageTemplates(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no messageTemplates exist", async () => {
      const result = await implHandleGetAllMessageTemplates(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/message-templates/search", () => {
    beforeEach(async () => {
      const data = createMessageTemplatesWithTags()
      for (const r of data)
        await implHandleCreateMessageTemplate(r, businessLogic)
    })

    it("should search by title", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/message-templates/filters", () => {
    beforeEach(async () => {
      const data = createMessageTemplatesWithTags()
      for (const r of data)
        await implHandleCreateMessageTemplate(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })
  })
})
