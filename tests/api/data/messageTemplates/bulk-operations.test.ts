//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  createMultipleMessageTemplates,
  createSimpleMessageTemplates,
  createExistingMessageTemplate,
  createDuplicateMessageTemplatesForBulk,
  createMessageTemplatesForBulkUpdate,
  createBulkUpdateData,
  createMessageTemplatesForBulkDelete,
} from "./object_creator"

describe("MessageTemplate Bulk Operations Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Bulk Create", () => {
    it("should successfully create multiple messageTemplates", async () => {
      const messageTemplatesData = createMultipleMessageTemplates()

      const result = await businessLogic.bulkCreate(messageTemplatesData)

      expect(result).toHaveLength(messageTemplatesData.length)
      expect(result[0].title).toBe(messageTemplatesData[0].title)
      expect(result[1].title).toBe(messageTemplatesData[1].title)
      expect(result[2].title).toBe(messageTemplatesData[2].title)
      expect(await dbRepository.getMessageTemplateCount()).toBe(
        messageTemplatesData.length,
      )
    })

    it("should fail if any messageTemplate has duplicate title", async () => {
      const existingMessageTemplate = createExistingMessageTemplate()
      await businessLogic.create(existingMessageTemplate)

      const messageTemplatesData = createDuplicateMessageTemplatesForBulk()

      await expect(
        businessLogic.bulkCreate(messageTemplatesData),
      ).rejects.toThrow("Duplicate title found: Existing MessageTemplate")

      expect(await dbRepository.getMessageTemplateCount()).toBe(1)
    })

    it("should handle simple messageTemplates creation", async () => {
      const messageTemplatesData = createSimpleMessageTemplates()

      const result = await businessLogic.bulkCreate(messageTemplatesData)

      expect(result).toHaveLength(messageTemplatesData.length)
      expect(await dbRepository.getMessageTemplateCount()).toBe(
        messageTemplatesData.length,
      )
    })
  })

  describe("Bulk Update", () => {
    it("should successfully update multiple messageTemplates", async () => {
      const messageTemplatesData = createMessageTemplatesForBulkUpdate()
      const messageTemplate1 = await businessLogic.create(
        messageTemplatesData[0],
      )
      const messageTemplate2 = await businessLogic.create(
        messageTemplatesData[1],
      )

      const updateData = createBulkUpdateData()
      const updates = [
        { id: messageTemplate1.id, data: updateData[0] },
        { id: messageTemplate2.id, data: updateData[1] },
      ]

      const updatedCount = await businessLogic.bulkUpdate(updates)

      expect(updatedCount).toBe(2)

      const updatedMessageTemplate1 = await businessLogic.getById(
        messageTemplate1.id,
      )
      const updatedMessageTemplate2 = await businessLogic.getById(
        messageTemplate2.id,
      )

      expect(updatedMessageTemplate1?.title).toBe(updateData[0].title)
      expect(updatedMessageTemplate1?.updatedBy).toBe(updateData[0].updatedBy)
      expect(updatedMessageTemplate2?.title).toBe(updateData[1].title)
      expect(updatedMessageTemplate2?.updatedBy).toBe(updateData[1].updatedBy)
    })

    it("should fail if any messageTemplate doesn't exist", async () => {
      const messageTemplateData = createMessageTemplatesForBulkUpdate()[0]
      const messageTemplate1 = await businessLogic.create(messageTemplateData)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: messageTemplate1.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ]

      await expect(businessLogic.bulkUpdate(updates)).rejects.toThrow()
    })

    it("should fail if any update would create duplicate title", async () => {
      const messageTemplatesData = createMessageTemplatesForBulkUpdate()
      await businessLogic.create(messageTemplatesData[0]) // Create first messageTemplate
      const messageTemplate2 = await businessLogic.create(
        messageTemplatesData[1],
      ) // Create second messageTemplate

      const updates = [
        {
          id: messageTemplate2.id,
          data: { title: messageTemplatesData[0].title, updatedBy: "admin" }, // Try to update second messageTemplate with first messageTemplate's title
        },
      ]

      await expect(businessLogic.bulkUpdate(updates)).rejects.toThrow(
        "Duplicate title in update: MessageTemplate 1",
      )
    })
  })

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple messageTemplates", async () => {
      const messageTemplatesData = createMessageTemplatesForBulkDelete()
      const messageTemplate1 = await businessLogic.create(
        messageTemplatesData[0],
      )
      const messageTemplate2 = await businessLogic.create(
        messageTemplatesData[1],
      )
      const messageTemplate3 = await businessLogic.create(
        messageTemplatesData[2],
      )

      const deletedCount = await businessLogic.bulkDelete([
        messageTemplate1.id,
        messageTemplate2.id,
      ])

      expect(deletedCount).toBe(2)
      expect(await dbRepository.getMessageTemplateCount()).toBe(1) // Only non-deleted

      expect(await businessLogic.getById(messageTemplate1.id)).toBeNull()
      expect(await businessLogic.getById(messageTemplate2.id)).toBeNull()
      expect(await businessLogic.getById(messageTemplate3.id)).not.toBeNull()
    })

    it("should successfully hard delete multiple messageTemplates", async () => {
      const messageTemplatesData = createMessageTemplatesForBulkDelete()
      const messageTemplate1 = await businessLogic.create(
        messageTemplatesData[0],
      )
      const messageTemplate2 = await businessLogic.create(
        messageTemplatesData[1],
      )

      const deletedCount = await businessLogic.bulkDelete(
        [messageTemplate1.id, messageTemplate2.id],
        true,
      )

      expect(deletedCount).toBe(2)
      expect(await dbRepository.getMessageTemplateCount()).toBe(0)
    })

    it("should fail if any messageTemplate doesn't exist", async () => {
      const messageTemplateData = createMessageTemplatesForBulkDelete()[0]
      const messageTemplate1 = await businessLogic.create(messageTemplateData)

      await expect(
        businessLogic.bulkDelete([messageTemplate1.id, "non-existent-id"]),
      ).rejects.toThrow()
    })

    it("should fail with empty messageTemplate IDs", async () => {
      await expect(businessLogic.bulkDelete(["", "valid-id"])).rejects.toThrow(
        "ID is required",
      )
    })
  })
})
