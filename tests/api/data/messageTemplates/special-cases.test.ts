//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateMessageTemplate,
  implHandleGetMessageTemplate,
  implHandleUpdateMessageTemplate,
  implHandleDeleteMessageTemplate,
} from "@/app/api/v1/message-templates/impl"
import {
  createSpecialCharacterMessageTemplate,
  createLongContentMessageTemplate,
  createEdgeCaseConditionsMessageTemplate,
  createComplexActionsMessageTemplate,
  createEmptyOptionalFieldsMessageTemplate,
  createAiLogicMessageTemplate,
} from "./object_creator"

/**
 * Special Cases Tests for MessageTemplates
 *
 * This file contains tests for MessageTemplate-specific functionality that doesn't exist
 * in other features like MessageTemplates or MessageTemplates. These tests focus on:
 * - AI-specific business logic (category, variables, AI decision making)
 * - Complex messageTemplate processing scenarios
 * - Edge cases unique to messageTemplate engines
 * - Special character and unicode handling in messageTemplate contexts
 * - Performance and limits testing for messageTemplate content
 *
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting MessageTemplate-specific complexity.
 */

describe("MessageTemplate Special Cases Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const messageTemplateData = createSpecialCharacterMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.title).toBe(messageTemplateData.title)
      expect(createResult.body.data?.content).toBe(messageTemplateData.content)
      expect(createResult.body.data?.tags).toContain("🚀")
      expect(createResult.body.data?.tags).toContain("Test@Tag")
    })

    it("should search messageTemplates with special characters", async () => {
      const messageTemplateData = createSpecialCharacterMessageTemplate()
      await implHandleCreateMessageTemplate(messageTemplateData, businessLogic)

      const searchResult = await businessLogic.search("José")
      expect(searchResult).toHaveLength(1)
      expect(searchResult[0].title).toBe("José María O'Connor")
    })

    it("should update messageTemplates with special characters", async () => {
      const messageTemplateData = createSpecialCharacterMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )
      const messageTemplateId = createResult.body.data.id

      const updateResult = await implHandleUpdateMessageTemplate(
        messageTemplateId,
        {
          title: "Updated José María 🎯",
          content: "Updated with more symbols ⭐ & emojis 🚀",
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.title).toBe("Updated José María 🎯")
      expect(updateResult.body.data?.content).toBe(
        "Updated with more symbols ⭐ & emojis 🚀",
      )
    })

    it("should delete messageTemplates with special characters", async () => {
      const messageTemplateData = createSpecialCharacterMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )
      const messageTemplateId = createResult.body.data.id

      const deleteResult = await implHandleDeleteMessageTemplate(
        messageTemplateId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetMessageTemplate(
        messageTemplateId,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
    })
  })

  describe("Content Length and Performance", () => {
    it("should handle very long content in all fields", async () => {
      const messageTemplateData = createLongContentMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.title.length).toBeGreaterThan(50)
      expect(createResult.body.data?.content.length).toBeGreaterThan(200)
      expect(createResult.body.data?.category.length).toBe(3)
      expect(createResult.body.data?.variables.length).toBe(4)
    })

    it("should search through long content efficiently", async () => {
      const messageTemplateData = createLongContentMessageTemplate()
      await implHandleCreateMessageTemplate(messageTemplateData, businessLogic)

      const searchResult = await businessLogic.search("extensive")
      expect(searchResult).toHaveLength(1)
      expect(searchResult[0].content).toContain("extensive")
    })
  })

  describe("Complex MessageTemplate Logic (MessageTemplate-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const messageTemplateData = createEdgeCaseConditionsMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.category).toContain(
        "user.age >= 18 && user.age <= 65",
      )
      expect(createResult.body.data?.category).toContain(
        "user.location.country === 'US' || user.location.country === 'CA'",
      )
    })

    it("should handle complex action definitions", async () => {
      const messageTemplateData = createComplexActionsMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.variables).toContain(
        "webhook.call('https://api.example.com/notify')",
      )
      expect(createResult.body.data?.variables).toContain(
        "database.update('user_stats', {last_interaction: now()})",
      )
    })

    it("should handle AI-specific business logic", async () => {
      const messageTemplateData = createAiLogicMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.category).toContain("ai.confidence > 0.8")
      expect(createResult.body.data?.variables).toContain(
        "ai.respond_with_confidence",
      )
      expect(createResult.body.data?.tags).toContain("AI")
      expect(createResult.body.data?.tags).toContain("MachineLearning")
    })
  })

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const messageTemplateData = createEmptyOptionalFieldsMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.content).toBe("")
      expect(createResult.body.data?.tags).toEqual([])
    })

    it("should validate messageTemplate activation logic", async () => {
      const messageTemplateData = createAiLogicMessageTemplate()
      messageTemplateData.isActive = false

      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.isActive).toBe(false)

      // Test activation toggle
      const updateResult = await implHandleUpdateMessageTemplate(
        createResult.body.data.id,
        {
          isActive: true,
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.isActive).toBe(true)
    })

    it("should handle messageTemplate priority and execution order concepts", async () => {
      // This test demonstrates MessageTemplate-specific concepts that don't exist in MessageTemplates/MessageTemplates
      const messageTemplates = [
        createAiLogicMessageTemplate(),
        createEdgeCaseConditionsMessageTemplate(),
        createComplexActionsMessageTemplate(),
      ]

      const createdMessageTemplates = []
      for (const messageTemplate of messageTemplates) {
        const result = await implHandleCreateMessageTemplate(
          messageTemplate,
          businessLogic,
        )
        createdMessageTemplates.push(result.body.data)
      }

      expect(createdMessageTemplates).toHaveLength(3)

      // Verify all messageTemplates are created with proper timestamps for execution order
      for (let i = 1; i < createdMessageTemplates.length; i++) {
        expect(
          new Date(createdMessageTemplates[i].createdAt).getTime(),
        ).toBeGreaterThanOrEqual(
          new Date(createdMessageTemplates[i - 1].createdAt).getTime(),
        )
      }
    })
  })

  describe("MessageTemplate Engine Specific Functionality", () => {
    it("should handle messageTemplate condition parsing and validation", async () => {
      const messageTemplateData = createEdgeCaseConditionsMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that category are stored as-is for later parsing by messageTemplate engine
      const category = createResult.body.data?.category
      expect(category).toBeDefined()
      expect(category?.some((c) => c.includes("&&"))).toBe(true)
      expect(category?.some((c) => c.includes("||"))).toBe(true)
    })

    it("should handle action execution metadata", async () => {
      const messageTemplateData = createComplexActionsMessageTemplate()
      const createResult = await implHandleCreateMessageTemplate(
        messageTemplateData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that variables contain execution metadata
      const variables = createResult.body.data?.variables
      expect(variables?.some((a) => a.includes("webhook.call"))).toBe(true)
      expect(variables?.some((a) => a.includes("database.update"))).toBe(true)
      expect(variables?.some((a) => a.includes("category.send"))).toBe(true)
    })
  })
})
