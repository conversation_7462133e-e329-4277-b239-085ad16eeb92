//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic"
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository"
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateMessageTemplate,
  implHandleGetAllMessageTemplates,
} from "@/app/api/v1/message-templates/impl"
import {
  createMultipleMessageTemplates,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated MessageTemplate API Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface
  let dbRepository: TestMessageTemplateDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("MessageTemplate")
    await driver.connect()
    const originalDb = new MongoMessageTemplateRepository(driver)
    dbRepository = new TestMessageTemplateDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new MessageTemplateBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testMessageTemplates = createMultipleMessageTemplates()

  describe("implHandleGetAllMessageTemplates - Consolidated Function", () => {
    beforeEach(async () => {
      for (const messageTemplatesData of testMessageTemplates) {
        await implHandleCreateMessageTemplate(
          messageTemplatesData,
          businessLogic,
        )
      }
    })

    it("should get all messageTemplates when no parameters provided", async () => {
      const result = await implHandleGetAllMessageTemplates(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search messageTemplates by title", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe MessageTemplate and Bob Johnson MessageTemplate

      const titles = result.body.data?.items.map((c: any) => c.title)
      expect(titles).toContain(testMessageTemplates[0].title) // John Doe MessageTemplate
      expect(titles).toContain(testMessageTemplates[2].title) // Bob Johnson MessageTemplate
    })

    it("should search messageTemplates by content", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All messageTemplates have "processing" in content
    })

    it("should filter messageTemplates by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe MessageTemplate and Bob Johnson MessageTemplate

      const titles = result.body.data?.items.map((c: any) => c.title)
      expect(titles).toContain(testMessageTemplates[0].title) // John Doe MessageTemplate
      expect(titles).toContain(testMessageTemplates[2].title) // Bob Johnson MessageTemplate
    })

    it("should filter messageTemplates by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe MessageTemplate and Jane Smith MessageTemplate

      const titles = result.body.data?.items.map((c: any) => c.title)
      expect(titles).toContain(testMessageTemplates[0].title) // John Doe MessageTemplate
      expect(titles).toContain(testMessageTemplates[1].title) // Jane Smith MessageTemplate
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by title", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const titles = result.body.data?.items.map((c: any) => c.title)
      expect(titles![0]).toBe(testMessageTemplates[3].title) // Alice Brown MessageTemplate
      expect(titles![1]).toBe(testMessageTemplates[2].title) // Bob Johnson MessageTemplate
      expect(titles![2]).toBe(testMessageTemplates[1].title) // Jane Smith MessageTemplate
      expect(titles![3]).toBe(testMessageTemplates[0].title) // John Doe MessageTemplate
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP messageTemplates (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted messageTemplates when specified", async () => {
      // Soft delete one messageTemplates
      const allMessageTemplates = await businessLogic.getAll()
      const messageTemplatesToDelete = allMessageTemplates.items[0]
      await businessLogic.delete(messageTemplatesToDelete.id)

      // Get all without including deleted
      const resultWithoutDeleted =
        await implHandleGetAllMessageTemplates(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllMessageTemplates(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
