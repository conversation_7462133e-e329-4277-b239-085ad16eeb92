// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface"
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic"
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository"
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateRole,
  implHandleGetRole,
  implHandleDeleteRole,
} from "@/app/api/v1/roles/impl"
import {
  createRole,
  createSimpleRoles,
  createComplexRole,
  createMinimalDeleteRole,
  createRolesForDeletionTest,
  createRetryDeleteRole,
} from "./object_creator"

describe("Delete Role API Tests", () => {
  let businessLogic: RoleBusinessLogicInterface
  let dbRepository: TestRoleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role")
    await driver.connect()
    const originalDb = new MongoRoleRepository(driver)
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new RoleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/roles/:id", () => {
    it("should successfully delete an existing role", async () => {
      const rolesData = createRole(5) // John Doe Role
      const createResult = await implHandleCreateRole(rolesData, businessLogic)
      const rolesId = createResult.body.data.id

      const getResult = await implHandleGetRole(rolesId, businessLogic)
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteRole(rolesId, businessLogic)

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe("Role deleted successfully")

      const getAfterDelete = await implHandleGetRole(rolesId, businessLogic)
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify role count decreases after deletion", async () => {
      const rolesData = createSimpleRoles()

      const rolesIds: string[] = []
      for (const data of rolesData) {
        const result = await implHandleCreateRole(data, businessLogic)
        rolesIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getRoleCount()).toBe(3)

      const deleteResult = await implHandleDeleteRole(
        rolesIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getRoleCount()).toBe(2)

      const getDeleted = await implHandleGetRole(rolesIds[1], businessLogic)
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent role", async () => {
      const result = await implHandleDeleteRole(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Role not found")
    })

    it("should fail with empty role ID", async () => {
      const result = await implHandleDeleteRole("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Role ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const rolesData = createComplexRole()
      const result = await implHandleCreateRole(rolesData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteRole(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetRole(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const rolesData = createMinimalDeleteRole()
      const result = await implHandleCreateRole(rolesData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteRole(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetRole(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple roles", async () => {
      const rolesData = createSimpleRoles()

      const ids: string[] = []
      for (const data of rolesData) {
        const res = await implHandleCreateRole(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteRole(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getRoleCount()).toBe(0)
    })

    it("should not affect other roles when deleting one", async () => {
      const rolesData = createRolesForDeletionTest()

      const ids: string[] = []
      for (const data of rolesData) {
        const res = await implHandleCreateRole(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteRole(ids[1], businessLogic)

      const getDeleted = await implHandleGetRole(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetRole(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetRole(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getRoleCount()).toBe(2)
    })

    it("should handle attempting to delete the same role twice", async () => {
      const rolesData = createRetryDeleteRole()
      const result = await implHandleCreateRole(rolesData, businessLogic)
      const id = result.body.data?.id

      const first = await implHandleDeleteRole(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteRole(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
