//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface"
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic"
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository"
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateRole,
  implHandleGetRole,
  implHandleBulkCreateRoles,
  implHandleBulkUpdateRoles,
  implHandleBulkDeleteRoles,
} from "@/app/api/v1/roles/impl"
import {
  createMultipleRoles,
  createSimpleRoles,
  createExistingRole,
  createDuplicateRolesForBulk,
  createRolesForBulkUpdate,
  createBulkUpdateData,
  createRolesForBulkDelete,
} from "./object_creator"

describe("Role Bulk Operations Tests", () => {
  let businessLogic: RoleBusinessLogicInterface
  let dbRepository: TestRoleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role")
    await driver.connect()
    const originalDb = new MongoRoleRepository(driver)
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new RoleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Bulk Create", () => {
    it("should successfully create multiple roles", async () => {
      const rolesData = createMultipleRoles()

      const result = await implHandleBulkCreateRoles(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(rolesData.length)
      expect(result.body.data[0].STRING_FIELD).toBe(rolesData[0].STRING_FIELD)
      expect(result.body.data[1].STRING_FIELD).toBe(rolesData[1].STRING_FIELD)
      expect(result.body.data[2].STRING_FIELD).toBe(rolesData[2].STRING_FIELD)
      expect(await dbRepository.getRoleCount()).toBe(rolesData.length)
    })

    it("should fail if any role has duplicate STRING_FIELD", async () => {
      const existingRole = createExistingRole()
      const createResult = await implHandleCreateRole(
        existingRole,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const rolesData = createDuplicateRolesForBulk()

      const result = await implHandleBulkCreateRoles(rolesData, businessLogic)

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD found: Existing Role",
      )
      expect(await dbRepository.getRoleCount()).toBe(1)
    })

    it("should handle simple roles creation", async () => {
      const rolesData = createSimpleRoles()

      const result = await implHandleBulkCreateRoles(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(rolesData.length)
      expect(await dbRepository.getRoleCount()).toBe(rolesData.length)
    })
  })

  describe("Bulk Update", () => {
    it("should successfully update multiple roles", async () => {
      const rolesData = createRolesForBulkUpdate()
      const createResult1 = await implHandleCreateRole(
        rolesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateRole(
        rolesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateRoles(updates, businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.updatedCount).toBe(2)

      const getResult1 = await implHandleGetRole(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetRole(
        createResult2.body.data.id,
        businessLogic,
      )

      expect(getResult1.body.data?.STRING_FIELD).toBe(
        updateData[0].STRING_FIELD,
      )
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy)
      expect(getResult2.body.data?.STRING_FIELD).toBe(
        updateData[1].STRING_FIELD,
      )
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy)
    })

    it("should fail if any role doesn't exist", async () => {
      const roleData = createRolesForBulkUpdate()[0]
      const createResult = await implHandleCreateRole(roleData, businessLogic)
      expect(createResult.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateRoles(updates, businessLogic)

      expect(result.status).toBe(500)
      expect(result.body.status).toBe("failed")
    })

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const rolesData = createRolesForBulkUpdate()
      const createResult1 = await implHandleCreateRole(
        rolesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateRole(
        rolesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: rolesData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second role with first role's STRING_FIELD
        },
      ]

      const result = await implHandleBulkUpdateRoles(updates, businessLogic)

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD in update: Role 1",
      )
    })
  })

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple roles", async () => {
      const rolesData = createRolesForBulkDelete()
      const createResult1 = await implHandleCreateRole(
        rolesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateRole(
        rolesData[1],
        businessLogic,
      )
      const createResult3 = await implHandleCreateRole(
        rolesData[2],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)
      expect(createResult3.status).toBe(201)

      const result = await implHandleBulkDeleteRoles(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getRoleCount()).toBe(1) // Only non-deleted

      const getResult1 = await implHandleGetRole(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetRole(
        createResult2.body.data.id,
        businessLogic,
      )
      const getResult3 = await implHandleGetRole(
        createResult3.body.data.id,
        businessLogic,
      )

      expect(getResult1.status).toBe(404)
      expect(getResult2.status).toBe(404)
      expect(getResult3.status).toBe(200)
    })

    it("should successfully hard delete multiple roles", async () => {
      const rolesData = createRolesForBulkDelete()
      const createResult1 = await implHandleCreateRole(
        rolesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateRole(
        rolesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const result = await implHandleBulkDeleteRoles(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
        true,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getRoleCount()).toBe(0)
    })

    it("should fail if any role doesn't exist", async () => {
      const roleData = createRolesForBulkDelete()[0]
      const createResult = await implHandleCreateRole(roleData, businessLogic)
      expect(createResult.status).toBe(201)

      const result = await implHandleBulkDeleteRoles(
        [createResult.body.data.id, "non-existent-id"],
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty role IDs", async () => {
      const result = await implHandleBulkDeleteRoles(
        ["", "valid-id"],
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ID at index 0 is required")
    })
  })
})
