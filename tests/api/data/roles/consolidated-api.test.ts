//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface"
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic"
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository"
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateRole,
  implHandleGetAllRoles,
  implHandleDeleteRole,
} from "@/app/api/v1/roles/impl"
import {
  createMultipleRoles,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated Role API Tests", () => {
  let businessLogic: RoleBusinessLogicInterface
  let dbRepository: TestRoleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role")
    await driver.connect()
    const originalDb = new MongoRoleRepository(driver)
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new RoleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testRoles = createMultipleRoles()

  describe("implHandleGetAllRoles - Consolidated Function", () => {
    beforeEach(async () => {
      for (const rolesData of testRoles) {
        await implHandleCreateRole(rolesData, businessLogic)
      }
    })

    it("should get all roles when no parameters provided", async () => {
      const result = await implHandleGetAllRoles(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search roles by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Role and Bob Johnson Role

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testRoles[0].STRING_FIELD) // John Doe Role
      expect(STRING_FIELDs).toContain(testRoles[2].STRING_FIELD) // Bob Johnson Role
    })

    it("should search roles by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All roles have "processing" in ARRAY_FIELD2
    })

    it("should filter roles by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Role and Bob Johnson Role

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testRoles[0].STRING_FIELD) // John Doe Role
      expect(STRING_FIELDs).toContain(testRoles[2].STRING_FIELD) // Bob Johnson Role
    })

    it("should filter roles by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe Role and Jane Smith Role

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testRoles[0].STRING_FIELD) // John Doe Role
      expect(STRING_FIELDs).toContain(testRoles[1].STRING_FIELD) // Jane Smith Role
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs![0]).toBe(testRoles[3].STRING_FIELD) // Alice Brown Role
      expect(STRING_FIELDs![1]).toBe(testRoles[2].STRING_FIELD) // Bob Johnson Role
      expect(STRING_FIELDs![2]).toBe(testRoles[1].STRING_FIELD) // Jane Smith Role
      expect(STRING_FIELDs![3]).toBe(testRoles[0].STRING_FIELD) // John Doe Role
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP roles (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllRoles(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted roles when specified", async () => {
      // Get all roles first to get one to delete
      const allResult = await implHandleGetAllRoles(businessLogic)
      expect(allResult.status).toBe(200)
      const rolesToDelete = allResult.body.data?.items[0]
      expect(rolesToDelete).toBeDefined()

      // Soft delete one roles
      const deleteResult = await implHandleDeleteRole(
        rolesToDelete!.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllRoles(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllRoles(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
