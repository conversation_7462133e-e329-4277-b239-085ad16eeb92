//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface"
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic"
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository"
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { implHandleCreateRole } from "@/app/api/v1/roles/impl"
import {
  createFullRole,
  createMinimalRole,
  createRoleWithDescription,
  createRoleWithTags,
  createRoleWithWhitespace,
  createDuplicateRole,
  createSecondDuplicateRole,
  createInvalidRole,
  createRoleWithManyTags,
  createRoleWithoutDescription,
  createRoleWithEmptyTags,
} from "./object_creator"

describe("Create Role API Tests", () => {
  let businessLogic: RoleBusinessLogicInterface
  let dbRepository: TestRoleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role")
    await driver.connect()
    const originalDb = new MongoRoleRepository(driver)
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new RoleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/roles", () => {
    it("should successfully create a new roles with all fields", async () => {
      const rolesData = createFullRole()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(rolesData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(rolesData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(rolesData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(rolesData.variables)
      expect(result.body.data?.tags).toEqual(rolesData.tags)
      expect(result.body.data?.isActive).toBe(rolesData.isActive)
      expect(result.body.data?.id).toBeDefined()
      expect(result.body.data?.createdAt).toBeDefined()
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should successfully create a roles with minimal required fields", async () => {
      const rolesData = createMinimalRole()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(rolesData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(rolesData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(rolesData.variables)
      expect(result.body.data?.isActive).toBe(true) // Should default to true
      expect(result.body.data?.tags).toEqual([])
    })

    it("should create role with ARRAY_FIELD2", async () => {
      const rolesData = createRoleWithDescription()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.data?.ARRAY_FIELD2).toBe(rolesData.ARRAY_FIELD2)
    })

    it("should create role with tags", async () => {
      const rolesData = createRoleWithTags()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.data?.tags).toEqual(rolesData.tags)
    })

    it("should trim whitespace from STRING_FIELD", async () => {
      const rolesData = createRoleWithWhitespace()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Role")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first roles
      const rolesData1 = createDuplicateRole()
      await implHandleCreateRole(rolesData1, businessLogic)

      // Try to create second roles with same STRING_FIELD
      const rolesData2 = createSecondDuplicateRole()
      const result = await implHandleCreateRole(rolesData2, businessLogic)

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Role with the same STRING_FIELD already exists",
      )
    })

    it("should fail with missing STRING_FIELD", async () => {
      const rolesData = {
        ARRAY_FIELD2: "+6281234567890",
      }

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD2", async () => {
      const rolesData = createInvalidRole("missing-ARRAY_FIELD2")

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD", async () => {
      const rolesData = createInvalidRole("missing-ARRAY_FIELD")

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("ARRAY_FIELD")
    })

    it("should fail with missing variables", async () => {
      const rolesData = createInvalidRole("missing-variables")

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("variables")
    })

    it("should fail with empty ARRAY_FIELD array", async () => {
      const rolesData = createInvalidRole("empty-ARRAY_FIELD")

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty variables array", async () => {
      const rolesData = createInvalidRole("empty-variables")

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should create role with many tags", async () => {
      const rolesData = createRoleWithManyTags()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(rolesData.tags)
    })

    it("should handle optional ARRAY_FIELD2", async () => {
      const rolesData = createRoleWithoutDescription()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.ARRAY_FIELD2).toBe("")
    })

    it("should handle empty arrays for tags", async () => {
      const rolesData = createRoleWithEmptyTags()

      const result = await implHandleCreateRole(rolesData, businessLogic)

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(rolesData.tags)
    })
  })
})
