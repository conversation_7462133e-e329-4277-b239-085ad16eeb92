// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic"
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository"
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateAiRule,
  implHandleGetAiRule,
  implHandleDeleteAiRule,
} from "@/app/api/v1/ai-rules/impl"
import {
  createAiRule,
  createSimpleAiRules,
  createComplexAiRule,
  createMinimalDeleteRule,
  createRulesForDeletionTest,
  createRetryDeleteRule,
} from "./object_creator"

describe("Delete AiRule API Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface
  let dbRepository: TestAiRuleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule")
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver)
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AiRuleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/ai-rules/:id", () => {
    it("should successfully delete an existing aiRule", async () => {
      const aiRulesData = createAiRule(5) // John Doe Rule
      const createResult = await implHandleCreateAiRule(
        aiRulesData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const getResult = await implHandleGetAiRule(aiRulesId, businessLogic)
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteAiRule(
        aiRulesId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe("AiRule deleted successfully")

      const getAfterDelete = await implHandleGetAiRule(aiRulesId, businessLogic)
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify aiRule count decreases after deletion", async () => {
      const aiRulesData = createSimpleAiRules()

      const aiRulesIds: string[] = []
      for (const data of aiRulesData) {
        const result = await implHandleCreateAiRule(data, businessLogic)
        aiRulesIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getAiRuleCount()).toBe(3)

      const deleteResult = await implHandleDeleteAiRule(
        aiRulesIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getAiRuleCount()).toBe(2)

      const getDeleted = await implHandleGetAiRule(aiRulesIds[1], businessLogic)
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent aiRule", async () => {
      const result = await implHandleDeleteAiRule(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("AI Rule not found")
    })

    it("should fail with empty aiRule ID", async () => {
      const result = await implHandleDeleteAiRule("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("AiRule ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const aiRulesData = createComplexAiRule()
      const result = await implHandleCreateAiRule(aiRulesData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteAiRule(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetAiRule(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const aiRulesData = createMinimalDeleteRule()
      const result = await implHandleCreateAiRule(aiRulesData, businessLogic)
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteAiRule(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetAiRule(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple aiRules", async () => {
      const aiRulesData = createSimpleAiRules()

      const ids: string[] = []
      for (const data of aiRulesData) {
        const res = await implHandleCreateAiRule(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteAiRule(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getAiRuleCount()).toBe(0)
    })

    it("should not affect other aiRules when deleting one", async () => {
      const aiRulesData = createRulesForDeletionTest()

      const ids: string[] = []
      for (const data of aiRulesData) {
        const res = await implHandleCreateAiRule(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteAiRule(ids[1], businessLogic)

      const getDeleted = await implHandleGetAiRule(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetAiRule(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetAiRule(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getAiRuleCount()).toBe(2)
    })

    it("should handle attempting to delete the same aiRule twice", async () => {
      const aiRulesData = createRetryDeleteRule()
      const result = await implHandleCreateAiRule(aiRulesData, businessLogic)
      const id = result.body.data?.id

      const first = await implHandleDeleteAiRule(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteAiRule(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
