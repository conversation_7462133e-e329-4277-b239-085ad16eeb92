//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic"
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository"
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateAiRule,
  implHandleGetAiRule,
  implHandleUpdateAiRule,
  implHandleDeleteAiRule,
  implHandleGetAllAiRules,
} from "@/app/api/v1/ai-rules/impl"
import {
  createSpecialCharacterRule,
  createLongContentRule,
  createEdgeCaseConditionsRule,
  createComplexActionsRule,
  createEmptyOptionalFieldsRule,
  createAiLogicRule,
} from "./object_creator"

/**
 * Special Cases Tests for AiRules
 *
 * This file contains tests for AiRule-specific functionality that doesn't exist
 * in other features like Contacts or MessageTemplates. These tests focus on:
 * - AI-specific business logic (conditions, actions, AI decision making)
 * - Complex rule processing scenarios
 * - Edge cases unique to rule engines
 * - Special character and unicode handling in rule contexts
 * - Performance and limits testing for rule content
 *
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting AiRule-specific complexity.
 */

describe("AiRule Special Cases Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface
  let dbRepository: TestAiRuleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule")
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver)
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AiRuleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const ruleData = createSpecialCharacterRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.name).toBe(ruleData.name)
      expect(createResult.body.data?.description).toBe(ruleData.description)
      expect(createResult.body.data?.tags).toContain("🚀")
      expect(createResult.body.data?.tags).toContain("Test@Tag")
    })

    it("should search rules with special characters", async () => {
      const ruleData = createSpecialCharacterRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllAiRules(businessLogic, {
        search: "José",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].name).toBe("José María O'Connor")
    })

    it("should update rules with special characters", async () => {
      const ruleData = createSpecialCharacterRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)
      const ruleId = createResult.body.data.id

      const updateResult = await implHandleUpdateAiRule(
        ruleId,
        {
          name: "Updated José María 🎯",
          description: "Updated with more symbols ⭐ & emojis 🚀",
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.name).toBe("Updated José María 🎯")
      expect(updateResult.body.data?.description).toBe(
        "Updated with more symbols ⭐ & emojis 🚀",
      )
    })

    it("should delete rules with special characters", async () => {
      const ruleData = createSpecialCharacterRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)
      const ruleId = createResult.body.data.id

      const deleteResult = await implHandleDeleteAiRule(ruleId, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetAiRule(ruleId, businessLogic)
      expect(getResult.status).toBe(404)
    })
  })

  describe("Content Length and Performance", () => {
    it("should handle very long content in all fields", async () => {
      const ruleData = createLongContentRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.name.length).toBeGreaterThan(50)
      expect(createResult.body.data?.description.length).toBeGreaterThan(200)
      expect(createResult.body.data?.conditions.length).toBe(3)
      expect(createResult.body.data?.actions.length).toBe(4)
    })

    it("should search through long content efficiently", async () => {
      const ruleData = createLongContentRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllAiRules(businessLogic, {
        search: "extensive",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].description).toContain("extensive")
    })
  })

  describe("Complex Rule Logic (AiRule-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const ruleData = createEdgeCaseConditionsRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.conditions).toContain(
        "user.age >= 18 && user.age <= 65",
      )
      expect(createResult.body.data?.conditions).toContain(
        "user.location.country === 'US' || user.location.country === 'CA'",
      )
    })

    it("should handle complex action definitions", async () => {
      const ruleData = createComplexActionsRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.actions).toContain(
        "webhook.call('https://api.example.com/notify')",
      )
      expect(createResult.body.data?.actions).toContain(
        "database.update('user_stats', {last_interaction: now()})",
      )
    })

    it("should handle AI-specific business logic", async () => {
      const ruleData = createAiLogicRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.conditions).toContain(
        "ai.confidence > 0.8",
      )
      expect(createResult.body.data?.actions).toContain(
        "ai.respond_with_confidence",
      )
      expect(createResult.body.data?.tags).toContain("AI")
      expect(createResult.body.data?.tags).toContain("MachineLearning")
    })
  })

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const ruleData = createEmptyOptionalFieldsRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.description).toBe("")
      expect(createResult.body.data?.tags).toEqual([])
    })

    it("should validate rule activation logic", async () => {
      const ruleData = createAiLogicRule()
      ruleData.isActive = false

      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)
      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.isActive).toBe(false)

      // Test activation toggle
      const updateResult = await implHandleUpdateAiRule(
        createResult.body.data.id,
        {
          isActive: true,
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.isActive).toBe(true)
    })

    it("should handle rule priority and execution order concepts", async () => {
      // This test demonstrates AiRule-specific concepts that don't exist in Contacts/MessageTemplates
      const rules = [
        createAiLogicRule(),
        createEdgeCaseConditionsRule(),
        createComplexActionsRule(),
      ]

      const createdRules = []
      for (const rule of rules) {
        const result = await implHandleCreateAiRule(rule, businessLogic)
        createdRules.push(result.body.data)
      }

      expect(createdRules).toHaveLength(3)

      // Verify all rules are created with proper timestamps for execution order
      for (let i = 1; i < createdRules.length; i++) {
        expect(
          new Date(createdRules[i].createdAt).getTime(),
        ).toBeGreaterThanOrEqual(
          new Date(createdRules[i - 1].createdAt).getTime(),
        )
      }
    })
  })

  describe("Rule Engine Specific Functionality", () => {
    it("should handle rule condition parsing and validation", async () => {
      const ruleData = createEdgeCaseConditionsRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)

      // Test that conditions are stored as-is for later parsing by rule engine
      const conditions = createResult.body.data?.conditions
      expect(conditions).toBeDefined()
      expect(conditions?.some((c) => c.includes("&&"))).toBe(true)
      expect(conditions?.some((c) => c.includes("||"))).toBe(true)
    })

    it("should handle action execution metadata", async () => {
      const ruleData = createComplexActionsRule()
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic)

      expect(createResult.status).toBe(201)

      // Test that actions contain execution metadata
      const actions = createResult.body.data?.actions
      expect(actions?.some((a) => a.includes("webhook.call"))).toBe(true)
      expect(actions?.some((a) => a.includes("database.update"))).toBe(true)
      expect(actions?.some((a) => a.includes("email.send"))).toBe(true)
    })
  })
})
