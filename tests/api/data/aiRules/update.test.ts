//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic"
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository"
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateAiRule,
  implHandleUpdateAiRule,
  implHandleDeleteAiRule,
} from "@/app/api/v1/ai-rules/impl"
import {
  createFullAiRule,
  createMinimalAiRule,
  createFullAiRuleUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createRuleForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createRuleForTrimming,
  createActiveRule,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update AiRule API Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface
  let dbRepository: TestAiRuleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule")
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver)
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AiRuleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/ai-rules/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createFullAiRuleUpdate()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.name).toBe(updateData.name)
      expect(result.body.data?.description).toBe(updateData.description)
      expect(result.body.data?.conditions).toEqual(updateData.conditions)
      expect(result.body.data?.actions).toEqual(updateData.actions)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the name", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.name).toBe(updateData.name)
      expect(result.body.data?.conditions).toEqual(createData.conditions)
      expect(result.body.data?.actions).toEqual(createData.actions)
    })

    it("should trim name when updating", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.name).toBe("Trimmed Name")
    })

    it("should fail to update non-existent rule", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateAiRule(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("AI Rule not found")
    })

    it("should fail with invalid input: empty name", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-name")
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty conditions", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-conditions")
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateAiRule("", updateData, businessLogic)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("AiRule ID is required")
    })

    it("should fail with duplicate name", async () => {
      // Create first rule
      const createData1 = createMinimalAiRule()
      await implHandleCreateAiRule(createData1, businessLogic)

      // Create second rule
      const createData2 = createFullAiRule()
      const createResult2 = await implHandleCreateAiRule(
        createData2,
        businessLogic,
      )

      // Try to update second rule with first rule's name
      const updateData = createDuplicateNameUpdate(createData1.name)
      const result = await implHandleUpdateAiRule(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another AI Rule with this name exists",
      )
    })

    it("should allow updating rule with same name (no change)", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.name).toBe(updateData.name)
      expect(result.body.data?.description).toBe(updateData.description)
    })

    it("should fail to update soft-deleted rule", async () => {
      const createData = createRuleForSoftDelete()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      // Soft delete the rule
      const deleteResult = await implHandleDeleteAiRule(
        aiRulesId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Try to update the soft-deleted rule
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("AI Rule not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createRuleForTrimming()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.name).toBe("Trimmed Name")
      expect(result.body.data?.description).toBe("Trimmed Description")
      expect(result.body.data?.conditions).toEqual(["trimmed_condition"])
      expect(result.body.data?.actions).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty actions", async () => {
      const createData = createMinimalAiRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-actions")
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveRule()
      const createResult = await implHandleCreateAiRule(
        createData,
        businessLogic,
      )
      const aiRulesId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateAiRule(
        aiRulesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
