// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic"
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository"
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateAiRule,
  implHandleGetAiRule,
  implHandleGetAllAiRules,
} from "@/app/api/v1/ai-rules/impl"
import {
  createAiRule,
  createSimpleAiRules,
  createAiRulesWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read AiRule API Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface
  let dbRepository: TestAiRuleDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule")
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver)
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver)
    businessLogic = new AiRuleBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/ai-rules/:id", () => {
    it("should successfully get aiRule by ID", async () => {
      const aiRule = createAiRule(5) // John Doe Rule

      const createResult = await implHandleCreateAiRule(aiRule, businessLogic)
      const id = createResult.body.data.id

      const result = await implHandleGetAiRule(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.name).toBe(aiRule.name)
    })

    it("should fail to get non-existent aiRule", async () => {
      const result = await implHandleGetAiRule(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty aiRule ID", async () => {
      const result = await implHandleGetAiRule("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only aiRule ID", async () => {
      const result = await implHandleGetAiRule("   ", businessLogic)
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/ai-rules", () => {
    it("should get all aiRules", async () => {
      const rules = createSimpleAiRules()
      for (const r of rules) await implHandleCreateAiRule(r, businessLogic)

      const result = await implHandleGetAllAiRules(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no aiRules exist", async () => {
      const result = await implHandleGetAllAiRules(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/ai-rules/search", () => {
    beforeEach(async () => {
      const data = createAiRulesWithTags()
      for (const r of data) await implHandleCreateAiRule(r, businessLogic)
    })

    it("should search by name", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/ai-rules/filters", () => {
    beforeEach(async () => {
      const data = createAiRulesWithTags()
      for (const r of data) await implHandleCreateAiRule(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllAiRules(businessLogic, params)
      expect(result.status).toBe(400)
    })
  })
})
