//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetConversationMessage,
  implHandleBulkCreateConversationMessages,
  implHandleBulkUpdateConversationMessages,
  implHandleBulkDeleteConversationMessages,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createMultipleConversationMessages,
  createSimpleConversationMessages,
  createExistingConversationMessage,
  createDuplicateConversationMessagesForBulk,
  createConversationMessagesForBulkUpdate,
  createBulkUpdateData,
  createConversationMessagesForBulkDelete,
} from "./object_creator"

describe("ConversationMessage Bulk Operations Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Bulk Create", () => {
    it("should successfully create multiple conversationMessages", async () => {
      const conversationMessagesData = createConversationipleConversationMessages()

      const result = await implHandleBulkCreateConversationMessages(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(conversationMessagesData.length)
      expect(result.body.data[0].STRING_FIELD).toBe(
        conversationMessagesData[0].STRING_FIELD,
      )
      expect(result.body.data[1].STRING_FIELD).toBe(
        conversationMessagesData[1].STRING_FIELD,
      )
      expect(result.body.data[2].STRING_FIELD).toBe(
        conversationMessagesData[2].STRING_FIELD,
      )
      expect(await dbRepository.getConversationMessageCount()).toBe(
        conversationMessagesData.length,
      )
    })

    it("should fail if any conversationMessage has duplicate STRING_FIELD", async () => {
      const existingConversationMessage = createExistingConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        existingConversationMessage,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const conversationMessagesData = createDConversationcateConversationMessagesForBulk()

      const result = await implHandleBulkCreateConversationMessages(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD found: Existing ConversationMessage",
      )
      expect(await dbRepository.getConversationMessageCount()).toBe(1)
    })

    it("should handle simple conversationMessages creation", async () => {
      const conversationMessagesData = creaConversationmpleConversationMessages()

      const result = await implHandleBulkCreateConversationMessages(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(conversationMessagesData.length)
      expect(await dbRepository.getConversationMessageCount()).toBe(
        conversationMessagesData.length,
      )
    })
  })

  describe("Bulk Update", () => {
    it("should successfully update multiple conversationMessages", async () => {
      const conversationMessagesData ConversationeateConversationMessagesForBulkUpdate()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessagesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessagesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateConversationMessages(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.updatedCount).toBe(2)

      const getResult1 = await implHandleGetConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetConversationMessage(
        createResult2.body.data.id,
        businessLogic,
      )

      expect(getResult1.body.data?.STRING_FIELD).toBe(
        updateData[0].STRING_FIELD,
      )
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy)
      expect(getResult2.body.data?.STRING_FIELD).toBe(
        updateData[1].STRING_FIELD,
      )
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy)
    })

    it("should fail if any conversationMessage doesn't exist", async () => {
      const conversationMessageData ConversationeateConversationMessagesForBulkUpdate()[0]
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateConversationMessages(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(500)
      expect(result.body.status).toBe("failed")
    })

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const conversationMessagesData ConversationeateConversationMessagesForBulkUpdate()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessagesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessagesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updates = [
        {
          id: createResult2.body.data.id,
          data: {
            STRING_FIELD: conversationMessagesData[0].STRING_FIELD,
            updatedBy: "admin",
          }, // Try to update second conversationMessage with first conversationMessage's STRING_FIELD
        },
      ]

      const result = await implHandleBulkUpdateConversationMessages(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD in update: ConversationMessage 1",
      )
    })
  })

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple conversationMessages", async () => {
      const conversationMessagesData ConversationeateConversationMessagesForBulkDelete()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessagesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessagesData[1],
        businessLogic,
      )
      const createResult3 = await implHandleCreateConversationMessage(
        conversationMessagesData[2],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)
      expect(createResult3.status).toBe(201)

      const result = await implHandleBulkDeleteConversationMessages(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getConversationMessageCount()).toBe(1) // Only non-deleted

      const getResult1 = await implHandleGetConversationMessage(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetConversationMessage(
        createResult2.body.data.id,
        businessLogic,
      )
      const getResult3 = await implHandleGetConversationMessage(
        createResult3.body.data.id,
        businessLogic,
      )

      expect(getResult1.status).toBe(404)
      expect(getResult2.status).toBe(404)
      expect(getResult3.status).toBe(200)
    })

    it("should successfully hard delete multiple conversationMessages", async () => {
      const conversationMessagesData ConversationeateConversationMessagesForBulkDelete()
      const createResult1 = await implHandleCreateConversationMessage(
        conversationMessagesData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateConversationMessage(
        conversationMessagesData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const result = await implHandleBulkDeleteConversationMessages(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
        true,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getConversationMessageCount()).toBe(0)
    })

    it("should fail if any conversationMessage doesn't exist", async () => {
      const conversationMessageData ConversationeateConversationMessagesForBulkDelete()[0]
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const result = await implHandleBulkDeleteConversationMessages(
        [createResult.body.data.id, "non-existent-id"],
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty conversationMessage IDs", async () => {
      const result = await implHandleBulkDeleteConversationMessages(
        ["", "valid-id"],
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ID at index 0 is required")
    })
  })
})
