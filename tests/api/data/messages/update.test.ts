//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleUpdateConversationMessage,
  implHandleDeleteConversationMessage,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createFullConversationMessage,
  createMinimalConversationMessage,
  createFullConversationMessageUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createConversationMessageForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createConversationMessageForTrimming,
  createActiveConversationMessage,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update ConversationMessage API Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/conversations/[conversationId]/messages/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createFullConversationMessageUpdate()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(updateData.variables)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(createData.variables)
    })

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
    })

    it("should fail to update non-existent conversationMessage", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversationMessage(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ConversationMessage not found")
    })

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-STRING_FIELD")
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-ARRAY_FIELD")
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateConversationMessage(
        "",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ConversationMessage ID is required")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first conversationMessage
      const createData1 = createMinimalConversationMessage()
      await implHandleCreateConversationMessage(createData1, businessLogic)

      // Create second conversationMessage
      const createData2 = createFullConversationMessage()
      const createResult2 = await implHandleCreateConversationMessage(
        createData2,
        businessLogic,
      )

      // Try to update second conversationMessage with first conversationMessage's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD)
      const result = await implHandleUpdateConversationMessage(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another ConversationMessage with this STRING_FIELD exists",
      )
    })

    it("should allow updating conversationMessage with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
    })

    it("should fail to update soft-deleted conversationMessage", async () => {
      const createData = createConversationMessageForSoftDelete()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      // Soft delete the conversationMessage
      const deleteResult = await implHandleDeleteConversationMessage(
        conversationMessagesId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Try to update the soft-deleted conversationMessage
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ConversationMessage not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createConversationMessageForTrimming()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description")
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"])
      expect(result.body.data?.variables).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-variables")
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        createData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateConversationMessage(
        conversationMessagesId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
