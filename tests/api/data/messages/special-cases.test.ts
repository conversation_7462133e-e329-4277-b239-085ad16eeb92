//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetConversationMessage,
  implHandleUpdateConversationMessage,
  implHandleDeleteConversationMessage,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createSpecialCharacterConversationMessage,
  createLongContentConversationMessage,
  createEdgeCaseConditionsConversationMessage,
  createComplexActionsConversationMessage,
  createEmptyOptionalFieldsConversationMessage,
  createAiLogicConversationMessage,
} from "./object_creator"

/**
 * Special Cases Tests for ConversationMessages
 *
 * This file contains tests for ConversationMessage-specific functionality that doesn't exist
 * in other features like ConversationMessages or ConversationMessages. These tests focus on:
 * - AI-specific business logic (ARRAY_FIELD, variables, AI decision making)
 * - Complex conversationMessage processing scenarios
 * - Edge cases unique to conversationMessage engines
 * - Special character and unicode handling in conversationMessage contexts
 * - Performance and limits testing for conversationMessage ARRAY_FIELD2
 *
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting ConversationMessage-specific complexity.
 */

describe("ConversationMessage Special Cases Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const conversationMessageData =
        createSpecialCConversationcterConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.STRING_FIELD).toBe(
        conversationMessageData.STRING_FIELD,
      )
      expect(createResult.body.data?.ARRAY_FIELD2).toBe(
        conversationMessageData.ARRAY_FIELD2,
      )
      expect(createResult.body.data?.tags).toContain("🚀")
      expect(createResult.body.data?.tags).toContain("Test@Tag")
    })

    it("should search conversationMessages with special characters", async () => {
      const conversationMessageData =
        createSpecialCConversationcterConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllConversationMessages(
        businessLogic,
        {
          search: "José",
        },
      )
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].STRING_FIELD).toBe("José María O'Connor")
    })

    it("should update conversationMessages with special characters", async () => {
      const conversationMessageData =
        createSpecialCConversationcterConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      const conversationMessageId = createResult.body.data.id

      const updateResult = await implHandleUpdateConversationMessage(
        conversationMessageId,
        {
          STRING_FIELD: "Updated José María 🎯",
          ARRAY_FIELD2: "Updated with more symbols ⭐ & emojis 🚀",
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.STRING_FIELD).toBe("Updated José María 🎯")
      expect(updateResult.body.data?.ARRAY_FIELD2).toBe(
        "Updated with more symbols ⭐ & emojis 🚀",
      )
    })

    it("should delete conversationMessages with special characters", async () => {
      const conversationMessageData =
        createSpecialCConversationcterConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      const conversationMessageId = createResult.body.data.id

      const deleteResult = await implHandleDeleteConversationMessage(
        conversationMessageId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetConversationMessage(
        conversationMessageId,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
    })
  })

  describe("Content Length and Performance", () => {
    it("should handle very long ARRAY_FIELD2 in all fields", async () => {
      const conversationMessageData =
        createLonConversationtentConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.status).toBe("success")
      expect(createResult.body.data?.STRING_FIELD.length).toBeGreaterThan(50)
      expect(createResult.body.data?.ARRAY_FIELD2.length).toBeGreaterThan(200)
      expect(createResult.body.data?.ARRAY_FIELD.length).toBe(3)
      expect(createResult.body.data?.variables.length).toBe(4)
    })

    it("should search through long ARRAY_FIELD2 efficiently", async () => {
      const conversationMessageData =
        createLonConversationtentConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const searchResult = await implHandleGetAllConversationMessages(
        businessLogic,
        {
          search: "extensive",
        },
      )
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(1)
      expect(searchResult.body.data[0].ARRAY_FIELD2).toContain("extensive")
    })
  })

  describe("Complex ConversationMessage Logic (ConversationMessage-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const conversationMessageData =
        createEdgeCaseCoConversationionsConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "user.age >= 18 && user.age <= 65",
      )
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "user.location.country === 'US' || user.location.country === 'CA'",
      )
    })

    it("should handle complex action definitions", async () => {
      const conversationMessageData =
        createCompleConversationionsConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.variables).toContain(
        "webhook.call('https://api.example.com/notify')",
      )
      expect(createResult.body.data?.variables).toContain(
        "database.update('user_stats', {last_interaction: now()})",
      )
    })

    it("should handle AI-specific business logic", async () => {
      const conversationMessageData = creatConversationogicConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD).toContain(
        "ai.confidence > 0.8",
      )
      expect(createResult.body.data?.variables).toContain(
        "ai.respond_with_confidence",
      )
      expect(createResult.body.data?.tags).toContain("AI")
      expect(createResult.body.data?.tags).toContain("MachineLearning")
    })
  })

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const conversationMessageData =
        createEmptyOptionConversationeldsConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.ARRAY_FIELD2).toBe("")
      expect(createResult.body.data?.tags).toEqual([])
    })

    it("should validate conversationMessage activation logic", async () => {
      const conversationMessageData = creatConversationogicConversationMessage()
      conversationMessageData.isActive = false

      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)
      expect(createResult.body.data?.isActive).toBe(false)

      // Test activation toggle
      const updateResult = await implHandleUpdateConversationMessage(
        createResult.body.data.id,
        {
          isActive: true,
          updatedBy: "admin",
        },
        businessLogic,
      )

      expect(updateResult.status).toBe(200)
      expect(updateResult.body.data?.isActive).toBe(true)
    })

    it("should handle conversationMessage priority and execution order concepts", async () => {
      // This test demonstrates ConversationMessage-specific concepts that don't exist in ConversationMessages/ConversationMessages
      const conversationMessages = [
        createAiLogicConversationMessage(),
        createEdgeCaseConditionsConversationMessage(),
        createComplexActionsConversationMessage(),
      ]

      const createdConversationMessages = []
      for (const conversationMessage of conversationMessages) {
        const result = await implHandleCreateConversationMessage(
          conversationMessage,
          businessLogic,
        )
        createdConversationMessages.push(result.body.data)
      }

      expect(createdConversationMessages).toHaveLength(3)

      // Verify all conversationMessages are created with proper timestamps for execution order
      for (let i = 1; i < createdConversationMessages.length; i++) {
        expect(
          new Date(createdConversationMessages[i].createdAt).getTime(),
        ).toBeGreaterThanOrEqual(
          new Date(createdConversationMessages[i - 1].createdAt).getTime(),
        )
      }
    })
  })

  describe("ConversationMessage Engine Specific Functionality", () => {
    it("should handle conversationMessage condition parsing and validation", async () => {
      const conversationMessageData =
        createEdgeCaseCoConversationionsConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that ARRAY_FIELD are stored as-is for later parsing by conversationMessage engine
      const ARRAY_FIELD = createResult.body.data?.ARRAY_FIELD
      expect(ARRAY_FIELD).toBeDefined()
      expect(ARRAY_FIELD?.some((c) => c.includes("&&"))).toBe(true)
      expect(ARRAY_FIELD?.some((c) => c.includes("||"))).toBe(true)
    })

    it("should handle action execution metadata", async () => {
      const conversationMessageData =
        createCompleConversationionsConversationMessage()
      const createResult = await implHandleCreateConversationMessage(
        conversationMessageData,
        businessLogic,
      )

      expect(createResult.status).toBe(201)

      // Test that variables contain execution metadata
      const variables = createResult.body.data?.variables
      expect(variables?.some((a) => a.includes("webhook.call"))).toBe(true)
      expect(variables?.some((a) => a.includes("database.update"))).toBe(true)
      expect(variables?.some((a) => a.includes("ARRAY_FIELD.send"))).toBe(true)
    })
  })
})
