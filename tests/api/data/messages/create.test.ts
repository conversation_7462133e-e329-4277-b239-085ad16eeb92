//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { implHandleCreateConversationMessage } from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createFullConversationMessage,
  createMinimalConversationMessage,
  createConversationMessageWithDescription,
  createConversationMessageWithTags,
  createConversationMessageWithWhitespace,
  createDuplicateConversationMessage,
  createSecondDuplicateConversationMessage,
  createInvalidConversationMessage,
  createConversationMessageWithManyTags,
  createConversationMessageWithoutDescription,
  createConversationMessageWithEmptyTags,
} from "./object_creator"

describe("Create ConversationMessage API Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/conversations/[conversationId]/messages", () => {
    it("should successfully create a new conversationMessages with all fields", async () => {
      const conversationMessagesData = crConversationFullConversationMessage()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(conversationMessagesData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(conversationMessagesData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(
        conversationMessagesData.ARRAY_FIELD,
      )
      expect(result.body.data?.variables).toEqual(conversationMessagesData.variables)
      expect(result.body.data?.tags).toEqual(conversationMessagesData.tags)
      expect(result.body.data?.isActive).toBe(conversationMessagesData.isActive)
      expect(result.body.data?.id).toBeDefined()
      expect(result.body.data?.createdAt).toBeDefined()
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should successfully create a conversationMessages with minimal required fields", async () => {
      const conversationMessagesData = creatConversationimalConversationMessage()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(conversationMessagesData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(
        conversationMessagesData.ARRAY_FIELD,
      )
      expect(result.body.data?.variables).toEqual(conversationMessagesData.variables)
      expect(result.body.data?.isActive).toBe(true) // Should default to true
      expect(result.body.data?.tags).toEqual([])
    })

    it("should create conversationMessage with ARRAY_FIELD2", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithDescription()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.ARRAY_FIELD2).toBe(conversationMessagesData.ARRAY_FIELD2)
    })

    it("should create conversationMessage with tags", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithTags()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.tags).toEqual(conversationMessagesData.tags)
    })

    it("should trim whitespace from STRING_FIELD", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithWhitespace()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed ConversationMessage")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first conversationMessages
      const conversationMessagesData1 = createDConversationcateConversationMessage()
      await implHandleCreateConversationMessage(conversationMessagesData1, businessLogic)

      // Try to create second conversationMessages with same STRING_FIELD
      const conversationMessagesData2 = createSecondDConversationcateConversationMessage()
      const result = await implHandleCreateConversationMessage(
        conversationMessagesData2,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "ConversationMessage with the same STRING_FIELD already exists",
      )
    })

    it("should fail with missing STRING_FIELD", async () => {
      const conversationMessagesData = {
        ARRAY_FIELD2: "+6281234567890",
      }

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD2", async () => {
      const conversationMessagesData = creatConversationalidConversationMessage("missing-ARRAY_FIELD2")

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD", async () => {
      const conversationMessagesData = creatConversationalidConversationMessage("missing-ARRAY_FIELD")

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("ARRAY_FIELD")
    })

    it("should fail with missing variables", async () => {
      const conversationMessagesData = creatConversationalidConversationMessage("missing-variables")

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("variables")
    })

    it("should fail with empty ARRAY_FIELD array", async () => {
      const conversationMessagesData = creatConversationalidConversationMessage("empty-ARRAY_FIELD")

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty variables array", async () => {
      const conversationMessagesData = creatConversationalidConversationMessage("empty-variables")

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should create conversationMessage with many tags", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithManyTags()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(conversationMessagesData.tags)
    })

    it("should handle optional ARRAY_FIELD2", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithoutDescription()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.ARRAY_FIELD2).toBe("")
    })

    it("should handle empty arrays for tags", async () => {
      const conversationMessagesData ConversationeateConversationMessageWithEmptyTags()

      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(conversationMessagesData.tags)
    })
  })
})
