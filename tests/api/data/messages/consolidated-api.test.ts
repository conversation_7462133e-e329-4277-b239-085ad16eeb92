//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetAllConversationMessages,
  implHandleDeleteConversationMessage,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createMultipleConversationMessages,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams,
} from "./object_creator"

describe("Consolidated ConversationMessage API Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  const testConversationMessages = createMultipleConversationMessages()

  describe("implHandleGetAllConversationMessages - Consolidated Function", () => {
    beforeEach(async () => {
      for (const conversationMessagesDataConversationtestConversationMessages) {
        await implHandleCreateConversationMessage(conversationMessagesData, businessLogic)
      }
    })

    it("should get all conversationMessages when no parameters provided", async () => {
      const result = await implHandleGetAllConversationMessages(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)
      expect(result.body.data?.total).toBe(4)
    })

    it("should search conversationMessages by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe ConversationMessage and Bob Johnson ConversationMessage

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversationMessages[0].STRING_FIELD) // John Doe ConversationMessage
      expect(STRING_FIELDs).toContain(testConversationMessages[2].STRING_FIELD) // Bob Johnson ConversationMessage
    })

    it("should search conversationMessages by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4) // All conversationMessages have "processing" in ARRAY_FIELD2
    })

    it("should filter conversationMessages by tag", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe ConversationMessage and Bob Johnson ConversationMessage

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversationMessages[0].STRING_FIELD) // John Doe ConversationMessage
      expect(STRING_FIELDs).toContain(testConversationMessages[2].STRING_FIELD) // Bob Johnson ConversationMessage
    })

    it("should filter conversationMessages by Customer tag", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // John Doe ConversationMessage and Jane Smith ConversationMessage

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs).toContain(testConversationMessages[0].STRING_FIELD) // John Doe ConversationMessage
      expect(STRING_FIELDs).toContain(testConversationMessages[1].STRING_FIELD) // Jane Smith ConversationMessage
    })

    it("should handle pagination", async () => {
      const params = createPaginationParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(4)
    })

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(4)

      const STRING_FIELDs = result.body.data?.items.map(
        (c: any) => c.STRING_FIELD,
      )
      expect(STRING_FIELDs![0]).toBe(testConversationMessages[3].STRING_FIELD) // Alice Brown ConversationMessage
      expect(STRING_FIELDs![1]).toBe(testConversationMessages[2].STRING_FIELD) // Bob Johnson ConversationMessage
      expect(STRING_FIELDs![2]).toBe(testConversationMessages[1].STRING_FIELD) // Jane Smith ConversationMessage
      expect(STRING_FIELDs![3]).toBe(testConversationMessages[0].STRING_FIELD) // John Doe ConversationMessage
    })

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(2) // VIP conversationMessages (tag filter applied)
    })

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.items).toHaveLength(0)
      expect(result.body.data?.total).toBe(0)
    })

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Search keyword cannot be empty")
    })

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams()
      const result = await implHandleGetAllConversationMessages(businessLogic, params)

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Filter field cannot be empty")
    })

    it("should include soft deleted conversationMessages when specified", async () => {
      // Get all conversationMessages first to get one to delete
      const allResult = await implHandleGetAllConversationMessages(businessLogic)
      expect(allResult.status).toBe(200)
      const conversationMessagesToDelete = allResult.body.data?.items[0]
      expect(conversationMessagesToDelete).toBeDefined()

      // Soft delete one conversationMessages
      const deleteResult = await implHandleDeleteConversationMessage(
        conversationMessagesToDelete!.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Get all without including deleted
      const resultWithoutDeleted =
        await implHandleGetAllConversationMessages(businessLogic)
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3)

      // Get all including deleted
      const params = createIncludeDeletedParams()
      const resultWithDeleted = await implHandleGetAllConversationMessages(
        businessLogic,
        params,
      )
      expect(resultWithDeleted.body.data?.items).toHaveLength(4)
    })
  })
})
