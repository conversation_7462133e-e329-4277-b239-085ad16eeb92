// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages/interface"
import { ConversationMessageBusinessLogic } from "@/lib/repositories/conversationMessages/BusinessLogic"
import { MongoConversationMessageRepository } from "@/lib/repositories/conversationMessages/MongoRepository"
import { TestConversationMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateConversationMessage,
  implHandleGetConversationMessage,
  implHandleDeleteConversationMessage,
} from "@/app/api/v1/conversations/[conversationId]/messages/impl"
import {
  createConversationMessage,
  createSimpleConversationMessages,
  createComplexConversationMessage,
  createMinimalDeleteConversationMessage,
  createConversationMessagesForDeletionTest,
  createRetryDeleteConversationMessage,
} from "./object_creator"

describe("Delete ConversationMessage API Tests", () => {
  let businessLogic: ConversationMessageBusinessLogicInterface
  let dbRepository: TestConversationMessageDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ConversationMessage")
    await driver.connect()
    const originalDb = new MongoConversationMessageRepository(driver)
    dbRepository = new TestConversationMessageDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationMessageBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/conversations/[conversationId]/messages/:id", () => {
    it("should successfully delete an existing conversationMessage", async () => {
      const conversationMessagesData ConversationeateConversationMessage(5) // JConversationDoe ConversationMessage
      const createResult = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )
      const conversationMessagesId = createResult.body.data.id

      const getResult = await implHandleGetConversationMessage(
        conversationMessagesId,
        businessLogic,
      )
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteConversationMessage(
        conversationMessagesId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "ConversationMessage deleted successfully",
      )

      const getAfterDelete = await implHandleGetConversationMessage(
        conversationMessagesId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify conversationMessage count decreases after deletion", async () => {
      const conversationMessagesData = creaConversationmpleConversationMessages()

      const conversationMessagesIds: string[] = []
      for (const data of conversationMessagesData) {
        const result = await implHandleCreateConversationMessage(data, businessLogic)
        conversationMessagesIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getConversationMessageCount()).toBe(3)

      const deleteResult = await implHandleDeleteConversationMessage(
        conversationMessagesIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getConversationMessageCount()).toBe(2)

      const getDeleted = await implHandleGetConversationMessage(
        conversationMessagesIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent conversationMessage", async () => {
      const result = await implHandleDeleteConversationMessage(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ConversationMessage not found")
    })

    it("should fail with empty conversationMessage ID", async () => {
      const result = await implHandleDeleteConversationMessage("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ConversationMessage ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const conversationMessagesData = creatConversationplexConversationMessage()
      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteConversationMessage(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetConversationMessage(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const conversationMessagesData = createMinimConversationleteConversationMessage()
      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteConversationMessage(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetConversationMessage(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple conversationMessages", async () => {
      const conversationMessagesData = creaConversationmpleConversationMessages()

      const ids: string[] = []
      for (const data of conversationMessagesData) {
        const res = await implHandleCreateConversationMessage(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteConversationMessage(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getConversationMessageCount()).toBe(0)
    })

    it("should not affect other conversationMessages when deleting one", async () => {
      const conversationMessagesData ConversationeateConversationMessagesForDeletionTest()

      const ids: string[] = []
      for (const data of conversationMessagesData) {
        const res = await implHandleCreateConversationMessage(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteConversationMessage(ids[1], businessLogic)

      const getDeleted = await implHandleGetConversationMessage(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetConversationMessage(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetConversationMessage(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getConversationMessageCount()).toBe(2)
    })

    it("should handle attempting to delete the same conversationMessage twice", async () => {
      const conversationMessagesData = createRetConversationleteConversationMessage()
      const result = await implHandleCreateConversationMessage(
        conversationMessagesData,
        businessLogic,
      )
      const id = result.body.data?.id

      const first = await implHandleDeleteConversationMessage(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteConversationMessage(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
