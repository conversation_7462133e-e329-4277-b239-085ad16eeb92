//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface"
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic"
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository"
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateInvitation,
  implHandleGetInvitation,
  implHandleDeleteInvitation,
  implHandleUpdateInvitation,
  implHandleGetAllInvitations,
  implHandleRestoreInvitation,
} from "@/app/api/v1/invitations/impl"
import {
  createInvitation,
  createInvitationUpdate,
  createInvitationWithDescription,
  createTestInvitation,
  createTestInvitation2,
} from "./object_creator"

describe("Invitation Soft Delete Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface
  let dbRepository: TestInvitationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation")
    await driver.connect()
    const originalDb = new MongoInvitationRepository(driver)
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new InvitationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a invitations by default", async () => {
      const invitationData = createInvitationWithDescription()
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // Invitation should not be accessible by default
      const getResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(200)
      expect(getDeletedResult.body.data).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getInvitationCount()).toBe(0)
      expect(await dbRepository.getInvitationCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const invitationData = createInvitationWithDescription()
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
        true,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // Invitation should not be accessible at all
      const getResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
      const getDeletedResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(404)

      // Count should be 0 in both cases
      expect(await dbRepository.getInvitationCount()).toBe(0)
      expect(await dbRepository.getInvitationCount(true)).toBe(0)
    })

    it("should not include soft deleted invitations in getAll by default", async () => {
      const invitationData1 = createInvitation(1)
      const invitationData2 = createInvitation(2)

      const createResult1 = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one invitations
      const deleteResult = await implHandleDeleteInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllInvitations(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(1)
      expect(result.body.data?.total).toBe(1)
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id)
    })

    it("should include soft deleted invitations when specified", async () => {
      const invitationData1 = createInvitation(1)
      const invitationData2 = createInvitation(2)

      const createResult1 = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one invitations
      const deleteResult = await implHandleDeleteInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllInvitations(businessLogic, {
        includeDeleted: true,
      })

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(2)

      const deletedInvitation = result.body.data?.items.find(
        (c: any) => c.id === createResult1.body.data.id,
      )
      expect(deletedInvitation).toBeDefined()
      expect(deletedInvitation?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted invitations", async () => {
      const invitationData = createInvitation(3)
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const invitationUpdate = createInvitationUpdate(1)

      const result = await implHandleUpdateInvitation(
        createResult.body.data.id,
        invitationUpdate,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should not include soft deleted invitations in search", async () => {
      const invitationData1 = createInvitation(3) // "Test Invitation"
      const createResult = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the invitation
      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Search should not find the soft deleted invitation
      const searchResult = await implHandleGetAllInvitations(businessLogic, {
        search: "Test",
      })
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted invitations", async () => {
      const invitationData = createInvitationWithDescription()
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the invitations
      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // Restore the invitations
      const restoreResult = await implHandleRestoreInvitation(
        createResult.body.data.id,
        businessLogic,
      )

      expect(restoreResult.status).toBe(200)
      expect(restoreResult.body.status).toBe("success")

      // Invitation should be accessible again
      const restoredResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoredResult.status).toBe(200)
      expect(restoredResult.body.data?.deletedAt).toBeUndefined()

      // Count should include the restored invitations
      expect(await dbRepository.getInvitationCount()).toBe(1)
    })

    it("should fail to restore a non-existent invitations", async () => {
      const restoreResult = await implHandleRestoreInvitation(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a invitations that was never deleted", async () => {
      const invitationData = createInvitation(3)
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const restoreResult = await implHandleRestoreInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a hard deleted invitations", async () => {
      const invitationData = createInvitation(3)
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete the invitations
      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail with empty invitations ID", async () => {
      const restoreResult = await implHandleRestoreInvitation("", businessLogic)
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("Invitation ID is required")
    })

    it("should fail with whitespace-only invitations ID", async () => {
      const restoreResult = await implHandleRestoreInvitation(
        "   ",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain("Invitation ID is required")
    })

    it("should update updatedAt when restoring", async () => {
      const invitationData = createInvitation(3)
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const originalUpdatedAt = createResult.body.data.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      const deleteResult = await implHandleDeleteInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(200)

      const getResult = await implHandleGetInvitation(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(200)
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating invitations with STRING_FIELD of soft deleted invitations", async () => {
      // Create and soft delete a invitations
      const invitationData1 = createTestInvitation()
      const createResult1 = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Should be able to create new invitations with same STRING_FIELD
      const invitationData2 = createTestInvitation2()
      const createResult2 = await implHandleCreateInvitation(
        invitationData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(201)
      expect(createResult2.body.data.STRING_FIELD).toBe(
        invitationData2.STRING_FIELD,
      )
      expect(await dbRepository.getInvitationCount()).toBe(1)
    })

    it("should prevent creating invitations with STRING_FIELD of active invitations", async () => {
      const invitationData1 = createTestInvitation()
      const createResult1 = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const invitationData2 = createTestInvitation2()
      const createResult2 = await implHandleCreateInvitation(
        invitationData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(409)
      expect(createResult2.body.status).toBe("failed")
      expect(createResult2.body.error).toContain(
        "Invitation with the same STRING_FIELD already exists",
      )
    })

    it("should prevent restoring invitations if STRING_FIELD is now taken", async () => {
      // Create and soft delete a invitations
      const invitationData1 = createTestInvitation()
      const createResult1 = await implHandleCreateInvitation(
        invitationData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Create new invitations with same STRING_FIELD
      const invitationData2 = createTestInvitation2()
      const createResult2 = await implHandleCreateInvitation(
        invitationData2,
        businessLogic,
      )
      expect(createResult2.status).toBe(201)

      // Should not be able to restore the first invitations
      const restoreResult = await implHandleRestoreInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })
  })
})
