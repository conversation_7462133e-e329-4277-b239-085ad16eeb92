// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface"
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic"
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository"
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateInvitation,
  implHandleGetInvitation,
  implHandleDeleteInvitation,
} from "@/app/api/v1/invitations/impl"
import {
  createInvitation,
  createSimpleInvitations,
  createComplexInvitation,
  createMinimalDeleteInvitation,
  createInvitationsForDeletionTest,
  createRetryDeleteInvitation,
} from "./object_creator"

describe("Delete Invitation API Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface
  let dbRepository: TestInvitationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation")
    await driver.connect()
    const originalDb = new MongoInvitationRepository(driver)
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new InvitationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/invitations/:id", () => {
    it("should successfully delete an existing invitation", async () => {
      const invitationsData = createInvitation(5) // John Doe Invitation
      const createResult = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )
      const invitationsId = createResult.body.data.id

      const getResult = await implHandleGetInvitation(
        invitationsId,
        businessLogic,
      )
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteInvitation(
        invitationsId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "Invitation deleted successfully",
      )

      const getAfterDelete = await implHandleGetInvitation(
        invitationsId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify invitation count decreases after deletion", async () => {
      const invitationsData = createSimpleInvitations()

      const invitationsIds: string[] = []
      for (const data of invitationsData) {
        const result = await implHandleCreateInvitation(data, businessLogic)
        invitationsIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getInvitationCount()).toBe(3)

      const deleteResult = await implHandleDeleteInvitation(
        invitationsIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getInvitationCount()).toBe(2)

      const getDeleted = await implHandleGetInvitation(
        invitationsIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent invitation", async () => {
      const result = await implHandleDeleteInvitation(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Invitation not found")
    })

    it("should fail with empty invitation ID", async () => {
      const result = await implHandleDeleteInvitation("", businessLogic)
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("Invitation ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const invitationsData = createComplexInvitation()
      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteInvitation(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetInvitation(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const invitationsData = createMinimalDeleteInvitation()
      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteInvitation(id, businessLogic)
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetInvitation(id, businessLogic)
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple invitations", async () => {
      const invitationsData = createSimpleInvitations()

      const ids: string[] = []
      for (const data of invitationsData) {
        const res = await implHandleCreateInvitation(data, businessLogic)
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteInvitation(id, businessLogic)
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getInvitationCount()).toBe(0)
    })

    it("should not affect other invitations when deleting one", async () => {
      const invitationsData = createInvitationsForDeletionTest()

      const ids: string[] = []
      for (const data of invitationsData) {
        const res = await implHandleCreateInvitation(data, businessLogic)
        ids.push(res.body.data.id)
      }

      await implHandleDeleteInvitation(ids[1], businessLogic)

      const getDeleted = await implHandleGetInvitation(ids[1], businessLogic)
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetInvitation(ids[0], businessLogic)
      expect(first.status).toBe(200)

      const third = await implHandleGetInvitation(ids[2], businessLogic)
      expect(third.status).toBe(200)

      expect(await dbRepository.getInvitationCount()).toBe(2)
    })

    it("should handle attempting to delete the same invitation twice", async () => {
      const invitationsData = createRetryDeleteInvitation()
      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const first = await implHandleDeleteInvitation(id, businessLogic)
      expect(first.status).toBe(200)

      const second = await implHandleDeleteInvitation(id, businessLogic)
      expect(second.status).toBe(404)
    })
  })
})
