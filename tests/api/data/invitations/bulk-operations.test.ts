//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface"
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic"
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository"
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateInvitation,
  implHandleGetInvitation,
  implHandleBulkCreateInvitations,
  implHandleBulkUpdateInvitations,
  implHandleBulkDeleteInvitations,
} from "@/app/api/v1/invitations/impl"
import {
  createMultipleInvitations,
  createSimpleInvitations,
  createExistingInvitation,
  createDuplicateInvitationsForBulk,
  createInvitationsForBulkUpdate,
  createBulkUpdateData,
  createInvitationsForBulkDelete,
} from "./object_creator"

describe("Invitation Bulk Operations Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface
  let dbRepository: TestInvitationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation")
    await driver.connect()
    const originalDb = new MongoInvitationRepository(driver)
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new InvitationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Bulk Create", () => {
    it("should successfully create multiple invitations", async () => {
      const invitationsData = createMultipleInvitations()

      const result = await implHandleBulkCreateInvitations(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(invitationsData.length)
      expect(result.body.data[0].STRING_FIELD).toBe(
        invitationsData[0].STRING_FIELD,
      )
      expect(result.body.data[1].STRING_FIELD).toBe(
        invitationsData[1].STRING_FIELD,
      )
      expect(result.body.data[2].STRING_FIELD).toBe(
        invitationsData[2].STRING_FIELD,
      )
      expect(await dbRepository.getInvitationCount()).toBe(
        invitationsData.length,
      )
    })

    it("should fail if any invitation has duplicate STRING_FIELD", async () => {
      const existingInvitation = createExistingInvitation()
      const createResult = await implHandleCreateInvitation(
        existingInvitation,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const invitationsData = createDuplicateInvitationsForBulk()

      const result = await implHandleBulkCreateInvitations(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD found: Existing Invitation",
      )
      expect(await dbRepository.getInvitationCount()).toBe(1)
    })

    it("should handle simple invitations creation", async () => {
      const invitationsData = createSimpleInvitations()

      const result = await implHandleBulkCreateInvitations(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data).toHaveLength(invitationsData.length)
      expect(await dbRepository.getInvitationCount()).toBe(
        invitationsData.length,
      )
    })
  })

  describe("Bulk Update", () => {
    it("should successfully update multiple invitations", async () => {
      const invitationsData = createInvitationsForBulkUpdate()
      const createResult1 = await implHandleCreateInvitation(
        invitationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateInvitations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.updatedCount).toBe(2)

      const getResult1 = await implHandleGetInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetInvitation(
        createResult2.body.data.id,
        businessLogic,
      )

      expect(getResult1.body.data?.STRING_FIELD).toBe(
        updateData[0].STRING_FIELD,
      )
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy)
      expect(getResult2.body.data?.STRING_FIELD).toBe(
        updateData[1].STRING_FIELD,
      )
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy)
    })

    it("should fail if any invitation doesn't exist", async () => {
      const invitationData = createInvitationsForBulkUpdate()[0]
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const updateData = createBulkUpdateData()
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ]

      const result = await implHandleBulkUpdateInvitations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(500)
      expect(result.body.status).toBe("failed")
    })

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const invitationsData = createInvitationsForBulkUpdate()
      const createResult1 = await implHandleCreateInvitation(
        invitationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const updates = [
        {
          id: createResult2.body.data.id,
          data: {
            STRING_FIELD: invitationsData[0].STRING_FIELD,
            updatedBy: "admin",
          }, // Try to update second invitation with first invitation's STRING_FIELD
        },
      ]

      const result = await implHandleBulkUpdateInvitations(
        updates,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Duplicate STRING_FIELD in update: Invitation 1",
      )
    })
  })

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple invitations", async () => {
      const invitationsData = createInvitationsForBulkDelete()
      const createResult1 = await implHandleCreateInvitation(
        invitationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationsData[1],
        businessLogic,
      )
      const createResult3 = await implHandleCreateInvitation(
        invitationsData[2],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)
      expect(createResult3.status).toBe(201)

      const result = await implHandleBulkDeleteInvitations(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getInvitationCount()).toBe(1) // Only non-deleted

      const getResult1 = await implHandleGetInvitation(
        createResult1.body.data.id,
        businessLogic,
      )
      const getResult2 = await implHandleGetInvitation(
        createResult2.body.data.id,
        businessLogic,
      )
      const getResult3 = await implHandleGetInvitation(
        createResult3.body.data.id,
        businessLogic,
      )

      expect(getResult1.status).toBe(404)
      expect(getResult2.status).toBe(404)
      expect(getResult3.status).toBe(200)
    })

    it("should successfully hard delete multiple invitations", async () => {
      const invitationsData = createInvitationsForBulkDelete()
      const createResult1 = await implHandleCreateInvitation(
        invitationsData[0],
        businessLogic,
      )
      const createResult2 = await implHandleCreateInvitation(
        invitationsData[1],
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      const result = await implHandleBulkDeleteInvitations(
        [createResult1.body.data.id, createResult2.body.data.id],
        businessLogic,
        true,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data.deletedCount).toBe(2)
      expect(await dbRepository.getInvitationCount()).toBe(0)
    })

    it("should fail if any invitation doesn't exist", async () => {
      const invitationData = createInvitationsForBulkDelete()[0]
      const createResult = await implHandleCreateInvitation(
        invitationData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const result = await implHandleBulkDeleteInvitations(
        [createResult.body.data.id, "non-existent-id"],
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty invitation IDs", async () => {
      const result = await implHandleBulkDeleteInvitations(
        ["", "valid-id"],
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("ID at index 0 is required")
    })
  })
})
