//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface"
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic"
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository"
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { implHandleCreateInvitation } from "@/app/api/v1/invitations/impl"
import {
  createFullInvitation,
  createMinimalInvitation,
  createInvitationWithDescription,
  createInvitationWithTags,
  createInvitationWithWhitespace,
  createDuplicateInvitation,
  createSecondDuplicateInvitation,
  createInvalidInvitation,
  createInvitationWithManyTags,
  createInvitationWithoutDescription,
  createInvitationWithEmptyTags,
} from "./object_creator"

describe("Create Invitation API Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface
  let dbRepository: TestInvitationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation")
    await driver.connect()
    const originalDb = new MongoInvitationRepository(driver)
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new InvitationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/invitations", () => {
    it("should successfully create a new invitations with all fields", async () => {
      const invitationsData = createFullInvitation()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(invitationsData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(invitationsData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(invitationsData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(invitationsData.variables)
      expect(result.body.data?.tags).toEqual(invitationsData.tags)
      expect(result.body.data?.isActive).toBe(invitationsData.isActive)
      expect(result.body.data?.id).toBeDefined()
      expect(result.body.data?.createdAt).toBeDefined()
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should successfully create a invitations with minimal required fields", async () => {
      const invitationsData = createMinimalInvitation()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(invitationsData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(invitationsData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(invitationsData.variables)
      expect(result.body.data?.isActive).toBe(true) // Should default to true
      expect(result.body.data?.tags).toEqual([])
    })

    it("should create invitation with ARRAY_FIELD2", async () => {
      const invitationsData = createInvitationWithDescription()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.ARRAY_FIELD2).toBe(invitationsData.ARRAY_FIELD2)
    })

    it("should create invitation with tags", async () => {
      const invitationsData = createInvitationWithTags()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.tags).toEqual(invitationsData.tags)
    })

    it("should trim whitespace from STRING_FIELD", async () => {
      const invitationsData = createInvitationWithWhitespace()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Invitation")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first invitations
      const invitationsData1 = createDuplicateInvitation()
      await implHandleCreateInvitation(invitationsData1, businessLogic)

      // Try to create second invitations with same STRING_FIELD
      const invitationsData2 = createSecondDuplicateInvitation()
      const result = await implHandleCreateInvitation(
        invitationsData2,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Invitation with the same STRING_FIELD already exists",
      )
    })

    it("should fail with missing STRING_FIELD", async () => {
      const invitationsData = {
        ARRAY_FIELD2: "+6281234567890",
      }

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD2", async () => {
      const invitationsData = createInvalidInvitation("missing-ARRAY_FIELD2")

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD", async () => {
      const invitationsData = createInvalidInvitation("missing-ARRAY_FIELD")

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("ARRAY_FIELD")
    })

    it("should fail with missing variables", async () => {
      const invitationsData = createInvalidInvitation("missing-variables")

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("variables")
    })

    it("should fail with empty ARRAY_FIELD array", async () => {
      const invitationsData = createInvalidInvitation("empty-ARRAY_FIELD")

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty variables array", async () => {
      const invitationsData = createInvalidInvitation("empty-variables")

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should create invitation with many tags", async () => {
      const invitationsData = createInvitationWithManyTags()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(invitationsData.tags)
    })

    it("should handle optional ARRAY_FIELD2", async () => {
      const invitationsData = createInvitationWithoutDescription()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.ARRAY_FIELD2).toBe("")
    })

    it("should handle empty arrays for tags", async () => {
      const invitationsData = createInvitationWithEmptyTags()

      const result = await implHandleCreateInvitation(
        invitationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(invitationsData.tags)
    })
  })
})
