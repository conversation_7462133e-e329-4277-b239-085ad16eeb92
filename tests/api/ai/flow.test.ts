//@ts-ignore
import { beforeEach, describe, expect, it } from "bun:test"
import {
  implHandleRuleInference,
  implHandleMessageTemplate,
  implHandleDatasource,
  implHandleReply,
} from "@/app/api/v1/ai/execution/impl"
import { AIInferenceEngine } from "@/app/api/v1/ai/execution/AIInferenceEngine"
import { AiBusinessLogic } from "@/app/api/v1/ai/execution/AIBusinessLogic"
import {
  IAiRepository,
  IDatasourceRepository,
} from "@/app/api/v1/ai/execution/repository-interface"

class MockAIInferenceRepository implements IAiRepository {
  // constructor(private pinecone: Pinecone, private mongo: MockMongoDbRepository) { }
  // these from pineconde
  async getRulesForMessage(
    message: string,
  ): Promise<Array<{ id: string; content: string }>> {
    return [
      { id: `rule_for_${message}`, content: `Rule content for ${message}` },
    ]
  }

  async getMessageTemplateForRule(
    ruleId: string,
  ): Promise<Array<{ id: string; content: string }>> {
    return [
      {
        id: `template_for_${ruleId}`,
        content: `Template content for ${ruleId}`,
      },
    ]
  }

  //make this read from mongo
  async getMessageTemplateById(templateId: string): Promise<{
    id: string
    content: string
    dataSource: string
    variables: string[]
    fields: string[]
  }> {
    return {
      id: templateId,
      content: `Nama anda adalah {{name}} dan jumlah order anda adalah {{order_count}}`,
      dataSource: "ORDERS",
      variables: ["name", "order_count"],
      fields: ["name", "order_count"],
    }
  }
}

class MockMongoDbRepository implements IDatasourceRepository {
  async getDataWithDataSourceAndVariables(
    sourceId: string,
  ): Promise<Array<{ field: string; field2: string; field3: string }>> {
    return [
      {
        field: `value1 for ${sourceId}`,
        field2: `value2 for ${sourceId}`,
        field3: `value3 for ${sourceId}`,
      },
    ]
  }

  async getReplyContext(messageId: string): Promise<{ replyContext: string }> {
    return {
      replyContext: `reply_context_for_${messageId}`,
    }
  }
}

// --- Mock Runner ---

class MockRunnerService implements AIInferenceEngine {
  public executedContext: string | null = null
  public executedPayload: any = null

  private storedResponse: {
    status: "success" | "failed"
    data?: any
    error?: string[]
    errorCodes?: string[]
  } | null = null

  async execute(
    context: string,
    payload: any,
  ): Promise<{
    status: "success" | "failed"
    data?: any
    error?: string[]
    errorCodes?: string[]
  }> {
    this.executedContext = context
    this.executedPayload = payload

    if (!this.storedResponse) {
      throw new Error("No response stored before execute call")
    }

    // Return stored response and clear it after
    const response = this.storedResponse
    this.storedResponse = null
    return Promise.resolve(response)
  }

  replyWithSuccess(data: any) {
    this.storedResponse = {
      status: "success",
      data,
    }
  }

  replyWithFailure(error: string[], errorCodes: string[]) {
    this.storedResponse = {
      status: "failed",
      error,
      errorCodes,
    }
  }
}

describe("AI Flow Handlers", () => {
  let mockRunner: MockRunnerService
  let aiLogic: AiBusinessLogic

  const mockPayload = {
    text: "Hello, world!",
    contextId: "context123",
    templateId: "template123",
    sourceId: "source123",
    messageId: "message123",
  }

  beforeEach(() => {
    mockRunner = new MockRunnerService()
    const aiRepo = new MockAIInferenceRepository()
    const dataRepo = new MockMongoDbRepository()
    aiLogic = new AiBusinessLogic(mockRunner, aiRepo, dataRepo)
  })

  // ---------- Success Cases ----------

  it("should handle rule inference successfully", async () => {
    mockRunner.replyWithSuccess({ message: "Executed RULE_INFERENCE" })

    const result = await implHandleRuleInference(mockPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(result.body.data?.message).toBe("Executed RULE_INFERENCE")
    expect(mockRunner.executedContext).toBe("INFER_MESSAGE_RULE")
    expect(mockRunner.executedPayload.rules).toEqual([
      { id: "rule_for_context123", content: "Mock rule content" },
    ])
  })

  it("should handle message template successfully", async () => {
    mockRunner.replyWithSuccess({ message: "Executed MESSAGE_TEMPLATE" })

    const result = await implHandleMessageTemplate(mockPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(result.body.data?.message).toBe("Executed MESSAGE_TEMPLATE")
    expect(mockRunner.executedContext).toBe("SELECT_MESSAGE_TEMPLATE")
    expect(mockRunner.executedPayload.template).toEqual({
      id: "template_for_template123",
      content: "Mock template content",
    })
  })

  it("should handle datasource successfully", async () => {
    mockRunner.replyWithSuccess({ message: "Executed DATASOURCE" })

    const result = await implHandleDatasource(mockPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(result.body.data?.message).toBe("Executed DATASOURCE")
    expect(mockRunner.executedContext).toBe("SUPPLY_WITH_DATASOURCE")
    expect(mockRunner.executedPayload.datasource).toEqual([
      {
        field: "field1",
        field2: "field2_value",
        field3: "field3_value",
      },
    ])
  })

  it("should handle reply successfully", async () => {
    mockRunner.replyWithSuccess({ message: "Executed REPLY" })

    const result = await implHandleReply(mockPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(result.body.data?.message).toBe("Executed REPLY")
    expect(mockRunner.executedContext).toBe("REPLY")
    expect(mockRunner.executedPayload.replyContext).toEqual({
      id: "reply_context_for_message123",
      content: "Mock reply context content",
    })
  })

  // ---------- Failure Cases ----------

  it("should fail rule inference with mock error", async () => {
    mockRunner.replyWithFailure(["Rule failed"], ["RULE_ERROR"])

    const result = await implHandleRuleInference(mockPayload, aiLogic)

    expect(result.status).toBe(400)
    expect(result.body.status).toBe("failed")
    expect(result.body.error).toContain("Rule failed")
    expect(result.body.errorCodes).toContain("RULE_ERROR")
    expect(mockRunner.executedContext).toBe("INFER_MESSAGE_RULE")
  })

  it("should fail message template with mock error", async () => {
    mockRunner.replyWithFailure(["Template error"], ["TEMPLATE_ERROR"])

    const result = await implHandleMessageTemplate(mockPayload, aiLogic)

    expect(result.status).toBe(400)
    expect(result.body.status).toBe("failed")
    expect(result.body.error).toContain("Template error")
    expect(result.body.errorCodes).toContain("TEMPLATE_ERROR")
    expect(mockRunner.executedContext).toBe("SELECT_MESSAGE_TEMPLATE")
  })

  it("should fail datasource with mock error", async () => {
    mockRunner.replyWithFailure(["Datasource failed"], ["DS_ERROR"])

    const result = await implHandleDatasource(mockPayload, aiLogic)

    expect(result.status).toBe(400)
    expect(result.body.status).toBe("failed")
    expect(result.body.error).toContain("Datasource failed")
    expect(result.body.errorCodes).toContain("DS_ERROR")
    expect(mockRunner.executedContext).toBe("SUPPLY_WITH_DATASOURCE")
  })

  it("should fail reply with mock error", async () => {
    mockRunner.replyWithFailure(["Reply error"], ["REPLY_ERROR"])

    const result = await implHandleReply(mockPayload, aiLogic)

    expect(result.status).toBe(400)
    expect(result.body.status).toBe("failed")
    expect(result.body.error).toContain("Reply error")
    expect(result.body.errorCodes).toContain("REPLY_ERROR")
    expect(mockRunner.executedContext).toBe("REPLY")
  })

  // ---------- Runner Throws Exception ----------

  it("should throw if runner fails during rule inference", async () => {
    mockRunner.execute = async () => {
      throw new Error("Runner execution failed")
    }

    await expect(implHandleRuleInference(mockPayload, aiLogic)).rejects.toThrow(
      "Runner execution failed",
    )
  })

  it("should throw if runner fails during datasource", async () => {
    mockRunner.execute = async () => {
      throw new Error("Execution failure on datasource")
    }

    await expect(implHandleDatasource(mockPayload, aiLogic)).rejects.toThrow(
      "Execution failure on datasource",
    )
  })

  // ---------- Edge Case: Empty Input ----------

  it("should handle empty payload in rule inference gracefully", async () => {
    mockRunner.replyWithSuccess({ message: "Executed RULE_INFERENCE" })

    const emptyPayload = { message: "", contextId: "" }
    const result = await implHandleRuleInference(emptyPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(mockRunner.executedPayload.rules).toEqual([
      { id: "rule_for_", content: "Mock rule content" },
    ])
  })

  it("should handle unknown ruleId in message template", async () => {
    mockRunner.replyWithSuccess({ message: "Executed MESSAGE_TEMPLATE" })

    const customPayload = { ...mockPayload, templateId: "nonexistent_template" }
    const result = await implHandleMessageTemplate(customPayload, aiLogic)

    expect(result.status).toBe(200)
    expect(result.body.status).toBe("success")
    expect(mockRunner.executedPayload.template).toEqual({
      id: "template_for_nonexistent_template",
      content: "Mock template content",
    })
  })
})
