"use client"

import { useState } from "react"
import {
  MessagesAPI,
  type SendMessagePayload,
  type SendMessageResponse,
} from "@/lib/services"

type SendMessageParams = SendMessagePayload

export function useSendMessage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [response, setResponse] = useState<SendMessageResponse | null>(null)

  const sendMessage = async ({
    conversationId,
    text,
  }: SendMessageParams): Promise<SendMessageResponse> => {
    setLoading(true)
    setError(null)

    try {
      const data = await MessagesAPI.SendMessage({
        conversationId,
        text,
      }).request()
      setResponse(data)

      if (!data.success) {
        setError(data.error || "Gagal mengirim pesan.")
      }

      return data
    } catch (err: any) {
      const errorMsg =
        err.response?.data?.error ||
        err.message ||
        "<PERSON><PERSON><PERSON><PERSON> kesalahan saat mengirim pesan."
      setError(errorMsg)
      return { success: false, error: errorMsg }
    } finally {
      setLoading(false)
    }
  }

  return {
    sendMessage,
    loading,
    error,
    response,
  }
}
