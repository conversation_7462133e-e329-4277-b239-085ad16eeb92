import { useState, useCallback } from "react"
import {
  MessagesAPI,
  type SendTypingPayload,
  type SendTypingResponse,
} from "@/lib/services"

interface SendTypingParams {
  status: "start" | "stop"
  conversationId: string
}

export function useSendTyping() {
  const [loading, setLoading] = useState(false)
  const [response, setResponse] = useState<SendTypingResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  const sendTyping = useCallback(async (params: SendTypingParams) => {
    setLoading(true)
    setError(null)

    try {
      const data = await MessagesAPI.SendTyping({
        conversationId: params.conversationId,
        status: params.status,
      }).request()

      if (data.success) {
        setResponse(data)
      } else {
        setError("Gagal mengirim status typing.")
      }
    } catch (err: any) {
      console.error("Error sending typing status:", err)
      setError(err?.response?.data?.error || "<PERSON><PERSON><PERSON><PERSON> k<PERSON>.")
    } finally {
      setLoading(false)
    }
  }, [])

  return { sendTyping, loading, response, error }
}
