"use client"

import { useEffect, useCallback } from "react"
import { realtime } from "@/lib/realtime"
import { Conversation } from "@/lib/repositories/conversations"

interface UseConversationListRealtimeProps {
  data: Conversation[] | null
  setData: (data: Conversation[]) => void
  selectedConversation: Conversation | null
  onSelectConversation: (conversation: Conversation) => void
  refetch: (() => void) | undefined
  onConversationUpdate?: (updatedConversation: Conversation) => void
}

export function useConversationListRealtime({
  data,
  setData,
  selectedConversation,
  onSelectConversation,
  refetch,
  onConversationUpdate,
}: UseConversationListRealtimeProps) {
  // Handle new conversation room creation
  const handleNewRoom = useCallback(
    (value: any) => {
      console.log("New room created:", value)
      if (!data || !Array.isArray(data)) return

      if (value.isNew && value.conversation) {
        // Add new conversation to the top of the list
        const newData = [value.conversation, ...data]
        setData(newData)
        console.log(
          `✅ Added new conversation room to list: ${value.conversation.id}`,
        )
      }
    },
    [data, setData],
  )

  // Handle new messages and conversation updates
  const handleNewMessage = useCallback(
    (value: any) => {
      console.log("New message received:", value)
      if (!data || !Array.isArray(data)) return

      const conversationId = value.conversationId || value.from
      const existingConversation = data.find(
        (conversation) => conversation.id === conversationId,
      )

      if (existingConversation) {
        // Update existing conversation efficiently
        const lastMessage = value.conversation?.lastMessage || {
          body: value.message?.content || value.message?.body || value.message || "",
          fromMe: value.message?.fromMe || value.fromMe || false,
          _data: value.message?.metadata?.wahaData || value.message?._data || {
            messageTimestamp: value.message?.createdAt || value.timestamp || new Date(),
          },
          ack: value.message?.ack || (existingConversation.id === selectedConversation?.id ? 3 : 0),
        }

        const updates: Partial<Conversation> = {
          lastMessage,
          lastMessageAt: value.message?.createdAt ||
            (value.timestamp ? new Date(value.timestamp * 1000) : null) ||
            new Date(),
        }

        // Move updated conversation to top of list
        const otherConversations = data.filter(
          (conversation) => conversation.id !== conversationId,
        )
        const updatedConversation = { ...existingConversation, ...updates }
        setData([updatedConversation, ...otherConversations])

        // Update selected conversation if it's the same conversation
        if (selectedConversation?.id === conversationId) {
          onSelectConversation(updatedConversation)
        }

        console.log(`✅ Updated existing conversation: ${conversationId}`)
      } else if (value.conversation && !value.isUpdate) {
        // This is a new conversation that was just created, add it to the top
        setData([value.conversation, ...data])
        console.log(
          `✅ Added new conversation from message: ${value.conversation.id}`,
        )
      }
    },
    [data, setData, selectedConversation?.id, onSelectConversation],
  )

  // Handle presence updates
  const handlePresenceUpdate = useCallback(
    (value: any) => {
      console.log("update-presence:", value)
      if (!data || !Array.isArray(data)) return

      const newData = data.map((conversation: Conversation) => {
        return {
          ...conversation,
          status_presence:
            value.presences.find((p: any) => p.participant === conversation.id)
              ?.lastKnownPresence || "offline",
        }
      })
      setData(newData)
      onSelectConversation(
        newData.filter(
          (conversation) => conversation.id === selectedConversation?.id,
        )[0] || newData[0],
      )
    },
    [data, setData, selectedConversation?.id, onSelectConversation],
  )

  // Setup realtime listeners for conversation room events
  useEffect(() => {
    if (!refetch) return
    const channel = realtime.client.subscribe("conversation-room-channel")

    channel.bind("new-room", handleNewRoom)
    channel.bind("new-message", handleNewMessage)

    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [refetch, handleNewRoom, handleNewMessage])

  // Setup realtime listeners for presence updates
  useEffect(() => {
    if (!refetch) return
    const channel = realtime.client.subscribe("message-channel")

    channel.bind("update-presence", handlePresenceUpdate)

    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [refetch, handlePresenceUpdate])
}
