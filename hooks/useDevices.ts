import { useEffect, useState, useCallback } from "react"
import { DevicesAP<PERSON>, SyncResult } from "@/lib/services/devicesApi"
import { Device } from "@/lib/repositories/devices/interface"

export function useDevices() {
  const [devices, setDevices] = useState<Device[]>([])
  const [loading, setLoading] = useState(false)
  const [syncResult, setSyncResult] = useState<SyncResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const fetchDevices = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await DevicesAPI.All({
        page: 1,
        per_page: 100,
        sort: [{ field: "createdAt", direction: "DESC" }],
      }).request()

      setDevices(response.items || [])
      setSyncResult(response.syncResult)
    } catch (err) {
      console.error("Error fetching devices:", err)
      setError("Failed to fetch devices")
    } finally {
      setLoading(false)
    }
  }, [])

  const syncDevices = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await DevicesAPI.Sync().request()
      setSyncResult(result)
      // Refresh devices after sync
      await fetchDevices()
    } catch (err) {
      console.error("Error syncing devices:", err)
      setError("Failed to sync devices")
    } finally {
      setLoading(false)
    }
  }, [fetchDevices])

  const deleteDevice = useCallback(async (deviceId: string) => {
    try {
      await DevicesAPI.Delete(deviceId).request()
      // Refresh devices after delete
      await fetchDevices()
    } catch (err) {
      console.error("Error deleting device:", err)
      setError("Failed to delete device")
    }
  }, [fetchDevices])

  const updateDevice = useCallback(async (deviceId: string, data: any) => {
    try {
      await DevicesAPI.Update(deviceId, data).request()
      // Refresh devices after update
      await fetchDevices()
    } catch (err) {
      console.error("Error updating device:", err)
      setError("Failed to update device")
    }
  }, [fetchDevices])

  useEffect(() => {
    fetchDevices()
  }, [fetchDevices])

  return {
    devices,
    loading,
    error,
    syncResult,
    fetchDevices,
    syncDevices,
    deleteDevice,
    updateDevice
  }
}
