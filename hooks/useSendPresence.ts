import { useState } from "react"
import {
  ConversationsPresenceAPI,
  type SendPresencePayload,
} from "@/lib/services"

interface SendPresenceOptions {
  provider?: string
  presence: string
}

interface UseSendPresenceResult {
  sendPresence: (options: SendPresenceOptions) => Promise<void>
  loading: boolean
  error: string | null
  result: any
}

export function useSendPresence(): UseSendPresenceResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<any>(null)

  const sendPresence = async ({ provider, presence }: SendPresenceOptions) => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const data = await ConversationsPresenceAPI.SendPresence({
        provider: provider || "waha",
        presence,
      }).request()

      setResult(data)
    } catch (err: any) {
      const message =
        err?.response?.data?.error || err.message || "Unknown error"
      setError(message)
    } finally {
      setLoading(false)
    }
  }

  return {
    sendPresence,
    loading,
    error,
    result,
  }
}
