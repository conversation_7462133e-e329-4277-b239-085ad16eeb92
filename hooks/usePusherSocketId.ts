import { realtime } from "@/lib/realtime"
import { useEffect, useState } from "react"

export function useRealtimeSocketId() {
  const [socketId, setSocketId] = useState<string | null>(null)

  useEffect(() => {
    realtime.client.connection.bind("connected", () => {
      setSocketId(realtime.client.connection.socket_id)
      console.log(
        "Realtime connected with socket ID:",
        realtime.client.connection.socket_id,
      )
    })

    return () => {
      realtime.client.disconnect()
    }
  }, [])

  return socketId
}

// Legacy export for backward compatibility
export const usePusherSocketId = useRealtimeSocketId
