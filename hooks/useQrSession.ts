import { useEffect, useState } from "react"
import { QrSessionAPI } from "@/lib/services"
import { Device } from "./useDevices"

export function useQrSession(
  devices: Device[],
  onSessionActive?: () => void,
  onEndTimer?: () => void,
) {
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [timeLeft, setTimeLeft] = useState<number>(0)

  async function startSession() {
    setQrCode(null)
    setSessionId(null)
    setTimeLeft(0)

    try {
      const data = await QrSessionAPI.CreateSession().request()

      if (!data.success) throw new Error(data.error)

      if (data.qr) setQrCode(data.qr)
      if (data.session) setSessionId(data.session)
    } catch (err) {
      console.error("Error creating session:", err)
    }
  }

  useEffect(() => {
    if (!sessionId || !devices) return

    let elapsedTime = 0
    const maxTime = 15000 // 15 seconds

    // fetching delay 2 seconds
    const fetchInterval = setInterval(() => {
      onSessionActive && onSessionActive()
    }, 2000)

    const intervalId = setInterval(async () => {
      elapsedTime += 1000
      const device = devices.find((d) => d.name === sessionId)
      const isActive = device?.status === "WORKING"

      if (isActive) {
        localStorage.setItem("activeSessionId", sessionId)

        setQrCode(null)
        setSessionId(null)
        setTimeLeft(0)
        onEndTimer && onEndTimer()
        clearInterval(fetchInterval)
        clearInterval(intervalId)
      } else if (elapsedTime >= maxTime) {
        setQrCode(null)
        setSessionId(null)
        setTimeLeft(0)
        onEndTimer && onEndTimer()
        clearInterval(fetchInterval)
        clearInterval(intervalId)
      } else {
        setTimeLeft((maxTime - elapsedTime) / 1000)
      }
    }, 1000)

    return () => {
      clearInterval(fetchInterval)
      clearInterval(intervalId)
    }
  }, [sessionId])

  return { startSession, qrCode, sessionId, timeLeft }
}
