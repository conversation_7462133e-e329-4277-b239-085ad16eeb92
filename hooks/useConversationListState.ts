"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { Conversation } from "@/lib/repositories/conversations"
import { useConversationDetails } from "./useConversationDetails"

interface SyncInfo {
  lastSync?: Date
  pendingChanges: number
  isOnline: boolean
}

export function useConversationListState() {
  const [searchQuery, setSearchQuery] = useState("")
  const [autoCompact, setAutoCompact] = useState(false)
  const [manualCompact, setManualCompact] = useState<boolean | null>(null)
  const [showOfflineData, setShowOfflineData] = useState(false)
  const [lastSyncInfo, setLastSyncInfo] = useState<SyncInfo>({
    pendingChanges: 0,
    isOnline: true,
  })

  const containerRef = useRef<HTMLDivElement | null>(null)
  const listRef = useRef<HTMLDivElement | null>(null)

  // Conversation details fetching for efficient single conversation updates
  const { fetchConversationDetails } = useConversationDetails()

  // Determine if compact mode is active
  const isCompact = manualCompact !== null ? manualCompact : autoCompact

  // Function to efficiently update a single conversation without full refetch
  const updateSingleConversation = useCallback(
    async (
      conversationId: string,
      updates?: Partial<Conversation>,
      data?: Conversation[] | null,
      setData?: (data: Conversation[]) => void,
      selectedConversation?: Conversation | null,
      onSelectConversation?: (converConversationon: Conversation) => void,
    ) => {
      if (!data || !Array.isArray(data) || !setData) return

      let updatedConversation: Conversation | null = null

      if (updates) {
        // Use provided updates
        const existingConversation = data.find(
          (conversation) => conversation.id === conversationId,
        )
        if (existingConversation) {
          updatedConversation = { ...existingConversation, ...updates }
        }
      } else {
        // Fetch fresh data for this conversation
        updatedConversation = await fetchConversationDetails(conversationId)
      }

      if (updatedConversation) {
        const newData = data.map((conversation) =>
          conversation.id === conversationId
            ? updatedConversation!
            : conversation,
        )
        setData(newData)

        // Update selected conversation if it's the same conversation
        if (
          selectedConversation?.id === conversationId &&
          onSelectConversation
        ) {
          onSelectConversation(updatedConversation)
        }
      }
    },
    [fetchConversationDetails],
  )

  // Update sync info periodically
  useEffect(() => {
    // const updateSyncInfo = async () => {
    //   try {
    //     const { offlineRepoManager } = await import('@/app/client_repositories/OfflineRepositoryManager')
    //     const syncStatus = offlineRepoManager.getSyncStatus()
    //     const pendingChanges = await offlineRepoManager.getPendingChangesCount()
    //     setLastSyncInfo({
    //       lastSync: syncStatus.lastSyncAt,
    //       pendingChanges,
    //       isOnline: syncStatus.isOnline
    //     })
    //   } catch (error) {
    //     console.error('Error updating sync info:', error)
    //   }
    // }
    // updateSyncInfo()
    // const interval = setInterval(updateSyncInfo, 5000) // Update every 5 seconds
    // return () => clearInterval(interval)
  }, [])

  // Auto compact mode based on container width
  useEffect(() => {
    const observer = new ResizeObserver(([entry]) => {
      if (manualCompact === null) {
        setAutoCompact(entry.contentRect.width < 160)
      }
    })

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [manualCompact])

  // Toggle compact mode
  const toggleCompactMode = useCallback(() => {
    setManualCompact((prev) => !prev)
  }, [])

  // Toggle offline data view
  const toggleOfflineData = useCallback(() => {
    setShowOfflineData((prev) => !prev)
  }, [])

  return {
    // State
    searchQuery,
    setSearchQuery,
    autoCompact,
    manualCompact,
    showOfflineData,
    lastSyncInfo,
    setLastSyncInfo,
    isCompact,

    // Refs
    containerRef,
    listRef,

    // Functions
    updateSingleConversation,
    toggleCompactMode,
    toggleOfflineData,
  }
}
