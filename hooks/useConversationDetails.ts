"use client"

import { useState, useCallback } from "react"
import { ConversationsAPI } from "@/lib/services"
import { Conversation } from "@/lib/repositories/conversations/interface"

export interface UseConversationDetailsResult {
  fetchConversationDetails: (
    conversationId: string,
  ) => Promise<Conversation | null>
  loading: boolean
  error: string | null
}

export function useConversationDetails(): UseConversationDetailsResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchConversationDetails = useCallback(
    async (conversationId: string): Promise<Conversation | null> => {
      if (!conversationId) return null

      setLoading(true)
      setError(null)

      try {
        const conversation =
          await ConversationsAPI.Detail(conversationId).request()
        return conversation
      } catch (err: any) {
        const errorMessage =
          err?.response?.data?.error ||
          err.message ||
          "Failed to fetch conversation details"
        setError(errorMessage)
        console.error("Failed to fetch conversation details:", err)
        return null
      } finally {
        setLoading(false)
      }
    },
    [],
  )

  return {
    fetchConversationDetails,
    loading,
    error,
  }
}
