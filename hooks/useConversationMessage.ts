import { ConversationMessage } from "@/lib/repositories/conversationMessages"
import { ConversationMessagesAPI } from "@/lib/services/conversationMessageApi"
import { useState, useEffect } from "react"

export type useConversationMessage = any

interface UseReceiveMessagesOptions {
  provider?: string
  conversationId: string
}

const k_PERPAGE = 20

export function useConversationMessage({
  provider,
  conversationId,
}: UseReceiveMessagesOptions) {
  const [messages, setMessages] = useState<ConversationMessage[] | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLastConversations, setIsLastConversations] = useState(false)
  const [page, setPage] = useState(0)

  const fetchMessages = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await ConversationMessagesAPI.All(conversationId, {
        page: page + 1, // API uses 1-based pagination
        per_page: k_PERPAGE,
        search: undefined,
        sort: [{ field: "createdAt", direction: "DESC" }], // Read from last created
        filters: [],
      }).request()

      if (data.items) {
        // Reverse the items since we're getting them in DESC order but want to display in ASC order
        const reversedItems = [...data.items].reverse()

        setMessages((prev: any) => {
          if (page === 0) {
            // Full refresh for page 1
            return reversedItems || []
          } else {
            // Append messages when page > 1 (prepend since we're loading older messages)
            return [...(reversedItems || []), ...(prev || [])]
          }
        })

        // Check if we've reached the last page
        setIsLastConversations(data?.items.length < k_PERPAGE)
      }
    } catch (err: any) {
      setError(err.message)
      setMessages(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!conversationId) return
    fetchMessages()
  }, [conversationId, page])

  return {
    messages,
    isLastConversations,
    page,
    setPage,
    loading,
    error,
    refetch: fetchMessages,
  }
}
