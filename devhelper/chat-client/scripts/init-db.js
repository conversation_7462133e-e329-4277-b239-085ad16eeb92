#!/usr/bin/env node

/**
 * Database initialization script for chat-client
 * This script sets up the MongoDB database with initial data and indexes
 */

import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DATABASE_NAME = process.env.MONGODB_DATABASE || 'chat_client';

async function initializeDatabase() {
  console.log('🚀 Initializing chat-client database...\n');

  const client = new MongoClient(MONGODB_URI);

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db(DATABASE_NAME);

    // Create collections
    const conversationsCollection = db.collection('conversations');
    const messagesCollection = db.collection('messages');

    // Drop existing collections (for clean setup)
    try {
      await conversationsCollection.drop();
      console.log('🗑️  Dropped existing conversations collection');
    } catch (error) {
      // Collection doesn't exist, which is fine
    }

    try {
      await messagesCollection.drop();
      console.log('🗑️  Dropped existing messages collection');
    } catch (error) {
      // Collection doesn't exist, which is fine
    }

    // Create indexes for conversations
    await conversationsCollection.createIndex({ sessionId: 1 });
    await conversationsCollection.createIndex({ from: 1 });
    await conversationsCollection.createIndex({ to: 1 });
    await conversationsCollection.createIndex({ updatedAt: -1 });
    await conversationsCollection.createIndex({ 
      name: 'text', 
      from: 'text', 
      to: 'text', 
      lastMessage: 'text' 
    });
    console.log('📊 Created indexes for conversations collection');

    // Create indexes for messages
    await messagesCollection.createIndex({ conversationId: 1 });
    await messagesCollection.createIndex({ timestamp: -1 });
    await messagesCollection.createIndex({ conversationId: 1, timestamp: -1 });
    console.log('📊 Created indexes for messages collection');

    // Insert sample data
    const sampleConversations = [
      {
        id: '1',
        sessionId: 'session_1',
        name: 'John Doe',
        from: '+1234567890',
        to: '+0987654321',
        status: 'CONNECTED',
        lastMessage: 'Hey, how are you doing?',
        timestamp: new Date('2024-01-15T10:30:00'),
        createdAt: new Date('2024-01-15T10:00:00'),
        updatedAt: new Date('2024-01-15T10:30:00'),
        messages: []
      },
      {
        id: '2',
        sessionId: 'session_2',
        name: 'Jane Smith',
        from: '+1987654321',
        to: '+1234567890',
        status: 'CONNECTED',
        lastMessage: 'Thanks for your help!',
        timestamp: new Date('2024-01-15T09:15:00'),
        createdAt: new Date('2024-01-15T09:00:00'),
        updatedAt: new Date('2024-01-15T09:15:00'),
        messages: []
      },
      {
        id: '3',
        sessionId: 'session_3',
        name: 'Mike Johnson',
        from: '+1122334455',
        to: '+1555666777',
        status: 'CONNECTED',
        lastMessage: 'See you tomorrow!',
        timestamp: new Date('2024-01-14T18:45:00'),
        createdAt: new Date('2024-01-14T18:00:00'),
        updatedAt: new Date('2024-01-14T18:45:00'),
        messages: []
      }
    ];

    const sampleMessages = [
      // Messages for conversation 1
      {
        id: '1',
        conversationId: '1',
        text: 'Hello there!',
        fromMe: false,
        timestamp: new Date('2024-01-15T10:25:00')
      },
      {
        id: '2',
        conversationId: '1',
        text: 'Hi! How are you?',
        fromMe: true,
        timestamp: new Date('2024-01-15T10:26:00')
      },
      {
        id: '3',
        conversationId: '1',
        text: 'Hey, how are you doing?',
        fromMe: false,
        timestamp: new Date('2024-01-15T10:30:00')
      },
      // Messages for conversation 2
      {
        id: '4',
        conversationId: '2',
        text: 'Can you help me with this?',
        fromMe: false,
        timestamp: new Date('2024-01-15T09:10:00')
      },
      {
        id: '5',
        conversationId: '2',
        text: 'Sure, what do you need?',
        fromMe: true,
        timestamp: new Date('2024-01-15T09:12:00')
      },
      {
        id: '6',
        conversationId: '2',
        text: 'Thanks for your help!',
        fromMe: false,
        timestamp: new Date('2024-01-15T09:15:00')
      },
      // Messages for conversation 3
      {
        id: '7',
        conversationId: '3',
        text: 'Are we still meeting tomorrow?',
        fromMe: false,
        timestamp: new Date('2024-01-14T18:40:00')
      },
      {
        id: '8',
        conversationId: '3',
        text: 'Yes, 2 PM at the coffee shop',
        fromMe: true,
        timestamp: new Date('2024-01-14T18:42:00')
      },
      {
        id: '9',
        conversationId: '3',
        text: 'See you tomorrow!',
        fromMe: false,
        timestamp: new Date('2024-01-14T18:45:00')
      }
    ];

    // Insert sample conversations
    await conversationsCollection.insertMany(sampleConversations);
    console.log(`📝 Inserted ${sampleConversations.length} sample conversations`);

    // Insert sample messages
    await messagesCollection.insertMany(sampleMessages);
    console.log(`💬 Inserted ${sampleMessages.length} sample messages`);

    console.log('\n🎉 Database initialization completed successfully!');
    console.log(`📊 Database: ${DATABASE_NAME}`);
    console.log(`🔗 URI: ${MONGODB_URI}`);
    console.log('\n📋 Collections created:');
    console.log('  - conversations (with indexes)');
    console.log('  - messages (with indexes)');
    console.log('\n🚀 You can now start the chat-client application!');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the initialization
initializeDatabase().catch(console.error);
