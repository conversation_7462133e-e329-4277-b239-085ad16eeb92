export interface Message {
  id: string;
  text: string;
  fromMe: boolean;
  timestamp: Date | string;
}


export interface ConversationLastMessage {
  body: string;
  fromMe: boolean;
  _data: {
    messageTimestamp: Date | string;
  };
  ack: number;
}

export interface Conversation {
  id: string;
  sessionId?: string;
  name: string;
  from: string;
  to?: string;
  status?: string;
  lastMessage?: ConversationLastMessage;
  timestamp: Date | string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  messages: Message[];
}

export interface SyncResult {
  synced: number;
  created: number;
  updated: number;
  errors: string[];
}

export interface ApiResponse<T> {
  status: 'success' | 'failed';
  data: T;
  messages?: string[];
  errorCodes?: string[];
}
