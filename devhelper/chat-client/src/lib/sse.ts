import type { Message, Conversation } from './types';

export interface SSEEvent {
  type: 'message' | 'conversation_update' | 'typing' | 'presence' | 'read_receipt';
  data: any;
  conversationId?: string;
}

export interface SSEMessageEvent extends SSEEvent {
  type: 'message';
  data: {
    message: Message;
    conversation: Conversation;
  };
  conversationId: string;
}

export interface ConversationUpdateEvent extends SSEEvent {
  type: 'conversation_update';
  data: {
    conversation: Conversation;
    action: 'created' | 'updated' | 'deleted';
  };
  conversationId: string;
}

export interface TypingEvent extends SSEEvent {
  type: 'typing';
  data: {
    status: 'typing' | 'stopped';
    conversationId: string;
    timestamp: string;
  };
  conversationId: string;
}

export interface PresenceEvent extends SSEEvent {
  type: 'presence';
  data: {
    presence: 'online' | 'offline';
    conversationId: string;
    timestamp: string;
  };
  conversationId: string;
}

export interface ReadReceiptEvent extends SSEEvent {
  type: 'read_receipt';
  data: {
    conversationId: string;
    readAt: string;
  };
  conversationId: string;
}

type EventCallback = (event: SSEEvent) => void;

class SSEService {
  private eventSource: EventSource | null = null;
  private callbacks: Map<string, EventCallback[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(): void {
    if (this.eventSource) {
      console.log('🔄 SSE: Disconnecting existing connection before reconnecting');
      this.disconnect();
    }

    try {
      console.log('🚀 SSE: Attempting to connect to /api/events');
      this.eventSource = new EventSource('/api/events');

      this.eventSource.onopen = () => {
        console.log('✅ SSE: Connection opened successfully');
        console.log('📊 SSE: Connection state:', this.eventSource?.readyState);
        this.reconnectAttempts = 0;
      };

      this.eventSource.onmessage = (event) => {
        console.log('📨 SSE: Raw message received:', {
          data: event.data,
          lastEventId: event.lastEventId,
          origin: event.origin,
          type: event.type
        });

        try {
          const data: SSEEvent = JSON.parse(event.data);
          console.log('📋 SSE: Parsed event data:', {
            type: data.type,
            conversationId: data.conversationId,
            dataKeys: Object.keys(data.data || {}),
            fullData: data
          });
          this.handleEvent(data);
        } catch (error) {
          console.error('❌ SSE: Error parsing event:', error, 'Raw data:', event.data);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('❌ SSE: Connection error:', error);
        console.log('📊 SSE: Connection state after error:', this.eventSource?.readyState);
        this.handleReconnect();
      };

      // Listen for specific event types
      this.eventSource.addEventListener('message', (event) => {
        console.log('🎯 SSE: Received typed "message" event');
        this.handleTypedEvent('message', event);
      });

      this.eventSource.addEventListener('conversation_update', (event) => {
        console.log('🎯 SSE: Received typed "conversation_update" event');
        this.handleTypedEvent('conversation_update', event);
      });

      this.eventSource.addEventListener('typing', (event) => {
        console.log('🎯 SSE: Received typed "typing" event');
        this.handleTypedEvent('typing', event);
      });

      this.eventSource.addEventListener('presence', (event) => {
        console.log('🎯 SSE: Received typed "presence" event');
        this.handleTypedEvent('presence', event);
      });

      this.eventSource.addEventListener('read_receipt', (event) => {
        console.log('🎯 SSE: Received typed "read_receipt" event');
        this.handleTypedEvent('read_receipt', event);
      });

      this.eventSource.addEventListener('heartbeat', (event) => {
        console.log('💓 SSE: Received heartbeat event');
        // Don't process heartbeat events, just log them
      });

    } catch (error) {
      console.error('Error creating SSE connection:', error);
      this.handleReconnect();
    }
  }

  private handleTypedEvent(type: string, event: MessageEvent<any>): void {
    console.log(`🔍 SSE: Processing typed event "${type}":`, {
      data: event.data,
      lastEventId: event.lastEventId,
      type: event.type
    });

    try {
      const data: SSEEvent = JSON.parse(event.data);
      console.log(`✅ SSE: Successfully parsed "${type}" event:`, {
        eventType: data.type,
        conversationId: data.conversationId,
        hasData: !!data.data
      });

      if (data.type === 'message') {
        console.log('💬 SSE: Message event details:', {
          messageText: data.data?.message?.text,
          messageId: data.data?.message?.id,
          fromMe: data.data?.message?.fromMe,
          conversationName: data.data?.conversation?.name,
          conversationId: data.data?.conversation?.id
        });
      }

      this.handleEvent(data);
    } catch (error) {
      console.error(`❌ SSE: Error parsing ${type} event:`, error, 'Raw data:', event.data);
    }
  }

  private handleEvent(event: SSEEvent): void {
    console.log('🎪 SSE: Handling event:', {
      type: event.type,
      conversationId: event.conversationId,
      timestamp: new Date().toISOString()
    });

    // Call global callbacks
    const globalCallbacks = this.callbacks.get('*') || [];
    console.log(`📢 SSE: Calling ${globalCallbacks.length} global callbacks for event "${event.type}"`);
    globalCallbacks.forEach((callback, index) => {
      try {
        console.log(`🔄 SSE: Executing global callback ${index + 1}/${globalCallbacks.length}`);
        callback(event);
      } catch (error) {
        console.error(`❌ SSE: Error in global callback ${index + 1}:`, error);
      }
    });

    // Call type-specific callbacks
    const typeCallbacks = this.callbacks.get(event.type) || [];
    console.log(`📢 SSE: Calling ${typeCallbacks.length} type-specific callbacks for event "${event.type}"`);
    typeCallbacks.forEach((callback, index) => {
      try {
        console.log(`🔄 SSE: Executing type callback ${index + 1}/${typeCallbacks.length} for "${event.type}"`);
        callback(event);
      } catch (error) {
        console.error(`❌ SSE: Error in type callback ${index + 1} for "${event.type}":`, error);
      }
    });

    // Call conversation-specific callbacks
    if (event.conversationId) {
      const conversationCallbacks = this.callbacks.get(`conversation:${event.conversationId}`) || [];
      console.log(`📢 SSE: Calling ${conversationCallbacks.length} conversation-specific callbacks for conversation "${event.conversationId}"`);
      conversationCallbacks.forEach((callback, index) => {
        try {
          console.log(`🔄 SSE: Executing conversation callback ${index + 1}/${conversationCallbacks.length} for conversation "${event.conversationId}"`);
          callback(event);
        } catch (error) {
          console.error(`❌ SSE: Error in conversation callback ${index + 1} for conversation "${event.conversationId}":`, error);
        }
      });
    }

    console.log(`✅ SSE: Finished handling event "${event.type}" (${globalCallbacks.length + typeCallbacks.length + (event.conversationId ? this.callbacks.get(`conversation:${event.conversationId}`)?.length || 0 : 0)} total callbacks executed)`);
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`🔄 SSE: Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        console.log(`🚀 SSE: Executing reconnection attempt ${this.reconnectAttempts}`);
        this.connect();
      }, delay);
    } else {
      console.error('❌ SSE: Max reconnection attempts reached. Connection failed permanently.');
      console.log('📊 SSE: Final connection state:', this.eventSource?.readyState);
    }
  }

  disconnect(): void {
    if (this.eventSource) {
      console.log('🔌 SSE: Disconnecting from server');
      console.log('📊 SSE: Connection state before disconnect:', this.eventSource.readyState);
      this.eventSource.close();
      this.eventSource = null;
      console.log('✅ SSE: Disconnected successfully');
    } else {
      console.log('ℹ️ SSE: No active connection to disconnect');
    }
  }

  // Subscribe to events
  on(eventType: string, callback: EventCallback): () => void {
    console.log(`📝 SSE: Subscribing to event type "${eventType}"`);

    if (!this.callbacks.has(eventType)) {
      this.callbacks.set(eventType, []);
    }

    this.callbacks.get(eventType)!.push(callback);
    const totalCallbacks = this.callbacks.get(eventType)!.length;

    console.log(`📊 SSE: Now have ${totalCallbacks} callback(s) for event type "${eventType}"`);

    // Return unsubscribe function
    return () => {
      console.log(`🗑️ SSE: Unsubscribing from event type "${eventType}"`);
      const callbacks = this.callbacks.get(eventType);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
          console.log(`📊 SSE: Now have ${callbacks.length} callback(s) for event type "${eventType}"`);
        }
      }
    };
  }

  // Subscribe to all events
  onAny(callback: EventCallback): () => void {
    return this.on('*', callback);
  }

  // Subscribe to specific conversation events
  onConversation(conversationId: string, callback: EventCallback): () => void {
    return this.on(`conversation:${conversationId}`, callback);
  }

  // Subscribe to new messages
  onMessage(callback: (event: SSEMessageEvent) => void): () => void {
    console.log('💬 SSE: Setting up message event subscription');
    return this.on('message', callback as EventCallback);
  }

  // Subscribe to conversation updates
  onConversationUpdate(callback: (event: ConversationUpdateEvent) => void): () => void {
    console.log('🔄 SSE: Setting up conversation update event subscription');
    return this.on('conversation_update', callback as EventCallback);
  }

  // Subscribe to typing events
  onTyping(callback: (event: TypingEvent) => void): () => void {
    return this.on('typing', callback as EventCallback);
  }

  // Subscribe to presence events
  onPresence(callback: (event: PresenceEvent) => void): () => void {
    return this.on('presence', callback as EventCallback);
  }

  // Subscribe to read receipts
  onReadReceipt(callback: (event: ReadReceiptEvent) => void): () => void {
    return this.on('read_receipt', callback as EventCallback);
  }

  isConnected(): boolean {
    const connected = this.eventSource?.readyState === EventSource.OPEN;
    console.log('📊 SSE: Connection status check:', {
      connected,
      readyState: this.eventSource?.readyState,
      readyStateText: this.eventSource?.readyState === EventSource.CONNECTING ? 'CONNECTING' :
        this.eventSource?.readyState === EventSource.OPEN ? 'OPEN' :
          this.eventSource?.readyState === EventSource.CLOSED ? 'CLOSED' : 'UNKNOWN'
    });
    return connected;
  }
}

export const sse = new SSEService();
