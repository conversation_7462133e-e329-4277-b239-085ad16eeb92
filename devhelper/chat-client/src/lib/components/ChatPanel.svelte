<script lang="ts">
  import { createEventDispatcher, afterUpdate } from "svelte"
  import ChatInput from "./ChatInput.svelte"
  import MessageBubble from "./MessageBubble.svelte"

  import type { Message, Conversation } from "../types"

  export let conversation: Conversation | null = null

  const dispatch = createEventDispatcher<{
    sendMessage: { text: string }
    updateConversation: { id: string; [key: string]: any }
    clearMessages: { id: string }
    deleteConversation: { id: string }
    duplicateConversation: Conversation
  }>()

  let messagesContainer: HTMLDivElement
  let editingFrom = false
  let editingTo = false
  let editingName = false
  let tempFrom = ""
  let tempTo = ""
  let tempName = ""

  function handleSendMessage(event: CustomEvent<{ text: string }>): void {
    dispatch("sendMessage", event.detail)
  }

  function startEditingFrom(): void {
    editingFrom = true
    tempFrom = conversation?.from || ""
  }

  function startEditingTo(): void {
    editingTo = true
    tempTo = conversation?.to || conversation?.name || ""
  }

  function startEditingName(): void {
    editingName = true
    tempName = conversation?.name || ""
  }

  function saveFrom(): void {
    if (tempFrom.trim() && conversation) {
      dispatch("updateConversation", {
        id: conversation.id,
        from: tempFrom.trim(),
      })
    }
    editingFrom = false
  }

  function saveTo(): void {
    if (tempTo.trim() && conversation) {
      dispatch("updateConversation", {
        id: conversation.id,
        to: tempTo.trim(),
        name: tempTo.trim(),
      })
    }
    editingTo = false
  }

  function saveName(): void {
    if (tempName.trim() && conversation) {
      dispatch("updateConversation", {
        id: conversation.id,
        name: tempName.trim(),
      })
    }
    editingName = false
  }

  function cancelEditingFrom(): void {
    editingFrom = false
    tempFrom = ""
  }

  function cancelEditingTo(): void {
    editingTo = false
    tempTo = ""
  }

  function cancelEditingName(): void {
    editingName = false
    tempName = ""
  }

  function handleKeydown(
    event: KeyboardEvent,
    type: "from" | "to" | "name",
  ): void {
    if (event.key === "Enter") {
      if (type === "from") {
        saveFrom()
      } else if (type === "to") {
        saveTo()
      } else if (type === "name") {
        saveName()
      }
    } else if (event.key === "Escape") {
      if (type === "from") {
        cancelEditingFrom()
      } else if (type === "to") {
        cancelEditingTo()
      } else if (type === "name") {
        cancelEditingName()
      }
    }
  }

  // Auto-scroll to bottom when new messages are added
  afterUpdate(() => {
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight
    }
  })

  // Clear all messages in the conversation
  function clearMessages(): void {
    if (
      conversation &&
      confirm(
        "Are you sure you want to clear all messages? This action cannot be undone.",
      )
    ) {
      dispatch("clearMessages", { id: conversation.id })
    }
  }

  // Delete the entire conversation
  function deleteConversation(): void {
    if (
      conversation &&
      confirm(
        `Are you sure you want to delete the conversation with "${conversation.name}"? This action cannot be undone.`,
      )
    ) {
      dispatch("deleteConversation", { id: conversation.id })
    }
  }

  // Duplicate the conversation
  function duplicateConversation(): void {
    if (conversation) {
      const duplicatedConversation: Conversation = {
        id: Date.now().toString(), // New unique ID
        sessionId: conversation.sessionId, // Keep same session ID
        name: `${conversation.name} (Copy)`, // Add "(Copy)" to name
        from: `${conversation.from}_copy`, // Modify FROM to make it unique
        to: conversation.to, // Keep same TO
        status: conversation.status,
        lastMessage: "",
        timestamp: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        messages: [], // Start with empty messages
      }

      dispatch("duplicateConversation", duplicatedConversation)
    }
  }

  // Debug: Log when conversation changes
  $: if (conversation) {
    console.log(
      "ChatPanel: Conversation updated:",
      conversation.id,
      "Messages count:",
      conversation.messages?.length || 0,
    )
  }
</script>

<div class="chat-panel" id="chat-panel" data-identifier="chat-panel">
  {#if conversation}
    <div class="chat-header" id="chat-header" data-identifier="chat-header">
      <div class="chat-title-container" data-identifier="chat-title-container">
        {#if editingName}
          <input
            type="text"
            id="edit-name-input"
            data-identifier="edit-name-input"
            bind:value={tempName}
            on:keydown={(e) => handleKeydown(e, "name")}
            on:blur={saveName}
            class="title-input"
            autofocus
          />
          <button
            class="title-btn save"
            id="save-name-btn"
            data-identifier="save-name-btn"
            on:click={saveName}>✓</button
          >
          <button
            class="title-btn cancel"
            id="cancel-name-btn"
            data-identifier="cancel-name-btn"
            on:click={cancelEditingName}>✕</button
          >
        {:else}
          <div
            class="chat-title"
            id="chat-title"
            data-identifier="chat-title"
            data-conversation-id={conversation.id}
            data-name-value={conversation.name}
            on:click={startEditingName}
          >
            {conversation.name}
          </div>
          <button
            class="title-btn edit"
            id="edit-title-btn"
            data-identifier="edit-title-btn"
            on:click={startEditingName}>✎</button
          >
        {/if}
      </div>

      <div class="chat-actions">
        <button
          class="action-btn duplicate-btn"
          title="Duplicate conversation"
          on:click={duplicateConversation}
        >
          📋 Duplicate
        </button>
        <button
          class="action-btn clear-btn"
          title="Clear all messages"
          on:click={clearMessages}
        >
          🗑️ Clear
        </button>
        <button
          class="action-btn delete-btn"
          title="Delete conversation"
          on:click={deleteConversation}
        >
          ❌ Delete
        </button>
      </div>

      <div class="chat-info" id="chat-fields" data-identifier="chat-fields">
        <div class="chat-field" data-identifier="from-field-group">
          <span class="field-label" data-identifier="from-field-label"
            >FROM:</span
          >
          {#if editingFrom}
            <input
              type="text"
              id="edit-from-input"
              data-identifier="edit-from-input"
              bind:value={tempFrom}
              on:keydown={(e) => handleKeydown(e, "from")}
              on:blur={saveFrom}
              class="field-input"
              autofocus
            />
            <button
              class="field-btn save"
              id="save-from-btn"
              data-identifier="save-from-btn"
              on:click={saveFrom}>✓</button
            >
            <button
              class="field-btn cancel"
              id="cancel-from-btn"
              data-identifier="cancel-from-btn"
              on:click={cancelEditingFrom}>✕</button
            >
          {:else}
            <span
              class="field-value"
              id="from-field-value"
              data-identifier="from-field-value"
              data-value={conversation.from}
              on:click={startEditingFrom}>{conversation.from}</span
            >
            <button
              class="field-btn edit"
              id="edit-from-btn"
              data-identifier="edit-from-btn"
              on:click={startEditingFrom}>✎</button
            >
          {/if}
        </div>

        <div class="field-group" data-identifier="to-field-group">
          <span class="field-label" data-identifier="to-field-label">TO:</span>
          {#if editingTo}
            <input
              type="text"
              id="edit-to-input"
              data-identifier="edit-to-input"
              bind:value={tempTo}
              on:keydown={(e) => handleKeydown(e, "to")}
              on:blur={saveTo}
              class="field-input"
              autofocus
            />
            <button
              class="field-btn save"
              id="save-to-btn"
              data-identifier="save-to-btn"
              on:click={saveTo}>✓</button
            >
            <button
              class="field-btn cancel"
              id="cancel-to-btn"
              data-identifier="cancel-to-btn"
              on:click={cancelEditingTo}>✕</button
            >
          {:else}
            <span
              class="field-value"
              id="to-field-value"
              data-identifier="to-field-value"
              data-value={conversation.to || conversation.name}
              on:click={startEditingTo}
              >{conversation.to || conversation.name}</span
            >
            <button
              class="field-btn edit"
              id="edit-to-btn"
              data-identifier="edit-to-btn"
              on:click={startEditingTo}>✎</button
            >
          {/if}
        </div>
      </div>
    </div>

    <div
      class="chat-messages scrollbar-thin"
      id="chat-messages"
      data-identifier="chat-messages"
      data-conversation-id={conversation.id}
      bind:this={messagesContainer}
    >
      {#each conversation.messages as message (message.id)}
        <MessageBubble {message} />
      {/each}
    </div>

    <ChatInput on:sendMessage={handleSendMessage} />
  {:else}
    <div
      class="empty-state"
      id="chat-empty-state"
      data-identifier="chat-empty-state"
    >
      <h3>Select a conversation</h3>
      <p>Choose a conversation from the list to start chatting</p>
    </div>
  {/if}
</div>

<style>
  .chat-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .chat-title {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .chat-title:hover {
    background-color: #f8f9fa;
  }

  .title-input {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #2196f3;
    border-radius: 4px;
    outline: none;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .title-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.2s;
  }

  .title-btn.edit {
    color: #666;
  }

  .title-btn.edit:hover {
    background: #f0f0f0;
    color: #333;
  }

  .title-btn.save {
    color: #4caf50;
  }

  .title-btn.save:hover {
    background: #e8f5e8;
  }

  .title-btn.cancel {
    color: #f44336;
  }

  .title-btn.cancel:hover {
    background: #ffeaea;
  }

  /* Chat Actions */
  .chat-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
  }

  .chat-actions .action-btn {
    background: none;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
  }

  .chat-actions .action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .chat-actions .action-btn.duplicate-btn {
    color: #2196f3;
    border-color: #2196f3;
  }

  .chat-actions .action-btn.duplicate-btn:hover {
    background: #e3f2fd;
    border-color: #1976d2;
    color: #1976d2;
  }

  .chat-actions .action-btn.clear-btn {
    color: #ff9800;
    border-color: #ff9800;
  }

  .chat-actions .action-btn.clear-btn:hover {
    background: #fff3e0;
    border-color: #f57c00;
    color: #f57c00;
  }

  .chat-actions .action-btn.delete-btn {
    color: #f44336;
    border-color: #f44336;
  }

  .chat-actions .action-btn.delete-btn:hover {
    background: #ffeaea;
    border-color: #d32f2f;
    color: #d32f2f;
  }

  .chat-fields {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .field-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .field-label {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    min-width: 50px;
  }

  .field-value {
    flex: 1;
    padding: 4px 8px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
  }

  .field-value:hover {
    background: #e9ecef;
  }

  .field-input {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #2196f3;
    border-radius: 4px;
    outline: none;
    font-size: 14px;
  }

  .field-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.2s;
  }

  .field-btn.edit {
    color: #666;
  }

  .field-btn.edit:hover {
    background: #f0f0f0;
    color: #333;
  }

  .field-btn.save {
    color: #4caf50;
  }

  .field-btn.save:hover {
    background: #e8f5e8;
  }

  .field-btn.cancel {
    color: #f44336;
  }

  .field-btn.cancel:hover {
    background: #ffeaea;
  }
</style>
