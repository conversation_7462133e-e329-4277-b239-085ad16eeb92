<script lang="ts">
  import type { Message } from "../types"

  export let message: Message

  function formatTime(timestamp: Date | string): string {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    })
  }
</script>

<div
  class="message-bubble {message.fromMe ? 'sent' : 'received'}"
  id="message-{message.id}"
  data-identifier="message-bubble"
  data-message-id={message.id}
  data-message-type={message.fromMe ? "sent" : "received"}
  data-message-text={message.text}
  data-message-timestamp={message.timestamp}
>
  <div class="message-text" data-identifier="message-text">{message.text}</div>
  <div class="message-time" data-identifier="message-time">
    {formatTime(message.timestamp)}
  </div>
</div>

<style>
  .message-text {
    line-height: 1.4;
  }
</style>
