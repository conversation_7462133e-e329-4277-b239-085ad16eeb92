// SSE utility functions for broadcasting events

// Store active SSE connections with metadata
interface ConnectionInfo {
  controller: ReadableStreamDefaultController;
  createdAt: Date;
  lastActivity: Date;
}

export const connections = new Set<ReadableStreamDefaultController>();
const connectionMetadata = new Map<ReadableStreamDefaultController, ConnectionInfo>();

// Helper function to check if a connection is still valid
function isConnectionValid(controller: ReadableStreamDefaultController): boolean {
  try {
    // Check if the controller is still writable
    return controller.desiredSize !== null;
  } catch (error) {
    return false;
  }
}

// Helper function to clean up stale connections
export function cleanupStaleConnections(): void {
  const staleConnections: ReadableStreamDefaultController[] = [];

  connections.forEach(controller => {
    if (!isConnectionValid(controller)) {
      staleConnections.push(controller);
    }
  });

  staleConnections.forEach(controller => {
    removeConnection(controller);
  });

  if (staleConnections.length > 0) {
    console.log(`Cleaned up ${staleConnections.length} stale SSE connections`);
  }
}

// Helper function to broadcast events to all connected clients
export function broadcastEvent(event: {
  type: string;
  data: any;
  conversationId?: string;
}): void {
  // Clean up stale connections first
  cleanupStaleConnections();

  if (connections.size === 0) {
    console.log('No active SSE connections to broadcast to');
    return;
  }

  const eventData = `event: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
  const encodedData = new TextEncoder().encode(eventData);

  let successCount = 0;
  let errorCount = 0;

  connections.forEach(controller => {
    try {
      if (isConnectionValid(controller)) {
        controller.enqueue(encodedData);

        // Update last activity
        const metadata = connectionMetadata.get(controller);
        if (metadata) {
          metadata.lastActivity = new Date();
        }

        successCount++;
      } else {
        // Connection is no longer valid, will be cleaned up in next cleanup
        errorCount++;
      }
    } catch (error) {
      // Connection error, will be cleaned up in next cleanup
      errorCount++;
    }
  });

  if (errorCount > 0) {
    console.log(`SSE broadcast: ${successCount} successful, ${errorCount} failed`);
  }
}

// Helper function to send event to specific conversation
export function broadcastToConversation(conversationId: string, event: {
  type: string;
  data: any;
}): void {
  broadcastEvent({
    ...event,
    conversationId,
  });
}

// Helper function to add a connection
export function addConnection(controller: ReadableStreamDefaultController): void {
  connections.add(controller);
  connectionMetadata.set(controller, {
    controller,
    createdAt: new Date(),
    lastActivity: new Date()
  });
  console.log(`SSE connection added. Total connections: ${connections.size}`);
}

// Helper function to remove a connection
export function removeConnection(controller: ReadableStreamDefaultController): void {
  connections.delete(controller);
  connectionMetadata.delete(controller);
  console.log(`SSE connection removed. Total connections: ${connections.size}`);
}



// Helper function to get connection count
export function getConnectionCount(): number {
  return connections.size;
}
