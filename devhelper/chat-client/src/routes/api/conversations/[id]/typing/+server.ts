import { json, error } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

// Mock typing status storage
let typingData: Record<string, any> = {};

export const POST: RequestHandler = async ({ params, request }) => {
  const body = await request.json();
  
  // Store typing status for the conversation/session
  typingData[params.id] = {
    status: body.status, // 'typing' or 'stopped'
    conversationId: body.conversationId,
    timestamp: body.timestamp || new Date().toISOString(),
    sessionId: params.id
  };

  // Auto-clear typing status after 5 seconds if it's 'typing'
  if (body.status === 'typing') {
    setTimeout(() => {
      if (typingData[params.id]?.status === 'typing') {
        typingData[params.id].status = 'stopped';
      }
    }, 5000);
  }

  return json({
    status: 'success',
    data: {
      message: 'Typing status updated successfully',
      status: body.status,
      sessionId: params.id,
      conversationId: body.conversationId
    }
  });
};

export const GET: RequestHandler = async ({ params }) => {
  const typing = typingData[params.id];

  if (!typing) {
    return json({
      status: 'success',
      data: {
        status: 'stopped',
        sessionId: params.id,
        timestamp: new Date().toISOString()
      }
    });
  }

  return json({
    status: 'success',
    data: typing
  });
};
