import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { ConversationService, MessageService, mongodb } from '../../../../../lib/mongodb';
import { broadcastEvent } from '../../../../../lib/sse-utils';
import { getServerEnv } from '$lib/env';

const env = getServerEnv();
const CS_AI_BASE_URL = env.CS_AI_BASE_URL!;
const INTERNAL_SECRET_TOKEN = env.INTERNAL_SECRET_TOKEN!;

export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const conversation = await ConversationService.getConversation(params.id);

    if (!conversation) {
      throw error(404, 'Conversation not found');
    }

    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Get messages from database
    const messages = await MessageService.getMessages(params.id, limit, offset);
    const total = await MessageService.getMessageCount(params.id);

    return json({
      status: 'success',
      data: {
        conversation: {
          id: conversation.id,
          sessionId: conversation.sessionId,
          name: conversation.name,
          from: conversation.from,
          to: conversation.to,
          status: conversation.status
        },
        messages,
        total
      }
    });
  } catch (err) {
    console.error('Error fetching messages:', err);
    throw error(500, 'Internal server error');
  }
};

export const POST: RequestHandler = async ({ params, request }) => {
  try {
    const body = await request.json();

    // Verify conversation exists
    const conversation = await ConversationService.getConversation(params.id);
    if (!conversation) {
      throw error(404, 'Conversation not found');
    }

    // Create new message
    const newMessage = await MessageService.createMessage(params.id, {
      text: body.text,
      fromMe: body.fromMe || false,
      timestamp: new Date(body.timestamp || Date.now())
    });


    // send to cs-ai webhook
    const formatToWhatsAppId = (number: string): string => {
      if (!number.includes('@')) {
        return `${number}@c.us`;
      }
      return number;
    };

    const payload: WahaWebhookPayload = {
      id: newMessage.id,
      timestamp: new Date().getTime(),
      event: "message",
      session: conversation.sessionId!,
      metadata: {},
      me: {
        id: formatToWhatsAppId(conversation.from),
        pushName: conversation.name || "unknown",
      },
      payload: {
        id: newMessage.id,
        timestamp: new Date().getTime(),
        from: formatToWhatsAppId(conversation.from),
        fromMe: newMessage.fromMe,
        source: "user",
        body: newMessage.text,
        hasMedia: false,
        media: null,
        ack: 1,
        ackName: "delivered",
        replyTo: null,
        _data: {
          key: {
            remoteJid: formatToWhatsAppId(conversation.to!),
            fromMe: newMessage.fromMe,
            id: newMessage.id
          },
          messageTimestamp: new Date().getTime(),
          pushName: conversation.name || "unknown",
          broadcast: false,
          message: {
            conversation: newMessage.text
          },
          status: 0
        }
      },
      engine: "chat-client",
      environment: {
        version: "1.0.0",
        engine: "chat-client",
        tier: "production",
        browser: null
      }
    };


    const response = await fetch(`${CS_AI_BASE_URL}/api/v1/functions/webhook`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Internal-System-Token": INTERNAL_SECRET_TOKEN,
      },
      body: JSON.stringify(payload),
    });

    const responseBody = await response.json();
    // console.log("Webhook response:", response);
    // console.log("Webhook response body:", responseBody);


    // Get updated conversation
    const updatedConversation = await ConversationService.getConversation(params.id);


    // Broadcast new message event via SSE
    broadcastEvent({
      type: 'message',
      data: {
        message: newMessage,
        conversation: {
          id: updatedConversation!.id,
          sessionId: updatedConversation!.sessionId,
          name: updatedConversation!.name,
          from: updatedConversation!.from,
          to: updatedConversation!.to,
          status: updatedConversation!.status,
          lastMessage: updatedConversation!.lastMessage,
          timestamp: updatedConversation!.timestamp
        }
      },
      conversationId: params.id
    });

    // Broadcast conversation update
    broadcastEvent({
      type: 'conversation_update',
      data: {
        conversation: updatedConversation!,
        action: 'updated'
      },
      conversationId: params.id
    });

    return json({
      status: 'success',
      data: newMessage
    }, { status: 201 });
  } catch (err) {
    console.error('Error creating message:', err);
    throw error(500, 'Internal server error');
  }
};

export const DELETE: RequestHandler = async ({ params }) => {
  try {
    // Verify conversation exists
    const conversation = await ConversationService.getConversation(params.id);
    if (!conversation) {
      throw error(404, 'Conversation not found');
    }

    // Clear all messages for this conversation
    const messagesCollection = mongodb.getMessagesCollection();
    const result = await messagesCollection.deleteMany({ conversationId: params.id });

    // Update conversation to clear last message
    await ConversationService.updateConversation(params.id, {
      lastMessage: '',
      updatedAt: new Date()
    });

    console.log(`Cleared ${result.deletedCount} messages from conversation ${params.id}`);

    return json({
      status: 'success',
      data: {
        cleared: true,
        deletedCount: result.deletedCount
      }
    });
  } catch (err) {
    console.error('Error clearing messages:', err);
    throw error(500, 'Internal server error');
  }
};



/// this is the webhook format

export interface WahaWebhookPayload {
  id: string
  timestamp: number
  event: "message"
  session: string
  metadata: Record<string, any>
  me: {
    id: string
    pushName: string
  }
  payload: MessagePayload
  engine: string
  environment: Environment
}

export interface MessagePayload {
  id: string
  timestamp: number
  from: string
  fromMe: boolean
  source: string
  body: string
  hasMedia: boolean
  media: any | null
  ack: number
  ackName: string
  replyTo: any | null
  _data: MessageData
}

export interface MessageData {
  key: {
    remoteJid: string
    fromMe: boolean
    id: string
  }
  messageTimestamp: number
  pushName: string
  broadcast: boolean
  message: {
    conversation: string
    messageContextInfo?: {
      deviceListMetadata?: {
        recipientKeyHash: string
        recipientTimestamp: string
      }
      deviceListMetadataVersion?: number
      messageSecret?: string
    }
  }
  verifiedBizName?: string
  status: number
}

export interface Environment {
  version: string
  engine: string
  tier: string
  browser: string | null
}
