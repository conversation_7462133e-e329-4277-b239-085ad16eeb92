# Chat Client

A simple chat client built with Svelte and SvelteKit.

## Features

- **Conversations List**: Right panel showing list of conversations
- **FROM Field**: Each conversation displays the sender's phone number/identifier
- **Chat Bubbles**: Messages displayed as chat bubbles (sent/received)
- **Chat Input**: Input field at the bottom for typing messages
- **Responsive Design**: Clean and modern UI
- **Real-time Updates**: Messages update in real-time

## Project Structure

```
src/
├── app.css                           # Global styles
├── app.html                          # HTML template
├── lib/
│   └── components/
│       ├── ChatInput.svelte          # Message input component
│       ├── ChatPanel.svelte          # Main chat area
│       ├── ConversationsList.svelte  # Conversations sidebar
│       └── MessageBubble.svelte      # Individual message bubble
└── routes/
    ├── +layout.svelte                # Layout wrapper
    └── +page.svelte                  # Main page component
```

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to `http://localhost:5173`

## Usage

- Click on any conversation in the right panel to open it
- Type messages in the input field at the bottom
- Press Enter or click Send to send messages
- Messages appear as bubbles with timestamps
- Sent messages appear on the right (blue), received on the left (white)

## Mock Data

The app currently uses mock data with sample conversations. In a real implementation, you would:

1. Connect to a WebSocket or API for real-time messaging
2. Implement user authentication
3. Add message persistence
4. Add file/media sharing capabilities
5. Implement typing indicators
6. Add message status indicators (sent, delivered, read)

## Customization

### Styling
- Modify `src/app.css` for global styles
- Each component has its own `<style>` section for component-specific styles

### Adding Features
- **Typing Indicators**: Add to ChatPanel component
- **Message Status**: Extend MessageBubble component
- **File Sharing**: Add to ChatInput component
- **Search**: Add to ConversationsList component

## Build for Production

```bash
npm run build
```

The built files will be in the `build/` directory.

## Technologies Used

- **Svelte 4**: Reactive UI framework
- **SvelteKit**: Full-stack framework
- **Vite**: Build tool and dev server
- **CSS**: Custom styling (no external CSS framework)

## Browser Support

Modern browsers that support ES6+ features.
