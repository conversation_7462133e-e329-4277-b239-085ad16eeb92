import { MongoDriver } from "@/lib/repositories/MongoDriver"
import {
  EventSender,
  SystemEvent,
  UserEvent,
  TestEvent,
  TestSessionEvent,
} from "./EventSender"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { Ka<PERSON><PERSON>, Partitioners, Producer } from "kafkajs"

interface EventStoreDocument {
  _id?: any
  type: string
  payload: any
  timestamp: Date
  version: number
  createdAt: Date
}

type EventStoreType = UserEvent | SystemEvent | TestEvent | TestSessionEvent

export class KafkaEventSender implements EventSender {
  private kafkaProducer: Producer

  constructor(
    private db: MongoDriver,
    kafkaBrokerList: string[],
  ) {
    const kafka = new Kafka({
      clientId: "nextjs-be-api",
      brokers: kafkaBrokerList, // e.g. ["localhost:9092"]
    })
    this.kafkaProducer = kafka.producer({
      createPartitioner: Partitioners.LegacyPartitioner,
    })
  }

  async connect() {
    await this.kafkaProducer.connect()
  }

  async disconnect() {
    await this.kafkaProducer.disconnect()
  }

  private async saveEventToStore(
    event: EventStoreType,
    timestamp: Date,
  ): Promise<string> {
    const collection = this.db.getCollection<EventStoreDocument>(
      MONGO_COLLECTIONS.EVENT_STORE,
    )

    const eventDoc: EventStoreDocument = {
      type: event.type,
      payload: event.payload,
      timestamp,
      version: 1,
      createdAt: new Date(),
    }

    const result = await collection.insertOne(eventDoc)
    return result.insertedId.toString()
  }

  private async sendToKafka(topic: string, message: any, key: string) {
    await this.kafkaProducer.send({
      topic,
      messages: [
        {
          key: key,
          value: JSON.stringify(message),
        },
      ],
    })
  }

  async sendUserEvent(event: UserEvent): Promise<string> {
    const timestamp = new Date()
    const eventId = await this.saveEventToStore(event, timestamp)

    const kafkaMessage = {
      event: eventId,
      type: event.type,
      timestamp,
      version: 1,
    }

    await this.sendToKafka("user-events", kafkaMessage, event.payload.user_id)

    return eventId
  }

  async sendTestEvent(event: TestEvent): Promise<string> {
    const timestamp = new Date()
    const eventId = await this.saveEventToStore(event, timestamp)

    const kafkaMessage = {
      event: eventId,
      type: event.type,
      timestamp,
      version: 1,
    }

    await this.sendToKafka("test-events", kafkaMessage, event.payload.test_id)

    return eventId
  }

  async sendTestSessionEvent(event: TestSessionEvent): Promise<string> {
    const timestamp = new Date()
    const eventId = await this.saveEventToStore(event, timestamp)
    const kafkaMessage = {
      event: eventId,
      type: event.type,
      timestamp,
      version: 1,
    }

    await this.sendToKafka(
      "test-session-events",
      kafkaMessage,
      event.payload.user_id,
    )

    return eventId
  }

  async sendSystemEvent(event: SystemEvent): Promise<string> {
    const timestamp = new Date()
    const eventId = await this.saveEventToStore(event, timestamp)

    const kafkaMessage = {
      event: eventId,
      type: event.type,
      timestamp,
      version: 1,
    }

    // Send to Kafka topic (e.g., "system-events")
    await this.sendToKafka("system-events", kafkaMessage, "system")

    return eventId
  }
}
