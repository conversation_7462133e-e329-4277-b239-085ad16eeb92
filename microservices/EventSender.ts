// Base event structure with snake_case type naming
export interface BaseEvent<TType extends string, TPayload = any> {
  type: TType
  payload: TPayload
}

// ✨ User test-related events
export type TestEvent =
  | BaseEvent<"test_created", { test_id: string; user_id: string }>
  | BaseEvent<"test_updated", { test_id: string; user_id: string }>
  | BaseEvent<"test_archived", { test_id: string; user_id: string }>

export type TestSessionEvent =
  | BaseEvent<
      "test_session_created",
      { test_session_id: string; user_id: string }
    >
  | BaseEvent<
      "test_session_completed",
      { test_session_id: string; user_id: string }
    >

// ✨ User authentication events
export type UserEvent =
  | BaseEvent<"password_changed", { user_id: string }>
  | BaseEvent<"email_verified", { user_id: string }>
  | BaseEvent<"account_login", { user_id: string }>
  | BaseEvent<"account_deleted", { user_id: string }>
  | BaseEvent<"user_registered", { user_id: string; email: string }>
  | BaseEvent<
      "password_reset_requested",
      { user_id: string; reset_token: string }
    >
  | BaseEvent<"password_reset_completed", { user_id: string }>
  | BaseEvent<"token_refreshed", { user_id: string; old_token_id: string }>

// ⚙️ System-level events
export type SystemEvent =
  | BaseEvent<"system", { message: string }>
  | BaseEvent<"test_reminder", { test_id: string; user_id: string }>

// 🎯 Event sender interface
export interface EventSender {
  sendUserEvent(event: UserEvent): Promise<string>
  sendTestEvent(event: TestEvent): Promise<string>
  sendTestSessionEvent(event: TestSessionEvent): Promise<string>
  sendSystemEvent(event: SystemEvent): Promise<string>
}
