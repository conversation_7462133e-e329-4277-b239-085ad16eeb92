function isObject(item: any): item is Record<string, any> {
  return item && typeof item === "object" && !Array.isArray(item)
}

export function deepMerge<T extends object, U extends object>(
  target: T,
  source: U,
): T & U {
  const result: any = { ...target }

  for (const key in source) {
    if (isObject(source[key]) && isObject(result[key])) {
      result[key] = deepMerge(result[key], source[key])
    } else {
      result[key] = source[key]
    }
  }

  return result
}
