import { SearchConfigApiResponseVisitor } from "@/app/api/v1/search-configs/SearchConfigApiResponseVisitor"
import { aiRuleSearchConfig } from "@/app/api/v1/search-configs/entities/aiRule"
import { contactsSearchConfig } from "@/app/api/v1/search-configs/entities/contacts"
import { customerProfilesSearchConfig } from "@/app/api/v1/search-configs/entities/customerProfiles"
import { datasourcesSearchConfig } from "@/app/api/v1/search-configs/entities/datasources"
import { devicesSearchConfig } from "@/app/api/v1/search-configs/entities/devices"
import { messageTemplatesSearchConfig } from "@/app/api/v1/search-configs/entities/messageTemplates"
import { workflowExecutionsSearchConfig } from "@/app/api/v1/search-configs/entities/workflowExecutions"
import { SearchConfigEntity } from "@/app/api/v1/search-configs/entities/interface"
import { SearchConfig } from "@/lib/types/searchConfig"

const entities: Record<string, SearchConfigEntity> = {
  contacts: contactsSearchConfig,
  workflowExecutions: workflowExecutionsSearchConfig,
  aiRules: aiRuleSearchConfig,
  messageTemplates: messageTemplatesSearchConfig,
  customerProfiles: customerProfilesSearchConfig,
  datasources: datasourcesSearchConfig,
  devices: devicesSearchConfig,
}

export function hasEntity(entityName: string): boolean {
  return entityName in entities
}

export function getRegisteredEntities(): string[] {
  return Object.keys(entities)
}

export function buildSearchConfig(entityName: string): SearchConfig | null {
  const entity = entities[entityName]
  if (!entity) {
    return null
  }

  const visitor = new SearchConfigApiResponseVisitor()
  entity.buildForApiResponse(visitor)
  
  return visitor.getResult()
}

// Helper functions for specific entities
export function getContactsSearchConfig(): SearchConfig | null {
  return buildSearchConfig("contacts")
}

export function getAiRulesSearchConfig(): SearchConfig | null {
  return buildSearchConfig("aiRules")
}

export function getMessageTemplatesSearchConfig(): SearchConfig | null {
  return buildSearchConfig("messageTemplates")
}

export function getCustomerProfilesSearchConfig(): SearchConfig | null {
  return buildSearchConfig("customerProfiles")
}

export function getDatasourcesSearchConfig(): SearchConfig | null {
  return buildSearchConfig("datasources")
}

export function getDevicesSearchConfig(): SearchConfig | null {
  return buildSearchConfig("devices")
}

export function getWorkflowExecutionsSearchConfig(): SearchConfig | null {
  return buildSearchConfig("workflowExecutions")
}
