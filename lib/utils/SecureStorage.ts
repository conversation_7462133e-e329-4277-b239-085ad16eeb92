type StorageType = "local" | "cookie" | "session"

class StorageKey {
  constructor(
    public key: string,
    public storageType: StorageType,
  ) {}
}

export const StorageKeys = {
  IsLoggedIn: new StorageKey("is_logged_in", "local"),
  UserID: new StorageKey("user_id", "local"),
  FullName: new StorageKey("user_full_name", "local"),
  Email: new StorageKey("user_email", "local"),
  CurrentOrganization: new StorageKey("organization_id", "cookie"),
} as const

export type StorageKeyType = (typeof StorageKeys)[keyof typeof StorageKeys]

class SecureStorage {
  private static instance: SecureStorage

  private constructor() {}

  public static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage()
    }
    return SecureStorage.instance
  }

  public setItem(key: StorageKeyType, value: string): void {
    try {
      switch (key.storageType) {
        case "local":
          localStorage.setItem(key.key, value)
          break
        case "session":
          sessionStorage.setItem(key.key, value)
          break
        case "cookie":
          document.cookie = `${key.key}=${encodeURIComponent(value)}; path=/`
          break
      }
    } catch (error) {
      console.error(`Error setting item for key "${key.key}"`, error)
      throw error
    }
  }

  public getItem(key: StorageKeyType): string | null {
    try {
      switch (key.storageType) {
        case "local":
          return localStorage.getItem(key.key)
        case "session":
          return sessionStorage.getItem(key.key)
        case "cookie": {
          const match = document.cookie.match(
            new RegExp("(^| )" + key.key + "=([^;]+)"),
          )
          return match ? decodeURIComponent(match[2]) : null
        }
      }
    } catch (error) {
      console.error(`Error getting item for key "${key.key}"`, error)
      throw error
    }
  }

  public removeItem(key: StorageKeyType): void {
    try {
      switch (key.storageType) {
        case "local":
          localStorage.removeItem(key.key)
          break
        case "session":
          sessionStorage.removeItem(key.key)
          break
        case "cookie":
          document.cookie = `${key.key}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`
          break
      }
    } catch (error) {
      console.error(`Error removing item for key "${key.key}"`, error)
      throw error
    }
  }
}

export const secureStorage = SecureStorage.getInstance()
