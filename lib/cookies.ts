// src/lib/cookies.ts
import { cookies } from "next/headers"

export async function setAuth<PERSON><PERSON>ie(name: string, value: string) {
  ;(await cookies()).set(name, value, {
    httpOnly: true,
    sameSite: "lax",
    secure: true,
    path: "/",
  })
}

export async function getAuthCookie(name: string): Promise<string | undefined> {
  return (await cookies()).get(name)?.value
}

export async function hasAuthCookie(name: string): Promise<boolean> {
  return !!(await cookies()).get(name)
}

export async function removeAuthCookie(name: string) {
  ;(await cookies()).delete(name)
}
