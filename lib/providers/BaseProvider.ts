// src/lib/providers/BaseProvider.ts
import axios, { AxiosInstance } from "axios"

export abstract class BaseProvider {
  public name: string
  protected client: AxiosInstance

  constructor(name: string, baseURL: string) {
    this.name = name
    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    })
  }

  /**
   * Membuat session baru jika perlu.
   * Kalau provider tidak butuh session, TIDAK WAJIB implement.
   */
  abstract createSession(): Promise<string>
  abstract restartSession?(sessionId: string): Promise<string>
  abstract startSession?(sessionId: string): Promise<string>
  abstract stopSession?(sessionId: string): Promise<string>

  /**
   * Mendapatkan QR code.
   */
  abstract getQr(sessionId?: string): Promise<string>

  abstract logout(sessionId: string): Promise<string>

  /**
   * List connected devices.
   */
  abstract listDevices(): Promise<any>

  abstract listConversations(
    sessionId?: string,
    limit?: number,
    offset?: number,
    filter?: string[],
  ): Promise<any>

  abstract receiveMessages(
    sessionId?: string,
    conversationId?: string,
    limit?: number,
    offset?: number,
  ): Promise<any>

  abstract readMessage(
    conversationId: string,
    sessionId?: string,
  ): Promise<string>

  abstract sendPresenceStatus(
    presence: string,
    sessionId?: string,
  ): Promise<string>

  abstract sendPresenceStatus(
    conversationId: string, // reply_message_id Gowa
    text: string, // message Gowa
    session: string, // phone Gowa
  ): Promise<string>

  abstract sendTyping(
    status: string,
    conversationId: string,
    sessionId?: string,
  ): Promise<string>
}
