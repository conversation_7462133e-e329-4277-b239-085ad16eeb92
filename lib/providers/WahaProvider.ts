import axios from "axios"
import { Provider } from "./types"

const WAHA_WEBHOOK_URL = process.env.WAHA_WEBHOOK_URL!
const INTERNAL_SECRET_TOKEN = process.env.INTERNAL_SECRET_TOKEN!

export function Waha<PERSON><PERSON>ider(baseURL: string, keyBase: string): Provider {
  const client = axios.create({
    baseURL: baseURL + "/api",
    headers: {
      "X-Api-Key": keyBase,
      "Content-Type": "application/json",
    },
  })

  return {
    name: "WAHA API",

    async createSession() {
      console.log("WAHA WEBHOOK_URL")
      const res = await client.post("/sessions", {
        start: true,
        config: {
          noweb: {
            store: {
              enabled: true,
              fullSync: true,
            },
          },
          webhooks: !WAHA_WEBHOOK_URL
            ? []
            : [
              {
                url: `${WAHA_WEBHOOK_URL}`,
                events: ["message", "session.status"],
                customHeaders: [
                  {
                    name: "X-Internal-System-Token",
                    value: INTERNAL_SECRET_TOKEN,
                  },
                ],
              },
              {
                url: `${WAHA_WEBHOOK_URL}/presence`,
                events: ["presence.update"],
                customHeaders: [
                  {
                    name: "X-Internal-System-Token",
                    value: INTERNAL_SECRET_TOKEN,
                  },
                ],
              },
            ],
        },
      })
      const sessionId = res.data?.name
      if (!sessionId) throw new Error("WAHA API tidak mengembalikan sessionId.")
      return sessionId
    },

    async startSession(sessionId: string) {
      const res = await client.post(`/sessions/${sessionId}/start`, {})
      if (!res) throw new Error(`WAHA API ${sessionId} start error.`)
      return res.data
    },

    async stopSession(sessionId: string) {
      const res = await client.post(`/sessions/${sessionId}/stop`, {})
      if (!res) throw new Error(`WAHA API ${sessionId} stop error.`)
      return res.data
    },

    async restartSession(sessionId: string) {
      const res = await client.post(`/sessions/${sessionId}/restart`, {})
      if (!res) throw new Error(`WAHA API ${sessionId} restart error.`)
      return res.data
    },

    async logoutSession(sessionId: string) {
      if (!sessionId) throw new Error("SessionId diperlukan untuk logout WAHA.")

      const res = await client.delete(`/sessions/${sessionId}`, {})
      if (!res) throw new Error(`WAHA API ${sessionId} logout error.`)
      return res.data
    },

    async getQr(sessionId?: string) {
      if (!sessionId) throw new Error("SessionId diperlukan untuk getQr WAHA.")

      const res = await client.get(`/${sessionId}/auth/qr`, {
        responseType: "arraybuffer",
      })

      const contentType = res.headers["content-type"]
      if (!contentType)
        throw new Error("Content-Type tidak ditemukan dalam response WAHA.")

      const base64Image = Buffer.from(res.data).toString("base64")

      if (!base64Image)
        throw new Error("QR tidak ditemukan dalam response WAHA.")

      return `data:${contentType};base64,${base64Image}`
    },

    async listDevices() {
      const res = await client.get("/sessions")
      return res.data
    },

    async listConversations(
      sessionId?: string,
      limit?: number,
      offset?: number,
      filter?: string[],
    ) {
      if (!sessionId)
        throw new Error("SessionId diperlukan untuk listConversations WAHA.")

      const res = await client.post(
        `/${sessionId}/conversations/overview?count=true&sortBy=conversationTimestamp&sortOrder=desc`,
        {
          pagination: {
            limit,
            offset,
          },
          filter,
        },
      )
      return res.data
    },

    async readMessage(conversationId: string, sessionId?: string) {
      if (!sessionId)
        throw new Error("SessionId diperlukan untuk readMessage WAHA.")
      if (!conversationId)
        throw new Error(
          "ConversationId diperlukan untuk readMessage WAHA.",
        )

      await client.post(
        `/${sessionId}/conversations/${conversationId}/messages/read`,
      )
      return "Success"
    },

    async receiveMessages(
      sessionId?: string,
      conversationId?: string,
      limit?: number,
      offset?: number,
    ) {
      if (!sessionId)
        throw new Error("SessionId diperlukan untuk receiveMessages WAHA.")
      if (!conversationId)
        throw new Error("ConversationId diperlukan untuk receiveMessages WAHA.")

      const res = await client.get(
        `/${sessionId}/conversations/${conversationId}/messages?limit=${limit}&offset=${offset}&sortBy=messageTimestamp&sortOrder=desc`,
      )

      // read message
      if (res.status === 200 && res.data?.length > 0)
        this.readMessage(conversationId, sessionId)

      if (!res.data) throw new Error("Tidak ada data pesan yang ditemukan.")
      return res.data.reverse()
    },

    async sendPresenceStatus(presence: string, sessionId?: string) {
      if (!sessionId)
        throw new Error("SessionId diperlukan untuk sendPresenceStatus WAHA.")
      const res = await client.post(`/${sessionId}/presence`, { presence })
      return "Success"
    },

    async sendMessage(
      conversationId: string, // reply_message_id Gowa
      text: string, // message Gowa
      session: string, // phone Gowa
    ) {
      if (!conversationId)
        throw new Error(
          "ConversationId diperlukan untuk sendMessage WAHA.",
        )
      if (!text) throw new Error("Text diperlukan untuk sendMessage WAHA.")
      if (!session)
        throw new Error("Session diperlukan untuk sendMessage WAHA.")

      const payload = {
        conversationId,
        text,
        session,
        reply_to: null,
        linkPreview: true,
        linkPreviewHighQuality: false,
      }

      const res = await client.post(`/sendText`, payload)
      return res.data
    },

    async sendTyping(
      status: string,
      conversationId: string,
      sessionId?: string,
    ) {
      if (!status) throw new Error("Status diperlukan untuk sendTyping WAHA.")
      if (!sessionId)
        throw new Error("SessionId diperlukan untuk readMessage WAHA.")
      if (!conversationId)
        throw new Error(
          "ConversationId diperlukan untuk readMessage WAHA.",
        )

      await client.post(status == "start" ? `/startTyping` : "/stopTyping", {
        conversationId,
        session: sessionId,
      })
      return "Success"
    },
  }
}
