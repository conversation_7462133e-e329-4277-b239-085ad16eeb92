// // src/lib/providers/createGowaProvider.ts
// import axios from "axios";
// import { Provider } from "./types";

// export function GowaProvider(
//   baseURL: string,
//   username: string,
//   password: string
// ): Provider {
//   const client = axios.create({
//     baseURL,
//     headers: { "Content-Type": "application/json" },
//   });

//   const authHeader = {
//     Authorization:
//       "Basic " + Buffer.from(`${username}:${password}`).toString("base64"),
//   };

//   return {
//     name: "Gowa API",

//     async logout() {
//       const res = await client.get(`/app/logout`, {
//         headers: authHeader,
//       });
//       if (!res) throw new Error(`Gowa API logout error.`);
//       return res.data?.results;
//     },

//     async getQr() {
//       const res = await client.get("/app/login", {
//         headers: authHeader,
//       });
//       const qr = res.data?.results?.qr_link;
//       if (!qr) throw new Error("QR tidak ditemukan dalam response Gowa.");
//       return qr;
//     },

//     async listDevices() {
//       const res = await client.get("/app/devices", {
//         headers: authHeader,
//       });
//       return res.data?.results;
//     },

//     async listConversations() {
//       const res = await client.get("/user/my/contacts", {
//         headers: authHeader,
//       });
//       return res.data?.results?.data;
//     },

//     async readMessage(conversationId: string, sessionId?: string) {
//       if (!conversationId) throw new Error("ConversationId diperlukan untuk readMessage Gowa.");

//       await client.post(`/message/${conversationId}/read`);
//       return "Success";
//     },

//     async receiveMessages(
//       sessionId?: string,
//       conversationId?: string,
//       limit?: number,
//       offset?: number
//     ) {
//       throw new Error("receiveMessages Gowa not have API.");
//     },

//     async sendPresenceStatus(presence: string, sessionId?: string) {
//       const res = await client.post(`/send/presence`, {
//         type:
//           presence == "online"
//             ? "available"
//             : presence == "offline"
//             ? "unavailable"
//             : "unavailable",
//         is_forwarded: false,
//       });
//       return res.data?.message;
//     },

//     async sendMessage(
//       conversationId: string, // reply_message_id Gowa
//       text: string, // message Gowa
//       session: string // phone Gowa
//     ) {
//       if (!session)
//         throw new Error("Session ID diperlukan untuk mengirim pesan.");
//       if (!text) throw new Error("Text diperlukan untuk mengirim pesan.");
//       if (!conversationId) throw new Error("Conversation ID diperlukan untuk mengirim pesan.");

//       const res = await client.post(`/sendText`, {
//         phone: session,
//         message: text,
//         reply_message_id: conversationId,
//         is_forwarded: false,
//       });
//       return res.data;
//     },

//     async sendTyping(status: string, conversationId: string, sessionId?: string) {
//       throw new Error("sendTyping Gowa not have API.");
//     },
//   };
// }
