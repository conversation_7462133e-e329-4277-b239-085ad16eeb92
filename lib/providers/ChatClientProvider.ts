import { Provider } from "./types"

const chatClientBaseURL = process.env.CHAT_CLIENT_BASE_URL!

export class ChatClientProvider implements Provider {
    name: string = "ChatClientProvider"

    async createSession(): Promise<string> {
        return "fake-session-id"
    }

    async restartSession(sessionId: string): Promise<string> {
        return "fake-session-id"
    }

    async startSession(sessionId: string): Promise<string> {
        return "fake-session-id"
    }

    async stopSession(sessionId: string): Promise<string> {
        return "fake-session-id"
    }

    async logoutSession(sessionId: string): Promise<string> {
        return "fake-session-id"
    }

    async getQr(sessionId?: string): Promise<string> {
        return "fake-session-id"
    }

    async listDevices(): Promise<any> {
        return []
    }

    async listConversations(sessionId?: string, limit?: number, offset?: number, filter?: string[]): Promise<any> {
        return []
    }

    async receiveMessages(sessionId?: string, conversationId?: string, limit?: number, offset?: number): Promise<any> {
        return []
    }

    async readMessage(conversationId: string, sessionId?: string): Promise<string> {
        return "read"
    }

    async sendPresenceStatus(presence: string, sessionId?: string): Promise<string> {
        return "presence"
    }

    async sendMessage(conversationId: string, text: string, session: string): Promise<string> {
        await fetch(`${chatClientBaseURL}/api/external/sendMessage`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                conversationId,
                text,
                sessionId: session
            }),
        })
        return "sent"
    }

    async sendTyping(status: string, conversationId: string, sessionId?: string): Promise<string> {
        return "foo"
    }
}
