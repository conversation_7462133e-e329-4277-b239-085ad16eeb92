// src/lib/providers/types.ts
export interface Provider {
  name: string
  createSession: () => Promise<string>
  restartSession?: (sessionId: string) => Promise<string>
  startSession?: (sessionId: string) => Promise<string>
  stopSession?: (sessionId: string) => Promise<string>
  logoutSession: (sessionId: string) => Promise<string>
  getQr: (sessionId?: string) => Promise<string>
  listDevices: () => Promise<any>
  listConversations: (
    sessionId?: string,
    limit?: number,
    offset?: number,
    filter?: string[],
  ) => Promise<any>
  receiveMessages: (
    sessionId?: string,
    conversationId?: string,
    limit?: number,
    offset?: number,
  ) => Promise<any>
  readMessage: (conversationId: string, sessionId?: string) => Promise<string>
  sendPresenceStatus: (presence: string, sessionId?: string) => Promise<string>
  sendMessage: (
    conversationId: string, // reply_message_id Gowa
    text: string, // message Gowa
    session: string, // phone Gowa
  ) => Promise<string>
  sendTyping: (
    status: string,
    conversationId: string,
    sessionId?: string,
  ) => Promise<string>
}
