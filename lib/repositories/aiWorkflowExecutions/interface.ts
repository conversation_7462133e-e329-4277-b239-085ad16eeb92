export interface AiWorkflowStep {
  id: string
  name: string
  status: "success" | "failure" | "in-progress" | "skipped"
  timestamp: string
  duration: number
  details?: string
  confidence?: number
  createdAt: Date
}

export interface AiWorkflowExecution {
  id: string
  customerId: string
  customerName: string
  originalMessage: string
  startTime: string
  endTime: string
  totalDuration: number
  finalStatus: "resolved" | "escalated" | "failed"
  steps: AiWorkflowStep[]
  finalResponse?: string
  csAgentId?: string
  feedbackScore?: number

  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface AiWorkflowExecutionCreateInput {
  customerId: string
  customerName: string
  originalMessage: string
  startTime: Date
  endTime?: Date
  totalDuration: number
  finalStatus: "resolved" | "escalated" | "failed"
  steps: Omit<AiWorkflowStep, "createdAt">[]
  finalResponse?: string
  csAgentId?: string
  feedbackScore?: number

  createdBy?: string
  organizationId?: string
}

export interface AiWorkflowExecutionUpdateInput {
  customerName?: string
  originalMessage?: string
  startTime?: string
  endTime?: string
  totalDuration?: number
  finalStatus?: "resolved" | "escalated" | "failed"
  steps?: Omit<AiWorkflowStep, "createdAt">[]
  finalResponse?: string
  csAgentId?: string
  feedbackScore?: number

  updatedBy?: string
  organizationId?: string
}

export interface AiWorkflowExecutionQueryParams {
  search?: string
  filters?: { field: keyof AiWorkflowExecution | string; value: any }[]
  sort?: {
    field: keyof AiWorkflowExecution | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

import { SessionContext } from "../auth/types"

export interface AiWorkflowExecutionBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<AiWorkflowExecution | null>
  getAll(
    params: AiWorkflowExecutionQueryParams,
    context: SessionContext,
  ): Promise<{
    items: AiWorkflowExecution[]
    total: number
  }>
  create(
    data: AiWorkflowExecutionCreateInput,
    context: SessionContext,
  ): Promise<AiWorkflowExecution>
  update(
    id: string,
    data: AiWorkflowExecutionUpdateInput,
    context: SessionContext,
  ): Promise<AiWorkflowExecution | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
}
