import {
  AiWorkflowExecution,
  AiWorkflowExecutionCreateInput,
  AiWorkflowExecutionUpdateInput,
  AiWorkflowExecutionQueryParams,
  AiWorkflowExecutionBusinessLogicInterface,
} from "./interface"
import { SessionContext } from "../auth/types"
import { createError } from "@/lib/utils/common"
import { AiWorkflowExecutionDBRepository } from "./DBRepository"

export class AiWorkflowExecutionBusinessLogic
  implements AiWorkflowExecutionBusinessLogicInterface {
  constructor(private readonly db: AiWorkflowExecutionDBRepository) { }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("AiWorkflowExecution ID is required", "INVALID_ID")
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<AiWorkflowExecution | null> {
    this.validateId(id)
    const execution = await this.db.getById(id, includeDeleted)

    // Filter by context - ensure user can only access their own organization's executions
    if (execution && context.organization && execution.organizationId !== context.organization.id) {
      return null
    }

    // Also check if user created this execution (for additional security)
    if (execution && context.user.id && execution.createdBy !== context.user.id && !context.organization) {
      return null
    }

    return execution
  }

  async getAll(
    params: AiWorkflowExecutionQueryParams,
    context: SessionContext,
  ): Promise<{ items: AiWorkflowExecution[]; total: number }> {
    // Use buildContextFilters for consistent filtering
    const contextFilters = this.buildContextFilters(context)
    const queryParams = {
      ...params,
      filters: [
        ...(params.filters || []),
        ...contextFilters,
      ],
    }

    return this.db.getAll(queryParams)
  }

  async create(
    data: AiWorkflowExecutionCreateInput,
    context: SessionContext,
  ): Promise<AiWorkflowExecution> {
    if (!data.customerId?.trim())
      throw createError("Customer ID is required", "INVALID_CUSTOMER_ID")
    // if (!data.customerName?.trim()) throw createError("Customer name is required", "INVALID_CUSTOMER_NAME")

    const executionData = {
      ...data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return this.db.create(executionData)
  }

  async update(
    id: string,
    data: AiWorkflowExecutionUpdateInput,
    context: SessionContext,
  ): Promise<AiWorkflowExecution | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if execution exists and user has permission to update it
    const existing = await this.getById(id, context)
    if (!existing) {
      return null
    }

    const updateData = {
      ...data,
      updatedBy: context.user.id,
    }

    return this.db.update(id, updateData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if execution exists and user has permission to delete it
    const existing = await this.getById(id, context)
    if (!existing) {
      return false
    }

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if execution exists and user has permission to restore it
    const execution = await this.getById(id, context, true) // includeDeleted = true
    if (!execution || !execution.deletedAt) {
      return false
    }

    return this.db.restore(id)
  }
}
