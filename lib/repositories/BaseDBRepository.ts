export type BaseQueryParams<T> = {
  search?: string
  limit?: number
  offset?: number
  sort?: { field: keyof T | string; direction: "ASC" | "DESC" }[]
  filters?: {
    field: keyof T | string
    value: T[keyof T] | any
  }[]
  includeDeleted?: boolean
}

// === Individual Interfaces ===

export interface IDbGetById<T> {
  getById(id: string, includeDeleted?: boolean): Promise<T | null>
}

export interface IDbGetAll<T, QueryParams extends BaseQueryParams<T>> {
  getAll(params: QueryParams): Promise<{ items: T[]; total: number }>
  getCount(params: QueryParams): Promise<{ total: number }>
}

export interface IDbCreate<T, CreateInput> {
  create(data: CreateInput): Promise<T>
}

export interface IDbUpdate<T, UpdateInput> {
  update(id: string, data: UpdateInput): Promise<T | null>
}

export interface IDbDelete {
  delete(id: string, hardDelete?: boolean): Promise<boolean>
  restore(id: string): Promise<boolean>
}

export interface IDbClear {
  clear(): Promise<void>
}

// === Composed Base Interface ===

export interface IDbBulkCreate<T, CreateInput> {
  bulkCreate(data: CreateInput[]): Promise<T[]>
}

export interface IDbBulkUpdate<UpdateInput> {
  bulkUpdate(updates: { id: string; data: UpdateInput }[]): Promise<number>
}

export interface IDbBulkDelete {
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>
}


export interface BaseDbRepository<
  T,
  CreateInput,
  UpdateInput,
  QueryParams extends BaseQueryParams<T>,
> extends IDbGetById<T>,
  IDbGetAll<T, QueryParams>,
  IDbCreate<T, CreateInput>,
  IDbUpdate<T, UpdateInput>,
  IDbDelete,
  IDbClear { }


export interface FullBaseDbRepository<
  T,
  CreateInput,
  UpdateInput,
  QueryParams extends BaseQueryParams<T>,
> extends BaseDbRepository<T, CreateInput, UpdateInput, QueryParams>,
  IDbBulkCreate<T, CreateInput>,
  IDbBulkUpdate<UpdateInput>,
  IDbBulkDelete { }
