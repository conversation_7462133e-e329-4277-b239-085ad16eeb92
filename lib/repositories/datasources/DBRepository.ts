import {
  IDb<PERSON><PERSON>,
  IDbD<PERSON><PERSON>,
  IDbGetAll,
  IDbGetById,
  IDbUpdate
} from "../BaseDBRepository"
import {
  Datasource,
  DatasourceCreateInput,
  DatasourceQueryParams,
  DatasourceUpdateInput,
} from "./interface"

export interface DatasourceDBRepository
  extends IDbGetById<Datasource>,
  IDbGetAll<Datasource, DatasourceQueryParams>,
  IDbCreate<Datasource, DatasourceCreateInput>,
  IDbUpdate<Datasource, DatasourceUpdateInput>,
  IDbDelete {
  getCount(params: DatasourceQueryParams): Promise<{ total: number }>
}