import { MongoDriver } from "../MongoDriver"
import { DatasourceDBRepository } from "./DBRepository"
import {
  Datasource,
  DatasourceCreateInput,
  DatasourceUpdateInput,
  DatasourceQueryParams,
} from "./interface"
import { Collection, Document } from "mongodb"
import { v4 as uuidv4 } from "uuid"
import { buildMongoQuery } from "../queryBuilder"
import { datasourcesSearchConfig } from "@/app/api/v1/search-configs/entities/datasources"

export class MongoDatasourceRepository implements DatasourceDBRepository {
  private collection: Collection<Document>

  constructor(private driver: MongoDriver) {
    this.collection = driver.getCollection("datasources")
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<Datasource | null> {
    const filter: any = { id }
    if (!includeDeleted) {
      filter.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(filter)
    return doc ? this.mapToEntity(doc) : null
  }

  async getAll(
    params: DatasourceQueryParams,
  ): Promise<{ items: Datasource[]; total: number }> {
    const initialQuery = datasourcesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      params,
      datasourcesSearchConfig.searchableFields,
    )

    const [items, total] = await Promise.all([
      this.collection
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit)
        .toArray(),
      this.collection.countDocuments(query),
    ])

    return {
      items: items.map((doc) => this.mapToEntity(doc)),
      total,
    }
  }

  async getCount(params: DatasourceQueryParams): Promise<{ total: number }> {
    const initialQuery = datasourcesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      datasourcesSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: DatasourceCreateInput): Promise<Datasource> {
    const now = new Date()
    const doc = {
      ...data,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(doc)
    return this.mapToEntity(doc)
  }

  async update(
    id: string,
    data: DatasourceUpdateInput,
  ): Promise<Datasource | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" },
    )

    return result ? this.mapToEntity(result) : null
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { id, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount > 0
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id, deletedAt: { $exists: true } },
      { $unset: { deletedAt: "" } },
    )
    return result.modifiedCount > 0
  }



  private mapToEntity(doc: any): Datasource {
    return {
      id: doc.id,
      name: doc.name,
      type: doc.type,
      url: doc.url,
      content: doc.content,
      accessKey: doc.accessKey,
      isActive: doc.isActive,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      deletedAt: doc.deletedAt,
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      organizationId: doc.organizationId,
    }
  }
}
