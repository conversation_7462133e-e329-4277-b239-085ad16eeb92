import {
  Invitation,
  InvitationCreateInput,
  InvitationUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type InvitationDbQueryParams = BaseQueryParams<Invitation>

export interface InvitationDBRepository
  extends BaseDbRepository<
    Invitation,
    InvitationCreateInput,
    InvitationUpdateInput,
    InvitationDbQueryParams
  > {}
