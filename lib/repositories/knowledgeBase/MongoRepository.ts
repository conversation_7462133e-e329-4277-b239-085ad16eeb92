import { WithId, Document } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import {
  KnowledgeBaseEntry,
  KnowledgeBaseCreateInput,
  KnowledgeBaseUpdateInput,
  KnowledgeBaseQueryParams,
} from "./interface"
import {
  KnowledgeBaseDBRepository,
  KnowledgeBaseDbQueryParams,
} from "./DBRepository"
import { buildMongoQuery } from "../queryBuilder"
import { generateId } from "@/lib/utils/common"

function mapMongoDocToKnowledgeBaseEntry(
  doc: any,
): KnowledgeBaseEntry | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest
  }
}

export class MongoKnowledgeBaseRepository implements KnowledgeBaseDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.KNOWLEDGE_BASE)
    this.ensureIndexes()
  }

  private async ensureIndexes() {
    await this.collection.createIndexes([
      { key: { id: 1 }, unique: true },
      { key: { organizationId: 1 } },
      { key: { isActive: 1 } },
      { key: { createdAt: -1 } },
    ])
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<KnowledgeBaseEntry | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToKnowledgeBaseEntry(doc)
  }

  async getAll(
    params: KnowledgeBaseDbQueryParams,
  ): Promise<{ items: KnowledgeBaseEntry[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        search: params?.search,
        limit: params?.limit,
        offset: params?.page,
        sort: params?.sort,
        filters: params?.filters,
        includeDeleted: params?.includeDeleted,
      },
      [],
    )

    const cursor = this.collection.find(query).sort(sort).skip(offset).limit(limit)
    const docs = await cursor.toArray()
    const items = docs.map(mapMongoDocToKnowledgeBaseEntry).filter(Boolean) as KnowledgeBaseEntry[]
    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(params: KnowledgeBaseQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.page,
      },
      [],
    )
    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async searchByContent(query: string, limit = 10): Promise<KnowledgeBaseEntry[]> {
    const searchQuery = {
      $and: [
        { deletedAt: { $exists: false } },
        { isActive: true },
        {
          $or: [
            { title: { $regex: query, $options: "i" } },
            { content: { $regex: query, $options: "i" } },
            { keywords: { $in: [new RegExp(query, "i")] } },
          ],
        },
      ],
    }

    const docs = await this.collection
      .find(searchQuery)
      .limit(limit)
      .sort({ createdAt: -1 })
      .toArray()

    return docs.map(mapMongoDocToKnowledgeBaseEntry).filter(Boolean) as KnowledgeBaseEntry[]
  }

  async create(data: KnowledgeBaseCreateInput): Promise<KnowledgeBaseEntry> {
    const now = new Date()
    const doc = {
      id: generateId(),
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return mapMongoDocToKnowledgeBaseEntry(doc)!
  }

  async update(id: string, data: KnowledgeBaseUpdateInput): Promise<KnowledgeBaseEntry | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }
    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" },
    )

    if (!result) return null

    return mapMongoDocToKnowledgeBaseEntry(result)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { id, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount > 0
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id, deletedAt: { $exists: true } },
      { $unset: { deletedAt: "" } },
    )
    return result.modifiedCount > 0
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
