import { SessionContext } from "../auth/types"

export interface KnowledgeBaseEntry {
  id: string
  content: string
  keywords: string[]
  category?: string
  tags?: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  organizationId?: string
}

export interface KnowledgeBaseDocument {
  id: string
  name: string
  originalName: string
  size: number
  mimeType: string
  filePath: string
  extractedContent?: string
  keywords: string[]
  isProcessed: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface KnowledgeBaseStats {
  score: number // 0-100
  totalEntries: number
  totalDocuments: number
  keywords: string[]
  inferredRules: string[]
  suggestedTemplates: string[]
  lastUpdated: Date
}

export interface KnowledgeBaseCreateInput {
  content: string
}

export interface KnowledgeBaseUpdateInput {
  content?: string
}

export interface KnowledgeBaseDocumentCreateInput {
  name: string
  originalName: string
  size: number
  mimeType: string
  filePath: string
  extractedContent?: string
  keywords?: string[]
  isProcessed?: boolean
  isActive?: boolean
  createdBy: string
  organizationId?: string
}

export interface KnowledgeBaseDocumentUpdateInput {
  name?: string
  extractedContent?: string
  keywords?: string[]
  isProcessed?: boolean
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
}

export interface KnowledgeBaseQueryParams {
  search?: string
  filters?: { field: keyof KnowledgeBaseEntry | string; value: any }[]
  sort?: {
    field: keyof KnowledgeBaseEntry | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface KnowledgeBaseDocumentQueryParams {
  search?: string
  filters?: { field: keyof KnowledgeBaseDocument | string; value: any }[]
  sort?: {
    field: keyof KnowledgeBaseDocument | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface KnowledgeBaseBusinessLogicInterface {
  // Entry management
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<KnowledgeBaseEntry | null>
  getAll(
    params: KnowledgeBaseQueryParams,
    context: SessionContext,
  ): Promise<{
    items: KnowledgeBaseEntry[]
    total: number
  }>
  create(
    data: KnowledgeBaseCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry>
  update(
    id: string,
    data: KnowledgeBaseUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>

  // Document management
  getDocumentById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<KnowledgeBaseDocument | null>
  getAllDocuments(
    params: KnowledgeBaseDocumentQueryParams,
    context: SessionContext,
  ): Promise<{
    items: KnowledgeBaseDocument[]
    total: number
  }>
  createDocument(
    data: KnowledgeBaseDocumentCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseDocument>
  updateDocument(
    id: string,
    data: KnowledgeBaseDocumentUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseDocument | null>
  deleteDocument(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>

  // Knowledge base operations
  updateKnowledgeBase(
    content: string,
    context: SessionContext,
  ): Promise<boolean>
  getStats(context: SessionContext): Promise<KnowledgeBaseStats>
  generateInferredRules(context: SessionContext): Promise<string[]>
  generateSuggestedTemplates(context: SessionContext): Promise<string[]>
  extractKeywords(content: string, context: SessionContext): Promise<string[]>

  // Search and AI operations
  searchSimilar(
    query: string,
    context: SessionContext,
    limit?: number,
  ): Promise<KnowledgeBaseEntry[]>
  processDocument(documentId: string, context: SessionContext): Promise<boolean>
}
