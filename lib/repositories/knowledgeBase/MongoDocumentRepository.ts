import { ObjectId, WithId, Document } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import {
  KnowledgeBaseDocument,
  KnowledgeBaseDocumentCreateInput,
  KnowledgeBaseDocumentUpdateInput,
  KnowledgeBaseDocumentQueryParams,
} from "./interface"
import {
  KnowledgeBaseDocumentDBRepository,
  KnowledgeBaseDocumentDbQueryParams,
} from "./DBRepository"
import { buildMongoQuery } from "../queryBuilder"

// Mapping function
function mapMongoDocToKnowledgeBaseDocument(
  doc: WithId<Document> | null,
): KnowledgeBaseDocument | null {
  if (!doc) return null
  return {
    id: doc._id.toString(),
    name: doc.name,
    originalName: doc.originalName,
    size: doc.size,
    mimeType: doc.mimeType,
    filePath: doc.filePath,
    extractedContent: doc.extractedContent,
    keywords: doc.keywords || [],
    isProcessed: doc.isProcessed,
    isActive: doc.isActive,
    createdAt: doc.createdAt,
    updatedAt: doc.updatedAt,
    deletedAt: doc.deletedAt,
    createdBy: doc.createdBy,
    updatedBy: doc.updatedBy,
    organizationId: doc.organizationId,
  }
}

export class MongoKnowledgeBaseDocumentRepository
  implements KnowledgeBaseDocumentDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(
      MONGO_COLLECTIONS.KNOWLEDGE_BASE_DOCUMENTS,
    )
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<KnowledgeBaseDocument | null> {
    const query: any = { _id: new ObjectId(id) }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToKnowledgeBaseDocument(doc)
  }

  async getAll(
    params: KnowledgeBaseDocumentDbQueryParams,
  ): Promise<{ items: KnowledgeBaseDocument[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        search: params?.search,
        limit: params?.limit,
        offset: params?.page,
        sort: params?.sort,
        filters: params?.filters,
        includeDeleted: params?.includeDeleted,
      },
      [],
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToKnowledgeBaseDocument)
      .filter(Boolean) as KnowledgeBaseDocument[]
    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(
    params: KnowledgeBaseDocumentQueryParams,
  ): Promise<{ total: number }> {
    const { query } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.page,
      },
      [],
    )
    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async getByFilePath(filePath: string): Promise<KnowledgeBaseDocument | null> {
    const doc = await this.collection.findOne({
      filePath,
      deletedAt: { $exists: false },
    })
    return mapMongoDocToKnowledgeBaseDocument(doc)
  }

  async getUnprocessedDocuments(): Promise<KnowledgeBaseDocument[]> {
    const docs = await this.collection
      .find({
        isProcessed: false,
        isActive: true,
        deletedAt: { $exists: false },
      })
      .toArray()
    return docs
      .map(mapMongoDocToKnowledgeBaseDocument)
      .filter(Boolean) as KnowledgeBaseDocument[]
  }

  async create(
    data: KnowledgeBaseDocumentCreateInput,
  ): Promise<KnowledgeBaseDocument> {
    const now = new Date()
    const doc = {
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    const result = await this.collection.insertOne(doc)
    return mapMongoDocToKnowledgeBaseDocument({
      _id: result.insertedId,
      ...doc,
    } as WithId<Document>)!
  }

  async update(
    id: string,
    data: KnowledgeBaseDocumentUpdateInput,
  ): Promise<KnowledgeBaseDocument | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }
    const result = await this.collection.findOneAndUpdate(
      { _id: new ObjectId(id), deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" },
    )
    return mapMongoDocToKnowledgeBaseDocument(result)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ _id: new ObjectId(id) })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { _id: new ObjectId(id), deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount > 0
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { _id: new ObjectId(id), deletedAt: { $exists: true } },
      { $unset: { deletedAt: "" } },
    )
    return result.modifiedCount > 0
  }

  async bulkCreate(
    data: KnowledgeBaseDocumentCreateInput[],
  ): Promise<KnowledgeBaseDocument[]> {
    const now = new Date()
    const docs = data.map((item) => ({
      ...item,
      createdAt: now,
      updatedAt: now,
    }))
    const result = await this.collection.insertMany(docs)
    return Object.values(result.insertedIds).map(
      (id, index) =>
        mapMongoDocToKnowledgeBaseDocument({
          _id: id,
          ...docs[index],
        } as WithId<Document>)!,
    )
  }

  async bulkUpdate(
    updates: { id: string; data: KnowledgeBaseDocumentUpdateInput }[],
  ): Promise<number> {
    const operations = updates.map(({ id, data }) => ({
      updateOne: {
        filter: { _id: new ObjectId(id), deletedAt: { $exists: false } },
        update: { $set: { ...data, updatedAt: new Date() } },
      },
    }))
    const result = await this.collection.bulkWrite(operations)
    return result.modifiedCount
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({
        _id: { $in: ids.map((id) => new ObjectId(id)) },
      })
      return result.deletedCount
    } else {
      const result = await this.collection.updateMany(
        {
          _id: { $in: ids.map((id) => new ObjectId(id)) },
          deletedAt: { $exists: false },
        },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
