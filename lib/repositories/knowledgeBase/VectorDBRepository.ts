import { BaseVectorDBRepository } from "../BaseVectorDBRepository"
import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"

export interface KnowledgeBaseVectorDB {
  id: string
  content: string
}

export interface KnowledgeBaseVectorDBRepository
  extends BaseVectorDBRepository<KnowledgeBaseVectorDB> {
  searchSimilar(query: string, topK?: number): Promise<KnowledgeBaseVectorDB[]>
  searchByKeywords(
    keywords: string[],
    topK?: number,
  ): Promise<KnowledgeBaseVectorDB[]>
}

export class PineconeKnowledgeBaseVectorDBRepository
  implements KnowledgeBaseVectorDBRepository
{
  private namespace: Index<RecordMetadata>

  constructor(pineconeApiKey: string, organization: string) {
    const pinecone = new Pinecone({ apiKey: pineconeApiKey })
    this.namespace = pinecone.index("knowledge-base").namespace(organization)
  }

  async create(item: KnowledgeBaseVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: item.id,
        text: item.content,
      },
    ])
  }

  async update(id: string, item: KnowledgeBaseVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: id,
        text: item.content,
      },
    ])
  }

  async delete(id: string): Promise<void> {
    await this.namespace.deleteMany([id])
  }

  async search(
    text: string,
    topK: number = 5,
  ): Promise<KnowledgeBaseVectorDB[]> {
    const searchResult = await this.namespace.searchRecords({
      query: {
        topK,
        inputs: { text: text }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        title: metadata?.title || "",
        content: metadata?.content || "",
        keywords: metadata?.keywords ? metadata.keywords.split(",") : [],
        category: metadata?.category || undefined,
      }
    })
  }

  async searchSimilar(
    query: string,
    topK: number = 5,
  ): Promise<KnowledgeBaseVectorDB[]> {
    return this.search(query, topK)
  }

  async searchByKeywords(
    keywords: string[],
    topK: number = 5,
  ): Promise<KnowledgeBaseVectorDB[]> {
    const keywordQuery = keywords.join(" ")
    return this.search(keywordQuery, topK)
  }
}
