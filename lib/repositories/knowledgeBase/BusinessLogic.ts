import {
  KnowledgeBaseEntry,
  KnowledgeBaseDocument,
  KnowledgeBaseStats,
  KnowledgeBaseCreateInput,
  KnowledgeBaseUpdateInput,
  KnowledgeBaseDocumentCreateInput,
  KnowledgeBaseDocumentUpdateInput,
  KnowledgeBaseQueryParams,
  KnowledgeBaseDocumentQueryParams,
  KnowledgeBaseBusinessLogicInterface,
} from "./interface"
import { SessionContext } from "../auth/types"
import { createError } from "@/lib/utils/common"
import {
  KnowledgeBaseDBRepository,
  KnowledgeBaseDocumentDBRepository,
} from "./DBRepository"
import { KnowledgeBaseVectorDBRepository } from "./VectorDBRepository"

export class KnowledgeBaseBusinessLogic
  implements KnowledgeBaseBusinessLogicInterface {
  private db: KnowledgeBaseDBRepository
  private documentDb: KnowledgeBaseDocumentDBRepository
  private vectorDb: KnowledgeBaseVectorDBRepository

  constructor(
    db: KnowledgeBaseDBRepository,
    documentDb: KnowledgeBaseDocumentDBRepository,
    vectorDb: KnowledgeBaseVectorDBRepository,
  ) {
    this.db = db
    this.documentDb = documentDb
    this.vectorDb = vectorDb
  }

  // Validation helpers
  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim() === "") {
      throw createError(
        "Knowledge base entry ID is required",
        "VALIDATION_FAILED",
      )
    }
  }

  private validateCreateInput(data: KnowledgeBaseCreateInput): void {
    if (!data.content || data.content.trim() === "") {
      throw createError("Content is required", "VALIDATION_FAILED")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  private validateDocumentCreateInput(
    data: KnowledgeBaseDocumentCreateInput,
  ): void {
    if (!data.name || data.name.trim() === "") {
      throw createError("Document name is required", "VALIDATION_FAILED")
    }
    if (!data.filePath || data.filePath.trim() === "") {
      throw createError("File path is required", "VALIDATION_FAILED")
    }
  }

  // Entry management
  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<KnowledgeBaseEntry | null> {
    this.validateId(id)
    const entry = await this.db.getById(id, includeDeleted)

    // Filter by context - ensure user can only access their own organization's entries
    if (entry && context.organization && entry.organizationId !== context.organization.id) {
      return null
    }

    // Also check if user created this entry (for additional security)
    if (entry && context.user.id && entry.createdBy !== context.user.id && !context.organization) {
      return null
    }

    return entry
  }

  async getAll(
    params: KnowledgeBaseQueryParams,
    context: SessionContext,
  ): Promise<{ items: KnowledgeBaseEntry[]; total: number }> {
    // Use buildContextFilters for consistent filtering
    const contextFilters = this.buildContextFilters(context)
    const queryParams = {
      ...params,
      filters: [
        ...(params.filters || []),
        ...contextFilters,
      ],
    }

    return this.db.getAll(queryParams)
  }

  async create(
    data: KnowledgeBaseCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry> {
    this.validateCreateInput(data)

    const entryData: KnowledgeBaseCreateInput = {
      ...data,
    }

    const entry = await this.db.create({
      ...entryData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    })

    // Add to vector database for similarity search
    try {
      await this.vectorDb.create({
        id: entry.id,
        content: entry.content,
      })
    } catch (error) {
      console.error("Failed to add to vector database:", error)
      // Don't fail the entire operation if vector DB fails
    }

    return entry
  }

  async update(
    id: string,
    data: KnowledgeBaseUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry | null> {
    this.validateId(id)

    // Check if entry exists and user has permission to update it
    const existingEntry = await this.getById(id, context)
    if (!existingEntry) {
      return null
    }

    const updateData: KnowledgeBaseUpdateInput = {
      ...data,
    }

    const entry = await this.db.update(id, updateData)

    // Update vector database
    if (entry) {
      try {
        await this.vectorDb.update(id, {
          id: entry.id,
          content: entry.content,
        })
      } catch (error) {
        console.error("Failed to update vector database:", error)
      }
    }

    return entry
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if entry exists and user has permission to delete it
    const existingEntry = await this.getById(id, context)
    if (!existingEntry) {
      return false
    }

    const success = await this.db.delete(id, hardDelete)

    // Remove from vector database if hard delete
    if (success && hardDelete) {
      try {
        await this.vectorDb.delete(id)
      } catch (error) {
        console.error("Failed to delete from vector database:", error)
      }
    }

    return success
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if entry exists and user has permission to restore it
    const existingEntry = await this.getById(id, context, true) // includeDeleted = true
    if (!existingEntry) {
      return false
    }

    return this.db.restore(id)
  }


  // Document management
  async getDocumentById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<KnowledgeBaseDocument | null> {
    this.validateId(id)
    const document = await this.documentDb.getById(id, includeDeleted)

    // Filter by context - ensure user can only access their own organization's documents
    if (document && context.organization && document.organizationId !== context.organization.id) {
      return null
    }

    // Also check if user created this document (for additional security)
    if (document && context.user.id && document.createdBy !== context.user.id && !context.organization) {
      return null
    }

    return document
  }

  async getAllDocuments(
    params: KnowledgeBaseDocumentQueryParams,
    context: SessionContext,
  ): Promise<{ items: KnowledgeBaseDocument[]; total: number }> {
    // Use buildContextFilters for consistent filtering
    const contextFilters = this.buildContextFilters(context)
    const queryParams = {
      ...params,
      filters: [
        ...(params.filters || []),
        ...contextFilters,
      ],
    }

    return this.documentDb.getAll(queryParams)
  }

  async createDocument(
    data: KnowledgeBaseDocumentCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseDocument> {
    this.validateDocumentCreateInput(data)

    const documentData: KnowledgeBaseDocumentCreateInput = {
      ...data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return this.documentDb.create(documentData)
  }

  async updateDocument(
    id: string,
    data: KnowledgeBaseDocumentUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseDocument | null> {
    this.validateId(id)

    // Check if document exists and user has permission to update it
    const existingDocument = await this.getDocumentById(id, context)
    if (!existingDocument) {
      return null
    }

    const updateData: KnowledgeBaseDocumentUpdateInput = {
      ...data,
      updatedBy: context.user.id,
    }

    return this.documentDb.update(id, updateData)
  }

  async deleteDocument(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if document exists and user has permission to delete it
    const existingDocument = await this.getDocumentById(id, context)
    if (!existingDocument) {
      return false
    }

    return this.documentDb.delete(id, hardDelete)
  }

  // Knowledge base operations
  async updateKnowledgeBase(
    content: string,
    context: SessionContext,
  ): Promise<boolean> {
    // This would typically trigger AI processing to update rules and templates
    // For now, we'll just return true
    return true
  }

  async getStats(context: SessionContext): Promise<KnowledgeBaseStats> {
    const [entriesResult, documentsResult] = await Promise.all([
      this.getAll({ page: 1, limit: 1 }, context),
      this.getAllDocuments({ page: 1, limit: 1 }, context),
    ])

    // Calculate a simple score based on content availability
    const totalEntries = entriesResult.total
    const totalDocuments = documentsResult.total
    const score = Math.min(100, (totalEntries + totalDocuments) * 5)

    return {
      score,
      totalEntries,
      totalDocuments,
      keywords: [], // Would be extracted from all entries
      inferredRules: [], // Would be generated by AI
      suggestedTemplates: [], // Would be generated by AI
      lastUpdated: new Date(),
    }
  }

  async generateInferredRules(context: SessionContext): Promise<string[]> {
    // This would use AI to analyze knowledge base and generate rules
    // For now, return empty array
    return []
  }

  async generateSuggestedTemplates(context: SessionContext): Promise<string[]> {
    // This would use AI to analyze knowledge base and generate templates
    // For now, return empty array
    return []
  }

  async extractKeywords(
    content: string,
    context: SessionContext,
  ): Promise<string[]> {
    // Simple keyword extraction - in production, use AI/NLP
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter((word) => word.length > 3)
      .filter((word, index, arr) => arr.indexOf(word) === index)
      .slice(0, 10)

    return words
  }

  async searchSimilar(
    query: string,
    context: SessionContext,
    limit = 5,
  ): Promise<KnowledgeBaseEntry[]> {
    try {
      const vectorResults = await this.vectorDb.searchSimilar(query, limit)

      // Get full entries from database with proper context filtering
      const entries = await Promise.all(
        vectorResults.map((result) => this.getById(result.id, context)),
      )

      return entries.filter(Boolean) as KnowledgeBaseEntry[]
    } catch (error) {
      console.error("Vector search failed, falling back to text search:", error)
      // Fallback to text search with context filtering
      const result = await this.getAll({
        search: query,
        limit: limit,
        page: 1,
      }, context)
      return result.items
    }
  }

  async processDocument(
    documentId: string,
    context: SessionContext,
  ): Promise<boolean> {
    const document = await this.getDocumentById(documentId, context)
    if (!document) {
      throw createError("Document not found", "NOT_FOUND")
    }

    // Mark as processed
    await this.updateDocument(documentId, { isProcessed: true }, context)

    // If document has extracted content, create knowledge base entries
    if (document.extractedContent) {
      await this.create(
        {
          content: document.extractedContent,
        },
        context,
      )
    }

    return true
  }
}
