import {
  KnowledgeBaseEntry,
  KnowledgeBaseDocument,
  KnowledgeBaseCreateInput,
  KnowledgeBaseUpdateInput,
  KnowledgeBaseDocumentCreateInput,
  KnowledgeBaseDocumentUpdateInput,
  KnowledgeBaseQueryParams,
  KnowledgeBaseDocumentQueryParams,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type KnowledgeBaseDbQueryParams = KnowledgeBaseQueryParams
export type KnowledgeBaseDocumentDbQueryParams =
  KnowledgeBaseDocumentQueryParams

export interface KnowledgeBaseDBRepository
  extends BaseDbRepository<
    KnowledgeBaseEntry,
    KnowledgeBaseCreateInput & { createdBy: string; organizationId?: string },
    KnowledgeBaseUpdateInput,
    KnowledgeBaseDbQueryParams
  > {
  getCount(params: KnowledgeBaseQueryParams): Promise<{ total: number }>
  searchByContent(query: string, limit?: number): Promise<KnowledgeBaseEntry[]>
}

export interface KnowledgeBaseDocumentDBRepository
  extends BaseDbRepository<
    KnowledgeBaseDocument,
    KnowledgeBaseDocumentCreateInput,
    KnowledgeBaseDocumentUpdateInput,
    KnowledgeBaseDocumentDbQueryParams
  > {
  getCount(params: KnowledgeBaseDocumentQueryParams): Promise<{ total: number }>
  getByFilePath(filePath: string): Promise<KnowledgeBaseDocument | null>
  getUnprocessedDocuments(): Promise<KnowledgeBaseDocument[]>
}
