import { SessionContext } from "../auth/types"

export interface CustomerProfile {
  id: string
  customerId: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  company?: string
  jobTitle?: string
  address?: {
    street?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  preferences?: {
    language?: string
    timezone?: string
    communicationChannel?: string
    notifications?: boolean
  }
  tags?: string[]
  notes?: string
  lastContactDate?: Date
  totalOrders?: number
  totalSpent?: number
  loyaltyLevel?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
  recentOrders?: [
    {
      id: "#ORD-2024-001234"
      date: "15 Jan"
      amount: "Rp 1.350.000"
      status: "Sedang diproses"
    },
    {
      id: "#ORD-2024-001198"
      date: "10 Jan"
      amount: "Rp 2.100.000"
      status: "Terkirim"
    },
    {
      id: "#ORD-2024-001156"
      date: "28 Des"
      amount: "Rp 3.500.000"
      status: "Terkirim"
    },
  ]
}

export type CustomerProfileQueryParams = {
  search?: string
  limit?: number
  offset?: number
  sort?: { field: keyof CustomerProfile | string; direction: "ASC" | "DESC" }[]
  filters?: {
    field: keyof CustomerProfile | string
    value: CustomerProfile[keyof CustomerProfile] | any
  }[]
  includeDeleted?: boolean
}

export interface CustomerProfileBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<CustomerProfile | null>
  getByCustomerId(
    customerId: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<CustomerProfile | null>
  getAll(
    params: CustomerProfileQueryParams,
    context: SessionContext,
  ): Promise<{
    items: CustomerProfile[]
    total: number
  }>
}
