import {
  CustomerProfile,
  CustomerProfileQueryParams,
  CustomerProfileBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { CustomerProfileDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"

export class CustomerProfileBusinessLogic
  implements CustomerProfileBusinessLogicInterface
{
  constructor(private db: CustomerProfileDBRepository) {}

  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim().length === 0) {
      throw createError("Invalid ID provided", "INVALID_ID")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<CustomerProfile | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getByCustomerId(
    customerId: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<CustomerProfile | null> {
    this.validateId(customerId)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "customerId", value: customerId }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: CustomerProfileQueryParams,
    context: SessionContext,
  ): Promise<{ items: CustomerProfile[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }
}
