import { BaseQueryParams, IDbGetById, IDbGetAll } from "../BaseDBRepository"
import { CustomerProfile, CustomerProfileQueryParams } from "./interface"

export interface CustomerProfileDBRepository
  extends IDbGetById<CustomerProfile>,
    IDbGetAll<CustomerProfile, CustomerProfileQueryParams> {
  getCount(params: CustomerProfileQueryParams): Promise<{ total: number }>
  getByCustomerId(
    customerId: string,
    includeDeleted?: boolean,
  ): Promise<CustomerProfile | null>
}
