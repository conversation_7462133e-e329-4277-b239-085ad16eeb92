import { MongoDriver } from "../MongoDriver"
import { CustomerProfileDBRepository } from "./DBRepository"
import { CustomerProfile, CustomerProfileQueryParams } from "./interface"
import { Collection, Document } from "mongodb"
import { buildMongoQuery } from "../queryBuilder"
import { customerProfilesSearchConfig } from "@/app/api/v1/search-configs/entities/customerProfiles"

export class MongoCustomerProfileRepository
  implements CustomerProfileDBRepository {
  private collection: Collection<Document>

  constructor(private driver: MongoDriver) {
    this.collection = driver.getCollection("customerProfiles")
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<CustomerProfile | null> {
    const filter: any = { id }
    if (!includeDeleted) {
      filter.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(filter)
    return doc ? this.mapToEntity(doc) : null
  }

  async getByCustomerId(
    customerId: string,
    includeDeleted = false,
  ): Promise<CustomerProfile | null> {
    const filter: any = { customerId }
    if (!includeDeleted) {
      filter.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(filter)
    return doc ? this.mapToEntity(doc) : null
  }

  async getAll(
    params: CustomerProfileQueryParams,
  ): Promise<{ items: CustomerProfile[]; total: number }> {
    const initialQuery = customerProfilesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: params?.page,
      },
      customerProfilesSearchConfig.searchableFields,
    )

    const [items, total] = await Promise.all([
      this.collection
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit)
        .toArray(),
      this.collection.countDocuments(query),
    ])

    return {
      items: items.map((doc) => this.mapToEntity(doc)),
      total,
    }
  }

  async getCount(
    params: CustomerProfileQueryParams,
  ): Promise<{ total: number }> {
    const initialQuery = customerProfilesSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      customerProfilesSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }



  private mapToEntity(doc: any): CustomerProfile {
    return {
      id: doc.id,
      customerId: doc.customerId,
      firstName: doc.firstName,
      lastName: doc.lastName,
      email: doc.email,
      phone: doc.phone,
      company: doc.company,
      jobTitle: doc.jobTitle,
      address: doc.address,
      preferences: doc.preferences,
      tags: doc.tags,
      notes: doc.notes,
      lastContactDate: doc.lastContactDate,
      totalOrders: doc.totalOrders,
      totalSpent: doc.totalSpent,
      loyaltyLevel: doc.loyaltyLevel,
      isActive: doc.isActive,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      deletedAt: doc.deletedAt,
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      organizationId: doc.organizationId,
    }
  }
}
