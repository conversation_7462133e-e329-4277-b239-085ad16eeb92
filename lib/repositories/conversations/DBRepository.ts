import {
  Conversation,
  ConversationCreateInput,
  ConversationUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type ConversationDbQueryParams = BaseQueryParams<Conversation>

export interface ConversationDBRepository
  extends BaseDbRepository<
    Conversation,
    ConversationCreateInput,
    ConversationUpdateInput,
    ConversationDbQueryParams
  > {}
