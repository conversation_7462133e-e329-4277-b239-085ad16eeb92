import { SessionContext } from "../auth/types"

export interface ConversationLastMessageData {
  messageTimestamp: Date | string
}

export interface ConversationLastMessage {
  body: string
  fromMe: boolean
  _data?: ConversationLastMessageData
  ack: number
}

export interface ConversationCloseMetadata {
  reason?: string
  closedAt: Date
  closedBy: string
  duration?: number
  notes?: string
}

export interface Conversation {
  id: string
  name: string
  description?: string
  participants: string[]
  participantsFull?: { id: string; name: string }[]
  lastMessage?: ConversationLastMessage
  lastMessageAt?: Date
  tags?: string[]
  customerProfileId?: string
  isActive: boolean
  status?: "CLOSED" | "OPEN"
  close_metadata?: ConversationCloseMetadata
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
  status_presence?: any
  timestamp?: any
}

export interface ConversationCreateInput {
  name: string
  description?: string
  participants: string[]
  tags?: string[]
  customerProfileId?: string
  isActive?: boolean
}

export type ConversationUpdateInput = Partial<Omit<Conversation, "id" | "createdAt" | "createdBy">>

export interface ConversationQueryParams {
  search?: string
  filters?: { field: keyof Conversation | string; value: any }[]
  sort?: { field: keyof Conversation | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface ConversationBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Conversation | null>
  getAll(
    params: ConversationQueryParams,
    context: SessionContext,
  ): Promise<{
    items: Conversation[]
    total: number
  }>
  create(
    data: ConversationCreateInput,
    context: SessionContext,
  ): Promise<Conversation>
  update(
    id: string,
    data: ConversationUpdateInput,
    context: SessionContext,
  ): Promise<Conversation | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(
    data: ConversationCreateInput[],
    context: SessionContext,
  ): Promise<Conversation[]>
  bulkUpdate(
    updates: { id: string; data: ConversationUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
}
