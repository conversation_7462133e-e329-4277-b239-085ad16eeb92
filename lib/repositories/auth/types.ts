export interface SessionContext {
  user: {
    id: string
    name: string
    email: string
  }
  organization?: { id: string }
}

export type WithUserAndOrganizationContextData<T> = T & {
  createdBy: string
  organizationId?: string
}

class PublicAccessSessionContext implements SessionContext {
  user = {
    id: "public",
    name: "Public",
    email: "<EMAIL>",
    createdAt: new Date(),
    updatedAt: new Date(),
  }
  organization = undefined
}

export const PUBLIC_SESSION_CONTEXT = new PublicAccessSessionContext()
export function isPublic(context: SessionContext) {
  return context.user.id === "public"
}
