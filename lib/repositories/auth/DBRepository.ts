import { User } from "@/lib/types/base"

/**
 * Pure CRUD interface for user data access
 * Contains only database operations without business logic
 */
export interface UserDBRepository {
  // Basic CRUD operations
  findUserByEmail(email: string): Promise<User | null>
  findUserById(id: string): Promise<User | null>
  findUserWithPasswordByEmail(
    email: string,
  ): Promise<(User & { password: string }) | null>
  createUser(
    userData: Omit<User, "id" | "createdAt" | "updatedAt"> & {
      password: string
    },
  ): Promise<User>
  updateUser(
    id: string,
    userData: Partial<User & { password?: string }>,
  ): Promise<User | null>
  deleteUser(id: string): Promise<boolean>

  // User existence checks
  userExistsByEmail(email: string): Promise<boolean>
  userExistsById(id: string): Promise<boolean>
}

/**
 * Pure CRUD interface for token data access
 * Contains only database operations for token management
 */
export interface TokenDBRepository {
  // Token CRUD operations
  createToken(tokenData: {
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  }): Promise<void>

  findTokenByValue(token: string): Promise<{
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  } | null>

  findTokenByRefreshToken(refreshToken: string): Promise<{
    id: string
    userId: string
    token: string
    refresh_token: string
    createdAt: Date
    expiresAt: Date
  } | null>

  updateToken(
    refreshToken: string,
    newTokenData: {
      token: string
      refresh_token: string
      updatedAt: Date
      expiresAt: Date
    },
  ): Promise<void>

  deleteToken(token: string): Promise<void>
  deleteTokenByRefreshToken(refreshToken: string): Promise<void>
  deleteTokensByUserId(userId: string): Promise<void>
}

/**
 * Pure CRUD interface for password reset data access
 */
export interface PasswordResetDBRepository {
  createPasswordResetRequest(data: {
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  }): Promise<void>

  findPasswordResetByToken(token: string): Promise<{
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  } | null>

  deletePasswordResetByToken(token: string): Promise<void>
  deletePasswordResetsByUserId(userId: string): Promise<void>
}

/**
 * Pure CRUD interface for email verification data access
 */
export interface EmailVerificationDBRepository {
  createEmailVerificationRequest(data: {
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  }): Promise<void>

  findEmailVerificationByToken(token: string): Promise<{
    id: string
    userId: string
    token: string
    expiresAt: Date
    createdAt: Date
  } | null>

  deleteEmailVerificationByToken(token: string): Promise<void>
  deleteEmailVerificationsByUserId(userId: string): Promise<void>
}

/**
 * Combined CRUD repository interface
 */
export interface AuthDBRepository
  extends UserDBRepository,
    TokenDBRepository,
    PasswordResetDBRepository,
    EmailVerificationDBRepository {}
