/**
 * Simple SearchConfig class for knowledge base repositories
 * Provides basic MongoDB query building functionality
 */

export interface SearchConfigOptions<T> {
  searchableFields: (keyof T | string)[]
  filterableFields: (keyof T | string)[]
  sortableFields: (keyof T | string)[]
}

export interface SearchQueryParams {
  sort?: { field: string; direction: "ASC" | "DESC" }[]
  filters?: { field: string; value: any }[]
  search?: string
  includeDeleted?: boolean
}

export interface MongoQueryResult {
  query: Record<string, any>
  sort: Record<string, 1 | -1>
}

export class SearchConfig<T = any> {
  public readonly searchableFields: string[]
  public readonly filterableFields: string[]
  public readonly sortableFields: string[]

  constructor(options: SearchConfigOptions<T>) {
    this.searchableFields = options.searchableFields.map((field) =>
      String(field),
    )
    this.filterableFields = options.filterableFields.map((field) =>
      String(field),
    )
    this.sortableFields = options.sortableFields.map((field) => String(field))
  }

  /**
   * Build MongoDB query from search parameters
   */
  buildMongoQuery(params: SearchQueryParams): MongoQueryResult {
    const query: Record<string, any> = {}
    const sort: Record<string, 1 | -1> = {}

    // Handle soft delete filter
    if (!params.includeDeleted) {
      query.deletedAt = { $exists: false }
    }

    // Handle filters
    if (params.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (this.filterableFields.includes(filter.field)) {
          if (Array.isArray(filter.value)) {
            query[filter.field] = { $in: filter.value }
          } else {
            query[filter.field] = filter.value
          }
        }
      }
    }

    // Handle sorting
    if (params.sort && params.sort.length > 0) {
      for (const sortParam of params.sort) {
        if (this.sortableFields.includes(sortParam.field)) {
          sort[sortParam.field] = sortParam.direction === "ASC" ? 1 : -1
        }
      }
    }

    return { query, sort }
  }
}
