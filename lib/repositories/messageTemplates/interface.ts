import { MessageTemplateVectorDB } from "./VectorDBRepository"
import { SessionContext } from "../auth/types"

export interface MessageTemplate {
  id: string
  title: string
  query: string
  template: string
  category?: string
  tags?: string[]
  variables?: string[] // Template variables like {name}, {company}, etc.
  isActive?: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface MessageTemplateCreateInput {
  title: string
  query: string
  template: string
  tags?: string[]
  variables?: string[]
  isActive?: boolean
  createdBy?: string
  organizationId?: string
}

export interface MessageTemplateUpdateInput {
  title?: string
  query?: string
  template?: string
  tags?: string[]
  variables?: string[]
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
}

export interface MessageTemplateQueryParams {
  search?: string
  filters?: { field: keyof MessageTemplate | string; value: any }[]
  sort?: { field: keyof MessageTemplate | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface MessageTemplateBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<MessageTemplate | null>
  getAll(
    params: MessageTemplateQueryParams,
    context: SessionContext,
  ): Promise<{
    items: MessageTemplate[]
    total: number
  }>
  create(
    data: MessageTemplateCreateInput,
    context: SessionContext,
  ): Promise<MessageTemplate>
  update(
    id: string,
    data: MessageTemplateUpdateInput,
    context: SessionContext,
  ): Promise<MessageTemplate | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(
    data: MessageTemplateCreateInput[],
    context: SessionContext,
  ): Promise<MessageTemplate[]>
  bulkUpdate(
    updates: { id: string; data: MessageTemplateUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>

  queryVectorDB(
    query: string,
    context: SessionContext,
    topK?: number,
  ): Promise<MessageTemplateVectorDB[]>
}
