import { BaseVectorDBRepository } from "../BaseVectorDBRepository"
import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"

export interface MessageTemplateVectorDB {
  id: string
  query: string
  template: string
}

export interface MessageTemplateVectorDBRepository
  extends BaseVectorDBRepository<MessageTemplateVectorDB> {}

export class PineconeMessageTemplateVectorDBRepository
  implements MessageTemplateVectorDBRepository
{
  private namespace: Index<RecordMetadata>

  constructor(pineconeApiKey: string, organization: string) {
    const pinecone = new Pinecone({ apiKey: pineconeApiKey })
    this.namespace = pinecone.index("message-template").namespace(organization)
  }

  async create(item: MessageTemplateVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: item.id,
        text: item.query,
        template: item.template,
      },
    ])
  }

  async update(id: string, item: MessageTemplateVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: id,
        text: item.query,
        template: item.template,
      },
    ])
  }

  async delete(id: string): Promise<void> {
    await this.namespace.deleteMany([id])
  }

  async search(
    text: string,
    topK: number = 3,
  ): Promise<MessageTemplateVectorDB[]> {
    const searchResult = await this.namespace.searchRecords({
      query: {
        topK,
        inputs: { text: text }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        query: metadata?.text || "",
        template: metadata?.template || "",
      }
    })
  }
}
