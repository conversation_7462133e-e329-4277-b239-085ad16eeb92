export interface BaseQueryParams<T> {
  search?: string
  filters?: { field: keyof T | string; value: any }[]
  sort?: { field: keyof T | string; direction: "ASC" | "DESC" }[]
  limit?: number
  offset?: number
  includeDeleted?: boolean
}

export function buildMongoQuery<T>(
  initialQuery: { query: Record<string, any>; sort: Record<string, any> },
  params: BaseQueryParams<T>,
  searchableFields: (keyof T | string)[] = [],
): {
  query: Record<string, any>
  sort: Record<string, 1 | -1>
  limit: number
  offset: number
} {
  const query: Record<string, any> = { ...initialQuery.query }
  const sort: Record<string, 1 | -1> = {}

  // 🔍 Full-text search on specified fields
  if (params.search && searchableFields.length > 0) {
    const searchTerm = params.search.trim()
    query.$or = searchableFields.map((field) => ({
      [field]: { $regex: searchTerm, $options: "i" },
    }))
  }

  // 🧼 Apply filters
  if (params.filters?.length) {
    for (const filter of params.filters) {
      query[filter.field as string] = filter.value
    }
  }

  // 🧹 Exclude soft-deleted items unless explicitly included
  const hasDeletedAtFilter = params.filters?.some(
    (f) => f.field === "deletedAt",
  )
  if (!params.includeDeleted && !hasDeletedAtFilter) {
    query.deletedAt = { $exists: false }
  }

  if (params.sort?.length) {
    for (const s of params.sort) {
      sort[s.field as string] = s.direction === "ASC" ? 1 : -1
    }
  } else {
    sort.createdAt = -1 // Default sort
  }

  // 📄 Pagination
  const limit = params.limit ?? 20
  const page = params.offset ?? 1
  const offset = (page - 1) * limit

  return {
    query,
    sort,
    limit: Math.max(0, limit),
    offset: Math.max(0, offset),
  }
}
