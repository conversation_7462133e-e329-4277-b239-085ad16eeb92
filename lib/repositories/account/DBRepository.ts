import { UserBusinessLogicInterface } from "../users/interface"
import { AccountInfo } from "./interface"
import { SessionContext } from "../auth/types"

// Account DB Repository interface - delegates to User repository
export interface AccountDBRepositoryInterface {
  // Get account information by user ID
  getAccountById(userId: string, context: SessionContext): Promise<AccountInfo | null>

  // Update account name by user ID
  updateAccountName(userId: string, name: string, context: SessionContext): Promise<boolean>
}

// Implementation that uses User repository
export class AccountDBRepository implements AccountDBRepositoryInterface {
  constructor(private userBusinessLogic: UserBusinessLogicInterface) { }

  async getAccountById(userId: string, context: SessionContext): Promise<AccountInfo | null> {
    const user = await this.userBusinessLogic.getById(userId, context)

    if (!user) {
      return null
    }

    // Map User to AccountInfo (only safe fields)
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }

  async updateAccountName(userId: string, name: string, context: SessionContext): Promise<boolean> {
    // Get current user to preserve other fields
    const currentUser = await this.userBusinessLogic.getById(userId, context)

    if (!currentUser) {
      return false
    }

    // Update only the name field, preserving all other user data
    const updateData = {
      name,
      email: currentUser.email,
      ARRAY_FIELD2: currentUser.ARRAY_FIELD2,
      tags: currentUser.tags,
      ARRAY_FIELD: currentUser.ARRAY_FIELD,
      isActive: currentUser.isActive,
      updatedBy: userId,
    }

    const updatedUser = await this.userBusinessLogic.update(userId, updateData, context)

    return updatedUser !== null
  }
}
