import { SessionContext } from "../auth/types"
import { AccountDBRepositoryInterface } from "./DBRepository"
import {
  AccountInfo,
  AccountBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"

export class AccountBusinessLogicImpl implements AccountBusinessLogicInterface {
  constructor(private accountDb: AccountDBRepositoryInterface) { }

  async getAccount(context: SessionContext): Promise<AccountInfo | null> {
    // Get current user ID from session context
    const userId = context.user.id
    if (!userId) {
      throw createError("User not authenticated", "UNAUTHORIZED")
    }

    return await this.accountDb.getAccountById(userId, context)
  }

  async updateName(name: string, context: SessionContext): Promise<boolean> {
    // Validate input
    if (!name || typeof name !== "string" || name.trim() === "") {
      throw createError("Name is required and must be a non-empty string", "INVALID_INPUT")
    }

    // Get current user ID from session context
    const userId = context.user.id
    if (!userId) {
      throw createError("User not authenticated", "UNAUTHORIZED")
    }

    // Trim the name
    const trimmedName = name.trim()

    return await this.accountDb.updateAccountName(userId, trimmedName, context)
  }
}
