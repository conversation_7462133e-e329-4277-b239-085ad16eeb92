import { SessionContext } from "../auth/types"

// Account information interface - subset of User data for account management
export interface AccountInfo {
  id: string
  name: string
  email: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Input for updating account name
export interface AccountUpdateNameInput {
  name: string
}

// Account business logic interface
export interface AccountBusinessLogicInterface {
  // Get current account information
  getAccount(context: SessionContext): Promise<AccountInfo | null>

  // Update account name
  updateName(name: string, context: SessionContext): Promise<boolean>
}
