import { BaseVectorDBRepository } from "../BaseVectorDBRepository"
import { Pinecone, Index, RecordMetadata } from "@pinecone-database/pinecone"

export interface AiRuleVectorDB {
  id: string
  message: string
  rule: string
}

export interface AiRuleVectorDBRepository
  extends BaseVectorDBRepository<AiRuleVectorDB> {}

export class PineconeAiRuleVectorDBRepository
  implements AiRuleVectorDBRepository
{
  private namespace: Index<RecordMetadata>

  constructor(pineconeApiKey: string, organization: string) {
    const pinecone = new Pinecone({ apiKey: pineconeApiKey })

    // Use full constructor for host targeting
    this.namespace = pinecone.index("message-rule").namespace(organization)
  }

  async create(item: AiRuleVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: item.id,
        text: item.message,
        rule: item.rule,
      },
    ])
  }

  async update(id: string, item: AiRuleVectorDB): Promise<void> {
    await this.namespace.upsertRecords([
      {
        _id: id,
        text: item.message,
        rule: item.rule,
      },
    ])
  }

  async delete(id: string): Promise<void> {
    await this.namespace.deleteMany([id])
  }

  async search(query: string, topK: number = 3): Promise<AiRuleVectorDB[]> {
    const searchResult = await this.namespace.searchRecords({
      query: {
        topK,
        inputs: { text: query }, // this gets embedded automatically
      },
    })

    return (searchResult.result.hits || []).map((hit) => {
      const metadata = hit.fields as any

      return {
        id: hit._id,
        message: metadata?.text || "",
        rule: metadata?.rule || "",
      }
    })
  }
}
