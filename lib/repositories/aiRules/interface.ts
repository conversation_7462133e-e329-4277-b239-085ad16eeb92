import { AiRuleVectorDB } from "./VectorDBRepository"
import { SessionContext } from "../auth/types"

export interface AiRule {
  id: string
  name: string
  description?: string
  conditions: string[]
  actions: string[]
  tags?: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface AiRuleCreateInput {
  name: string
  description?: string
  conditions: string[]
  actions: string[]
  tags?: string[]
  isActive?: boolean
  createdBy: string
  organizationId?: string
}

export interface AiRuleUpdateInput {
  name?: string
  description?: string
  conditions?: string[]
  actions?: string[]
  tags?: string[]
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
}

export interface AiRuleQueryParams {
  search?: string
  filters?: { field: keyof AiRule | string; value: any }[]
  sort?: { field: keyof AiRule | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface AiRuleBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<AiRule | null>
  getAll(
    params: AiRuleQueryParams,
    context: SessionContext,
  ): Promise<{
    items: AiRule[]
    total: number
  }>
  create(data: AiRuleCreateInput, context: SessionContext): Promise<AiRule>
  update(
    id: string,
    data: AiRuleUpdateInput,
    context: SessionContext,
  ): Promise<AiRule | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(
    data: AiRuleCreateInput[],
    context: SessionContext,
  ): Promise<AiRule[]>
  bulkUpdate(
    updates: { id: string; data: AiRuleUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>

  queryVectorDB(
    query: string,
    context: SessionContext,
    topK?: number,
  ): Promise<AiRuleVectorDB[]>
}
