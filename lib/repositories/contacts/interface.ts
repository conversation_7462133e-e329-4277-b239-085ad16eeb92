// contact.ts

import { SessionContext } from "../auth/types"

export interface Contact {
  id: string
  name: string
  phone: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]

  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy?: string
  updatedBy?: string
  organizationId?: string
}

export interface ContactCreateInput {
  name: string
  phone: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]
  createdBy?: string
  organizationId?: string
}

export interface ContactUpdateInput {
  name?: string
  phone?: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]
  updatedBy?: string
  organizationId?: string
}

export interface ContactQueryParams {
  search?: string
  filters?: { field: keyof Contact | string; value: any }[]
  sort?: { field: keyof Contact | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface ContactBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Contact | null>
  getAll(
    params: ContactQueryParams,
    context: SessionContext,
  ): Promise<{
    items: Contact[]
    total: number
  }>
  create(data: ContactCreateInput, context: SessionContext): Promise<Contact>
  update(
    id: string,
    data: ContactUpdateInput,
    context: SessionContext,
  ): Promise<Contact | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>

  bulkCreate(
    data: ContactCreateInput[],
    context: SessionContext,
  ): Promise<Contact[]>
  bulkUpdate(
    updates: { id: string; data: ContactUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
}
