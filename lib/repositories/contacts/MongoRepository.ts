import { contactsSearchConfig } from "@/app/api/v1/search-configs/entities/contacts"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { Document, WithId } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { buildMongoQuery } from "../queryBuilder"
import { ContactDBRepository } from "./DBRepository"
import {
  Contact,
  ContactCreateInput,
  ContactQueryParams,
  ContactUpdateInput,
} from "./interface"
import { nanoid } from "nanoid"

function mapMongoDocToContact(doc: WithId<Document> | null): Contact | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as unknown as Contact
}

export class MongoContactRepository implements ContactDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.CONTACTS)
  }

  async getById(id: string, includeDeleted = false): Promise<Contact | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToContact(doc)
  }

  async getAll(
    params: ContactQueryParams,
  ): Promise<{ items: Contact[]; total: number }> {
    const initialQuery = contactsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: params?.page,
      },
      contactsSearchConfig.searchableFields,
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToContact)
      .filter((i): i is Contact => i !== null)

    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(params: ContactQueryParams): Promise<{ total: number }> {
    const initialQuery = contactsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      contactsSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: ContactCreateInput): Promise<Contact> {
    const now = new Date()
    const id = `contact-${nanoid(10)}`
    const doc = {
      id,
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return doc as Contact
  }

  async update(id: string, data: ContactUpdateInput): Promise<Contact | null> {
    const existing = await this.collection.findOne({
      id,
      deletedAt: { $exists: false },
    })
    if (!existing) return null

    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const updateResult = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return updated ? mapMongoDocToContact(updated) : null
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async bulkCreate(data: ContactCreateInput[]): Promise<Contact[]> {
    const now = new Date()
    const docs = data.map((d) => ({
      id: `contact-${nanoid(10)}`,
      ...d,
      createdAt: now,
      updatedAt: now,
    }))

    await this.collection.insertMany(docs)
    return docs as Contact[]
  }

  async bulkUpdate(
    updates: { id: string; data: ContactUpdateInput }[],
  ): Promise<number> {
    let count = 0
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { id },
        { $set: { ...data, updatedAt: new Date() } },
      )
      if (res.modifiedCount) count++
    }
    return count
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({ id: { $in: ids } })
      return result.deletedCount ?? 0
    } else {
      const result = await this.collection.updateMany(
        { id: { $in: ids } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount ?? 0
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
