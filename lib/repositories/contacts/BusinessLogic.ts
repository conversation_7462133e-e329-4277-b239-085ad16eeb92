import {
  Contact,
  ContactCreateInput,
  ContactUpdateInput,
  ContactQueryParams,
  ContactBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { ContactDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"

export class ContactBusinessLogic implements ContactBusinessLogicInterface {
  constructor(private readonly db: ContactDBRepository) { }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("Contact ID is required", "INVALID_ID")
  }

  private trimCreateInput(data: ContactCreateInput): ContactCreateInput {
    return {
      ...data,
      name: data.name.trim(),
      phone: data.phone?.trim() ?? "",
      tags: data.tags?.map((t) => t.trim()) ?? [],
    }
  }

  private trimUpdateInput(data: ContactUpdateInput): ContactUpdateInput {
    return {
      ...data,
      name: data.name?.trim(),
      phone: data.phone?.trim(),
      tags: data.tags?.map((t) => t.trim()),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Contact | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: ContactQueryParams,
    context: SessionContext,
  ): Promise<{ items: Contact[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }
    return this.db.getAll(paramsWithContext)
  }

  async getByPhone(
    phone: string,
    context: SessionContext,
  ): Promise<Contact | null> {
    if (!phone || !phone.trim()) {
      throw createError("Phone number is required", "INVALID_PHONE")
    }
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "phone", value: phone }, ...contextFilters],
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async create(
    data: ContactCreateInput,
    context: SessionContext,
  ): Promise<Contact> {
    const trimmedData = this.trimCreateInput(data)

    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return this.db.create(dataWithContext)
  }

  async update(
    id: string,
    data: ContactUpdateInput,
    context: SessionContext,
  ): Promise<Contact | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if contact exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Contact not found", "NOT_FOUND")
    }

    const existingRule = existingResult.items[0]

    if (data.name && data.name.trim() !== existingRule.name) {
      const duplicates = await this.db.getAll({
        filters: [
          { field: "name", value: data.name.trim() },
          ...contextFilters,
        ],
      })
      if (duplicates.items.some((r) => r.id !== id)) {
        throw createError(
          "Another Contact with this name exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }
    return this.db.update(id, trimmedData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if contact exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Contact not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if contact exists and belongs to the current context (including deleted)
    const contextFilters = this.buildContextFilters(context)
    const contactResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted: true,
    })

    if (contactResult.items.length === 0) return false

    const contact = contactResult.items[0]
    if (!contact.deletedAt) return false

    // Check for conflicts by name within the same context
    const conflict = await this.db.getAll({
      filters: [{ field: "name", value: contact.name }, ...contextFilters],
    })
    if (conflict.items.length > 0) return false

    return this.db.restore(id)
  }

  async bulkCreate(
    data: ContactCreateInput[],
    context: SessionContext,
  ): Promise<Contact[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_CREATE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const entry of data) {
      this.trimCreateInput(entry) // Will throw if invalid
      const existing = await this.db.getAll({
        filters: [
          { field: "name", value: entry.name.trim() },
          ...contextFilters,
        ],
      })
      if (existing.items.length > 0) {
        throw createError(
          `Duplicate name found: ${entry.name}`,
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = data.map((d) => ({
      ...this.trimCreateInput(d),
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }))
    return this.db.bulkCreate(trimmedData)
  }

  async bulkUpdate(
    updates: { id: string; data: ContactUpdateInput }[],
    context: SessionContext,
  ): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_UPDATE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const { id, data } of updates) {
      this.validateId(id)

      if (!data || Object.keys(data).length === 0) {
        throw createError(
          `No data provided for update of ID ${id}`,
          "INVALID_UPDATE_DATA",
        )
      }

      // Check if contact exists and belongs to the current context
      const existingResult = await this.db.getAll({
        filters: [{ field: "id", value: id }, ...contextFilters],
      })

      if (existingResult.items.length === 0) {
        throw createError(`Contact with ID ${id} not found`, "NOT_FOUND")
      }

      const existingRule = existingResult.items[0]

      if (data.name && data.name.trim() !== existingRule.name) {
        const duplicates = await this.db.getAll({
          filters: [
            { field: "name", value: data.name.trim() },
            ...contextFilters,
          ],
        })
        if (duplicates.items.some((r) => r.id !== id)) {
          throw createError(
            `Duplicate name in update: ${data.name}`,
            "DUPLICATE_NAME",
          )
        }
      }

      this.trimUpdateInput(data) // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: {
        ...this.trimUpdateInput(data),
        updatedBy: context.user.id,
      },
    }))

    return this.db.bulkUpdate(trimmedUpdates)
  }

  async bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete = false,
  ): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError(
        "IDs must be a non-empty array",
        "INVALID_BULK_DELETE_DATA",
      )
    }

    const contextFilters = this.buildContextFilters(context)

    for (const id of ids) {
      this.validateId(id)

      // Check if contact exists and belongs to the current context
      const contactResult = await this.db.getAll({
        filters: [{ field: "id", value: id }, ...contextFilters],
      })

      if (contactResult.items.length === 0) {
        throw createError(`Contact with ID ${id} not found`, "NOT_FOUND")
      }
    }

    return this.db.bulkDelete(ids, hardDelete)
  }

  async search(query: string): Promise<Contact[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const result = await this.db.getAll({
      filters: [
        { field: "name", value: { $regex: searchTerm, $options: "i" } },
      ],
    })

    return result.items
  }
}
