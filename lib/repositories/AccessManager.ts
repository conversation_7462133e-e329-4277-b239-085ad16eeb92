import { QueryValueHolder } from "@/app/api/v1/search-configs/filters/base"
import { InMemoryDriver } from "./RedisDriver"
import { MongoDriver } from "./MongoDriver"
import { matchPattern } from "../utils/wildcardPatternMatching"

export type CrudAction = "create" | "read" | "update" | "delete"

// Raw user role data (used in RoleStore)
export interface UserRole {
  name: string
  level: number
}

// Abstract interface for fetching roles from DB (Mongo, Redis, etc.)
export interface RoleStore {
  getUserRolesInGroup(userId: string, groupId: string): Promise<UserRole[]>
  getGroupRoles(groupId: string): Promise<UserRole[]>
  setUserRoles(
    userId: string,
    groupId: string,
    roles: UserRole[],
  ): Promise<void>
}

export interface RuleStore {
  updateRules(groupId: string, rules: AccessRule[]): Promise<void>
  add(groupId: string, rule: AccessRule): Promise<string>
  remove(groupId: string, rule: AccessRule): Promise<void>
  findRulesForResource(
    resource: string,
    groupId: string,
  ): Promise<Array<AccessRule & { group: string }>>
}

// Used to apply tenant-aware filters to a resource query
export interface ResourceAccessManagerQueryParams {
  filters: { field: string; value: QueryValueHolder }[]
}

// Main RBAC interface
export interface ResourceAccessManager<
  Action extends CrudAction = CrudAction,
  T extends ResourceAccessManagerQueryParams = ResourceAccessManagerQueryParams,
> {
  isAllowed(params: {
    resource: string
    action: Action
    userId: string
  }): Promise<boolean>

  addAccessRule(params: {
    resource: string
    group: string
    role: string
    actions: Action[] | ["*"]
  }): Promise<string>

  removeAccessRule(params: {
    resource: string
    group: string
    role: string
    actions: Action[] | ["*"]
  }): Promise<void>

  buildQuery(params: T): Promise<T>

  updateRules(rules: AccessRule[]): Promise<void>
  appendRules(rules: AccessRule[]): Promise<void>
}

// A single access rule for an org
export type AccessRule = {
  resource: string | "*" // ResourcePattern ID or wildcard
  role: string | "*" // Role or wildcard
  actions: CrudAction[] | ["*"] // Actions allowed
  group: string
}

// Internal format used by ruleStore
export type RuleStoreRecord = {
  ruleId: string // usually groupId
  group: string
  rules: AccessRule[]
}

export interface AuditLog {
  logAccessCheck(info: { [key: string]: any }): void

  log(...message: any[]): void
}

export class GroupRoleResourceAccessManager<
  ResourcePattern extends string = string,
> implements ResourceAccessManager {
  constructor(
    private groupId: string,
    private roleStore: RoleStore,
    private ruleStore: RuleStore,
    private auditLog?: AuditLog,
  ) { }

  async updateRules(rules: AccessRule[]): Promise<void> {
    await this.ruleStore.updateRules(this.groupId, rules)
  }

  async appendRules(rules: AccessRule[]): Promise<void> {
    const existing = await this.ruleStore.findRulesForResource(
      "*",
      this.groupId,
    )
    await this.ruleStore.updateRules(this.groupId, [...existing, ...rules])
  }

  async addAccessRule(params: {
    resource: ResourcePattern
    group: string
    role: string
    actions: CrudAction[] | ["*"]
  }): Promise<string> {
    return await this.ruleStore.add(this.groupId, {
      resource: params.resource,
      group: params.group,
      role: params.role,
      actions: params.actions,
    })
  }

  async removeAccessRule(params: {
    resource: ResourcePattern
    group: string
    role: string
    actions: CrudAction[] | ["*"]
  }): Promise<void> {
    await this.ruleStore.remove(this.groupId, {
      resource: params.resource,
      group: params.group,
      role: params.role,
      actions: params.actions,
    })
  }

  async isAllowed(params: {
    resource: ResourcePattern
    action: CrudAction
    userId: string
  }): Promise<boolean> {
    const { resource, action, userId } = params

    const rules = await this.ruleStore.findRulesForResource(
      resource,
      this.groupId,
    )
    const roles = await this.roleStore.getUserRolesInGroup(userId, this.groupId)

    for (const rule of rules) {
      for (const role of roles) {
        const matchesResource = matchResource(rule.resource, resource)
        const matchesRole = rule.role === "*" || rule.role === role.name
        const matchesAction =
          rule.actions.map((a) => a.toString()).includes("*") || rule.actions.map((a) => a.toString()).includes(action)

        const isAllowed = matchesResource && matchesRole && matchesAction

        this.auditLog?.log(`IS ALLOWED ${resource} ${action} ${userId}`, {
          rule,
          role,
          matchesResource,
          matchesRole,
          matchesAction,
          isAllowed,
        })

        if (isAllowed) {
          this.auditLog?.logAccessCheck({
            userId,
            resource,
            action,
            matchedRule: rule,
            role,
            result: isAllowed,
          })
        }

        if (isAllowed) return true
      }
    }

    this.auditLog?.logAccessCheck({
      userId,
      resource,
      action,
      rules,
      roles,
      result: false,
    })

    return false
  }

  async buildQuery(
    params: ResourceAccessManagerQueryParams,
  ): Promise<ResourceAccessManagerQueryParams> {
    // Optional: Add org/role-based filtering logic here for tenant-aware queries
    return params
  }
}

function matchResource(ruleResource: string, actualResource: string): boolean {
  if (!ruleResource.includes("*") && !actualResource.includes("*")) {
    return ruleResource === actualResource
  }

  return matchPattern(ruleResource, actualResource)
}

// ========== Redis Implementations ==========

export class RoleStoreRedisRepository implements RoleStore {
  constructor(private redis: InMemoryDriver) { }

  async getUserRolesInGroup(
    userId: string,
    groupId: string,
  ): Promise<UserRole[]> {
    const key = `roles:${userId}:${groupId}`
    return (await this.redis.get(key)) ?? []
  }

  async getGroupRoles(groupId: string): Promise<UserRole[]> {
    const key = `groupRoles:${groupId}`
    return (await this.redis.get(key)) ?? []
  }

  async setUserRoles(
    userId: string,
    groupId: string,
    roles: UserRole[],
  ): Promise<void> {
    const key = `roles:${userId}:${groupId}`
    await this.redis.set(key, roles)
  }
}

const ruleKey = (groupId: string) => `accessRules:${groupId}`

export class RuleStoreRedisRepository implements RuleStore {
  constructor(private redis: InMemoryDriver) { }

  async updateRules(groupId: string, rules: AccessRule[]): Promise<void> {
    await this.redis.set(ruleKey(groupId), rules)
  }

  async add(groupId: string, rule: AccessRule): Promise<string> {
    const key = ruleKey(groupId)
    const rules: AccessRule[] = (await this.redis.get(key)) ?? []
    rules.push(rule)
    await this.redis.set(key, rules)
    return key
  }

  async remove(groupId: string, ruleToRemove: AccessRule): Promise<void> {
    const key = ruleKey(groupId)
    const rules: AccessRule[] = (await this.redis.get(key)) ?? []
    const filtered = rules.filter(
      (rule) =>
        rule.resource !== ruleToRemove.resource ||
        rule.role !== ruleToRemove.role ||
        rule.group !== ruleToRemove.group ||
        !this.actionsEqual(rule.actions, ruleToRemove.actions),
    )
    await this.redis.set(key, filtered)
  }

  async findRulesForResource(
    resource: string,
    groupId: string,
  ): Promise<Array<AccessRule>> {
    const allMatching: AccessRule[] = []

    const rules =
      (await this.redis.get<AccessRule[]>(`accessRules:${groupId}`)) ?? []
    rules.forEach((rule) => {
      if (
        rule.resource === "*" ||
        rule.resource === resource ||
        matchPattern(rule.resource, resource)
      ) {
        allMatching.push(rule)
      }
    })
    allMatching.push(...rules)

    return allMatching
  }

  private actionsEqual(
    a1: CrudAction[] | ["*"],
    a2: CrudAction[] | ["*"],
  ): boolean {
    if (a1.length !== a2.length) return false
    const set1 = new Set(a1)
    const set2 = new Set(a2)
    return [...set1].every((item) => set2.has(item))
  }
}

// ========== Mongo Implementations ==========

export class RoleStoreMongoRepository implements RoleStore {
  constructor(private db: MongoDriver) { }

  async getUserRolesInGroup(
    userId: string,
    groupId: string,
  ): Promise<UserRole[]> {
    const doc = await this.db
      .getCollection("roles")
      .findOne({ userId, groupId })
    const roles = doc?.roles ?? []
    return roles
  }

  async getGroupRoles(groupId: string): Promise<UserRole[]> {
    const doc = await this.db.getCollection("groupRoles").findOne({ groupId })
    return doc?.roles ?? []
  }

  async setUserRoles(
    userId: string,
    groupId: string,
    roles: UserRole[],
  ): Promise<void> {
    await this.db
      .getCollection("roles")
      .updateOne({ userId, groupId }, { $set: { roles } }, { upsert: true })
  }
}

type AccessRulesMongoDocument = {
  groupId: string
  rules: AccessRule[]
}

export class RuleStoreMongoRepository implements RuleStore {
  constructor(private db: MongoDriver) { }

  async updateRules(groupId: string, rules: AccessRule[]): Promise<void> {
    await this.db
      .getCollection<AccessRulesMongoDocument>("accessRules")
      .updateOne({ groupId }, { $set: { rules } }, { upsert: true })
  }

  async add(groupId: string, rule: AccessRule): Promise<string> {
    const result = await this.db
      .getCollection<AccessRulesMongoDocument>("accessRules")
      .updateOne({ groupId }, { $push: { rules: rule } }, { upsert: true })
    return groupId
  }

  async remove(groupId: string, rule: AccessRule): Promise<void> {
    await this.db
      .getCollection<AccessRulesMongoDocument>("accessRules")
      .updateOne(
        { groupId },
        {
          $pull: {
            rules: {
              resource: rule.resource,
              role: rule.role,
              group: rule.group,
              actions: rule.actions,
            },
          },
        },
      )
  }

  async findRulesForResource(
    resource: string,
    groupId: string,
  ): Promise<AccessRule[]> {
    const doc = await this.db.getCollection("accessRules").findOne({ groupId })
    const rules = doc?.rules ?? []
    const allMatching: AccessRule[] = []
    rules.forEach((rule: any) => {
      if (
        rule.resource === "*" ||
        rule.resource === resource ||
        matchPattern(rule.resource, resource)
      ) {
        allMatching.push(rule)
      }
    })
    return allMatching
  }
}

// ========== Composite ==========

export class CompositeRoleStore implements RoleStore {
  constructor(
    private redis: RoleStoreRedisRepository,
    private mongo: RoleStoreMongoRepository,
  ) { }

  async getUserRolesInGroup(
    userId: string,
    groupId: string,
  ): Promise<UserRole[]> {
    const cached = await this.redis.getUserRolesInGroup(userId, groupId)
    if (cached.length > 0) return cached

    const fallback = await this.mongo.getUserRolesInGroup(userId, groupId)
    if (fallback.length > 0) {
      await this.redis.setUserRoles(userId, groupId, fallback)
    }

    return fallback
  }

  async getGroupRoles(groupId: string): Promise<UserRole[]> {
    const cached = await this.redis.getGroupRoles(groupId)
    if (cached.length > 0) return cached

    const fallback = await this.mongo.getGroupRoles(groupId)
    return fallback
  }

  async setUserRoles(
    userId: string,
    groupId: string,
    roles: UserRole[],
  ): Promise<void> {
    await this.mongo.setUserRoles(userId, groupId, roles)
    await this.redis.setUserRoles(userId, groupId, roles)
  }
}

export class CompositeRuleStore implements RuleStore {
  constructor(
    private redis: RuleStoreRedisRepository,
    private mongo: RuleStoreMongoRepository,
  ) { }

  async updateRules(groupId: string, rules: AccessRule[]): Promise<void> {
    await this.mongo.updateRules(groupId, rules)
    await this.redis.updateRules(groupId, rules)
  }

  async add(groupId: string, rule: AccessRule): Promise<string> {
    await this.mongo.add(groupId, rule)
    return await this.redis.add(groupId, rule)
  }

  async remove(groupId: string, rule: AccessRule): Promise<void> {
    await this.mongo.remove(groupId, rule)
    await this.redis.remove(groupId, rule)
  }

  async findRulesForResource(
    resource: string,
    groupId: string,
  ): Promise<AccessRule[]> {
    const cached = await this.redis.findRulesForResource(resource, groupId)
    if (cached.length > 0) return cached

    const fallback = await this.mongo.findRulesForResource(resource, groupId)
    if (fallback.length > 0) {
      await this.redis.updateRules(groupId, fallback)
    }

    return fallback
  }
}
