import { ConsoleEventSender } from "@/microservices/ConsoleEventSender"
import { ResendEmailSender } from "@/microservices/ResendEmailSender"
import { AiRuleBusinessLogic } from "./aiRules"
import { MongoAiRuleRepository } from "./aiRules/MongoRepository"
import { PineconeAiRuleVectorDBRepository } from "./aiRules/VectorDBRepository"
import { AiWorkflowExecutionBusinessLogic } from "./aiWorkflowExecutions"
import { MongoAiWorkflowExecutionRepository } from "./aiWorkflowExecutions/MongoRepository"
import { AuthBusinessLogic, MongoAuthDBRepository } from "./auth"
import { AuthDBRedisRepository } from "./auth/RedisInMemoryRepository"
import { BroadcastBusinessLogic } from "./broadcast/BusinessLogic"
import { MongoBroadcastRepository } from "./broadcast/MongoRepository"
import { DeviceBusinessLogicImpl } from "./devices/BusinessLogic"
import { MongoDeviceRepository } from "./devices/MongoRepository"
import { ContactBusinessLogic } from "./contacts"
import { MongoContactRepository } from "./contacts/MongoRepository"
import { ConversationMessageBusinessLogic } from "./conversationMessages"
import { MongoConversationMessageRepository } from "./conversationMessages/MongoRepository"
import { ConversationBusinessLogic } from "./conversations"
import { MongoConversationRepository } from "./conversations/MongoRepository"
import { CustomerProfileBusinessLogic } from "./customerProfiles"
import { MongoCustomerProfileRepository } from "./customerProfiles/MongoRepository"
import { DatasourceBusinessLogic } from "./datasources"
import { MongoDatasourceRepository } from "./datasources/MongoRepository"
import { InvitationBusinessLogic } from "./invitations"
import { MongoInvitationRepository } from "./invitations/MongoRepository"
import { KnowledgeBaseBusinessLogic } from "./knowledgeBase/BusinessLogic"
import { MongoKnowledgeBaseDocumentRepository } from "./knowledgeBase/MongoDocumentRepository"
import { MongoKnowledgeBaseRepository } from "./knowledgeBase/MongoRepository"
import { PineconeKnowledgeBaseVectorDBRepository } from "./knowledgeBase/VectorDBRepository"
import { driver } from "./LiveMongoDriver"
import { driver as inMemoryDriver } from "./LiveRedisDriver"
import { MessageTemplateBusinessLogic } from "./messageTemplates"
import { MongoMessageTemplateRepository } from "./messageTemplates/MongoRepository"
import { PineconeMessageTemplateVectorDBRepository } from "./messageTemplates/VectorDBRepository"
import { RoleBusinessLogic } from "./roles"
import { MongoRoleRepository } from "./roles/MongoRepository"
import { TeamBusinessLogic } from "./teams"
import { MongoTeamRepository } from "./teams/MongoRepository"
import { UserBusinessLogic } from "./users"
import { MongoUserRepository } from "./users/MongoRepository"
import { AccountBusinessLogicImpl, AccountDBRepository } from "./account"

const authDb = new MongoAuthDBRepository(driver)
const inMemoryAuthRepo = new AuthDBRedisRepository(inMemoryDriver)
const emailSender = new ResendEmailSender()
const eventSender = new ConsoleEventSender()
export const authBusinessLogic = new AuthBusinessLogic(
  authDb,
  inMemoryAuthRepo,
  emailSender,
  eventSender,
)

const aiRulesDb = new MongoAiRuleRepository(driver)
const aiRulesVectordb = new PineconeAiRuleVectorDBRepository(
  process.env.PINECONE_API_KEY!,
  "testing_cs_ai_namespace",
)
export const aiRulesBusinessLogic = new AiRuleBusinessLogic(
  aiRulesDb,
  aiRulesVectordb,
)

const workflowExecutionsDb = new MongoAiWorkflowExecutionRepository(driver)
export const workflowExecutionsBusinessLogic =
  new AiWorkflowExecutionBusinessLogic(workflowExecutionsDb)

const contactsDb = new MongoContactRepository(driver)
export const contactsBusinessLogic = new ContactBusinessLogic(contactsDb)

const customerProfilesDb = new MongoCustomerProfileRepository(driver)
export const customerProfilesBusinessLogic = new CustomerProfileBusinessLogic(
  customerProfilesDb,
)

const datasourcesDb = new MongoDatasourceRepository(driver)
export const datasourcesBusinessLogic = new DatasourceBusinessLogic(
  datasourcesDb,
)

const messageTemplatesDb = new MongoMessageTemplateRepository(driver)
const messageTemplatesVectordb = new PineconeMessageTemplateVectorDBRepository(
  process.env.PINECONE_API_KEY!,
  "testing_cs_ai_namespace",
)
export const messageTemplatesBusinessLogic = new MessageTemplateBusinessLogic(
  messageTemplatesDb,
  messageTemplatesVectordb,
)

const conversationMessagesDb = new MongoConversationMessageRepository(driver)
export const conversationMessagesBusinessLogic =
  new ConversationMessageBusinessLogic(conversationMessagesDb)

const conversationsDb = new MongoConversationRepository(driver)
export const conversationBusinessLogic = new ConversationBusinessLogic(
  conversationsDb,
)

// Users
const usersDb = new MongoUserRepository(driver)
export const usersBusinessLogic = new UserBusinessLogic(usersDb)

// Account (uses Users business logic)
const accountDb = new AccountDBRepository(usersBusinessLogic)
export const accountBusinessLogic = new AccountBusinessLogicImpl(accountDb)

// Teams
const teamsDb = new MongoTeamRepository(driver)
export const teamsBusinessLogic = new TeamBusinessLogic(teamsDb)

// Invitations
const invitationsDb = new MongoInvitationRepository(driver)
export const invitationsBusinessLogic = new InvitationBusinessLogic(
  invitationsDb,
)

const rolesDb = new MongoRoleRepository(driver)
export const rolesBusinessLogic = new RoleBusinessLogic(rolesDb)

// Knowledge Base
const knowledgeBaseDb = new MongoKnowledgeBaseRepository(driver)
const knowledgeBaseDocumentDb = new MongoKnowledgeBaseDocumentRepository(driver)
const knowledgeBaseVectorDb = new PineconeKnowledgeBaseVectorDBRepository(
  process.env.PINECONE_API_KEY!,
  "testing_cs_ai_namespace",
)
export const knowledgeBaseBusinessLogic = new KnowledgeBaseBusinessLogic(
  knowledgeBaseDb,
  knowledgeBaseDocumentDb,
  knowledgeBaseVectorDb,
)

const broadcastDb = new MongoBroadcastRepository(driver)
export const broadcastBusinessLogic = new BroadcastBusinessLogic(broadcastDb)

const devicesDb = new MongoDeviceRepository(driver)
export const devicesBusinessLogic = new DeviceBusinessLogicImpl(devicesDb)
