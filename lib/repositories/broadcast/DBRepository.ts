import {
  Broadcast,
  BroadcastCreateInput,
  BroadcastUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type BroadcastDbQueryParams = BaseQueryParams<Broadcast>

export class BroadcastDBRepository
  implements
    BaseDbRepository<
      Broadcast,
      BroadcastCreateInput,
      BroadcastUpdateInput,
      BroadcastDbQueryParams
    >
{
  getById(id: string, includeDeleted?: boolean): Promise<Broadcast | null> {
    throw new Error("Method not implemented.")
  }
  getAll(
    params: BroadcastDbQueryParams,
  ): Promise<{ items: Broadcast[]; total: number }> {
    throw new Error("Method not implemented.")
  }
  getCount(params: BroadcastDbQueryParams): Promise<{ total: number }> {
    throw new Error("Method not implemented.")
  }
  create(data: BroadcastCreateInput): Promise<Broadcast> {
    throw new Error("Method not implemented.")
  }
  bulkCreate(data: BroadcastCreateInput[]): Promise<Broadcast[]> {
    throw new Error("Method not implemented.")
  }
  update(id: string, data: BroadcastUpdateInput): Promise<Broadcast | null> {
    throw new Error("Method not implemented.")
  }
  bulkUpdate(
    updates: { id: string; data: BroadcastUpdateInput }[],
  ): Promise<number> {
    throw new Error("Method not implemented.")
  }
  delete(id: string, hardDelete?: boolean): Promise<boolean> {
    throw new Error("Method not implemented.")
  }
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number> {
    throw new Error("Method not implemented.")
  }
  restore(id: string): Promise<boolean> {
    throw new Error("Method not implemented.")
  }
  clear(): Promise<void> {
    throw new Error("Method not implemented.")
  }
}
