import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { Document, WithId } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { buildMongoQuery } from "../queryBuilder"
import { BroadcastDBRepository } from "./DBRepository"
import {
  Broadcast,
  BroadcastCreateInput,
  BroadcastQueryParams,
  BroadcastUpdateInput,
} from "./interface"
import { nanoid } from "nanoid"
import { broadcastsSearchConfig } from "@/app/api/v1/search-configs/entities/broadcast"

function mapMongoDocToBroadcast(
  doc: WithId<Document> | null,
): Broadcast | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as unknown as Broadcast
}

export class MongoBroadcastRepository extends BroadcastDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    super()
    this.collection = driver.getCollection(MONGO_COLLECTIONS.BROADCASTS)
  }

  async getById(id: string, includeDeleted = false): Promise<Broadcast | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToBroadcast(doc)
  }

  async getAll(
    params: BroadcastQueryParams,
  ): Promise<{ items: Broadcast[]; total: number }> {
    const initialQuery = broadcastsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: 0,
      },
      broadcastsSearchConfig.searchableFields,
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToBroadcast)
      .filter((i): i is Broadcast => i !== null)

    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(params: BroadcastQueryParams): Promise<{ total: number }> {
    const initialQuery = broadcastsSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      broadcastsSearchConfig.searchableFields,
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: BroadcastCreateInput): Promise<Broadcast> {
    const now = new Date()
    const id = `broadcast-${nanoid(10)}`
    const doc = {
      id,
      ...data,
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return doc as Broadcast
  }

  async update(
    id: string,
    data: BroadcastUpdateInput,
  ): Promise<Broadcast | null> {
    const existing = await this.collection.findOne({
      id,
      deletedAt: { $exists: false },
    })
    if (!existing) return null

    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const updateResult = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return updated ? mapMongoDocToBroadcast(updated) : null
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async bulkCreate(data: BroadcastCreateInput[]): Promise<Broadcast[]> {
    const now = new Date()
    const docs = data.map((d) => ({
      id: `broadcast-${nanoid(10)}`,
      ...d,
      createdAt: now,
      updatedAt: now,
    }))

    await this.collection.insertMany(docs)
    return docs as Broadcast[]
  }

  async bulkUpdate(
    updates: { id: string; data: BroadcastUpdateInput }[],
  ): Promise<number> {
    let count = 0
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { id },
        { $set: { ...data, updatedAt: new Date() } },
      )
      if (res.modifiedCount) count++
    }
    return count
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({ id: { $in: ids } })
      return result.deletedCount ?? 0
    } else {
      const result = await this.collection.updateMany(
        { id: { $in: ids } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount ?? 0
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
