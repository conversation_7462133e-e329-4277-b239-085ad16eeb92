import {
  BroadcastBusinessLogicInterface,
  Broadcast,
  BroadcastCreateInput,
  BroadcastUpdateInput,
  BroadcastQueryParams,
  BroadcastRecipient,
  BroadcastStats,
  BroadcastStatus,
  BroadcastRecipientStatus,
} from "./interface"
import { SessionContext } from "../auth/types"
import { createError } from "@/lib/utils/common"
import { BroadcastDBRepository } from "./DBRepository"
import { providers } from "@/lib/providers"
import { contactsBusinessLogic, devicesBusinessLogic } from "../businessLogics"

export class BroadcastBusinessLogic implements BroadcastBusinessLogicInterface {
  constructor(private db: BroadcastDBRepository) { }
  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim() === "") {
      throw createError("Invalid ID provided", "INVALID_ID")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    } else {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    return filters
  }

  private trimCreateInput(data: BroadcastCreateInput): BroadcastCreateInput {
    if (
      !data.title ||
      typeof data.title !== "string" ||
      data.title.trim() === ""
    ) {
      throw createError("Title is required", "INVALID_TITLE")
    }

    if (
      !data.message ||
      typeof data.message !== "string" ||
      data.message.trim() === ""
    ) {
      throw createError("Message is required", "INVALID_MESSAGE")
    }

    if (
      !Array.isArray(data.targetContacts) ||
      data.targetContacts.length === 0
    ) {
      throw createError(
        "At least one target contact is required",
        "INVALID_TARGET_CONTACTS",
      )
    }

    if (!data.deviceId || typeof data.deviceId !== "string" || data.deviceId.trim() === "") {
      throw createError("Device is required", "INVALID_DEVICE")
    }

    return {
      ...data,
      title: data.title.trim(),
      message: data.message.trim(),
      deviceId: data.deviceId.trim(),
    }
  }

  async create(
    data: BroadcastCreateInput,
    context: SessionContext,
  ): Promise<Broadcast> {
    const trimmedData = this.trimCreateInput(data)

    // Initialize recipients array from target contacts
    const recipients: BroadcastRecipient[] = trimmedData.targetContacts.map(
      (contactId) => ({
        contactId,
        name: "", // Will be populated when sending
        status: BroadcastRecipientStatus.PENDING,
      }),
    )

    // Add context information to the data
    const dataWithContext = {
      ...trimmedData,
      recipients,
      status: BroadcastStatus.DRAFT,
      totalTargets: trimmedData.targetContacts.length,
      sentCount: 0,
      failedCount: 0,
      pendingCount: trimmedData.targetContacts.length,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const broadcast = await this.db.create(dataWithContext)
    return broadcast
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<Broadcast | null> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const filters = [{ field: "id", value: id }, ...contextFilters]

    if (!includeDeleted) {
      filters.push({ field: "deletedAt", value: undefined as any })
    }

    const result = await this.db.getAll({ filters, limit: 1 })
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: BroadcastQueryParams,
    context: SessionContext,
  ): Promise<{ items: Broadcast[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)

    const queryParams = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }

    return this.db.getAll(queryParams)
  }

  async update(
    id: string,
    data: BroadcastUpdateInput,
    context: SessionContext,
  ): Promise<Broadcast | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if broadcast exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    const existing = existingResult.items[0]

    // Only allow updates to draft broadcasts
    if (existing.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Cannot update broadcast that is not in draft status",
        "INVALID_STATUS",
      )
    }

    const updateData: any = {
      ...data,
      updatedBy: context.user.id,
      updatedAt: new Date(),
    }

    // If target contacts are updated, update the recipients and counts
    if (data.targetContacts) {
      const recipients: BroadcastRecipient[] = data.targetContacts.map(
        (contactId) => ({
          contactId,
          name: "", // Will be populated when sending
          status: BroadcastRecipientStatus.PENDING,
        }),
      )

      updateData.recipients = recipients
      updateData.totalTargets = data.targetContacts.length
      updateData.pendingCount = data.targetContacts.length
      updateData.sentCount = 0
      updateData.failedCount = 0
    }

    return this.db.update(id, updateData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async getRecipients(
    broadcastId: string,
    context: SessionContext,
  ): Promise<BroadcastRecipient[]> {
    this.validateId(broadcastId)

    // Get broadcast and return its recipients array
    const broadcast = await this.getById(broadcastId, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    return broadcast.recipients || []
  }

  async startBroadcast(id: string, context: SessionContext): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Can only start broadcasts in draft status",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.SENDING,
      startedAt: new Date(),
      updatedBy: context.user.id,
    })

    // Trigger actual broadcast sending logic
    await this.performBroadcastSending(id, context)

    return true
  }

  async cancelBroadcast(id: string, context: SessionContext): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.SENDING) {
      throw createError(
        "Can only cancel broadcasts that are currently sending",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.CANCELLED,
      updatedBy: context.user.id,
    })

    return true
  }

  async scheduleBroadcast(
    id: string,
    scheduledAt: Date,
    context: SessionContext,
  ): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.DRAFT) {
      throw createError(
        "Can only schedule broadcasts in draft status",
        "INVALID_STATUS",
      )
    }

    await this.db.update(id, {
      status: BroadcastStatus.SCHEDULED,
      scheduledAt,
      updatedBy: context.user.id,
    })

    return true
  }

  private async performBroadcastSending(id: string, context: SessionContext): Promise<boolean> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    if (broadcast.status !== BroadcastStatus.SENDING) {
      throw createError(
        "Can only send broadcasts that are in sending status",
        "INVALID_STATUS",
      )
    }

    // Get the device to use for sending
    const device = await devicesBusinessLogic.getById(broadcast.deviceId, context)
    if (!device) {
      throw createError("Device not found", "DEVICE_NOT_FOUND")
    }

    if (!device.isActive) {
      throw createError("Device is not active", "DEVICE_INACTIVE")
    }

    const provider = providers[process.env.WHATSAPP_PROVIDER!]
    if (!provider) {
      throw createError("Provider not available", "PROVIDER_NOT_FOUND")
    }

    let sentCount = 0
    let failedCount = 0
    const updatedRecipients: BroadcastRecipient[] = []

    // Process each recipient
    for (const recipient of broadcast.recipients) {
      try {
        // Get contact information to get the conversation ID
        const contact = await contactsBusinessLogic.getById(
          recipient.contactId,
          context,
        )

        if (!contact) {
          // Mark as failed if contact not found
          updatedRecipients.push({
            ...recipient,
            status: BroadcastRecipientStatus.FAILED,
            failedAt: new Date(),
            errorMessage: "Contact not found",
          })
          failedCount++
          continue
        }

        // Use contact phone as conversation ID for WhatsApp
        const conversationId = contact.phone || contact.id

        // Use the device's session ID
        const session = device.sessionId

        // Send message using provider
        await provider.sendMessage(conversationId, broadcast.message, session)

        // Mark as sent
        updatedRecipients.push({
          ...recipient,
          name: contact.name,
          phone: contact.phone,
          email: contact.email,
          status: BroadcastRecipientStatus.SENT,
          sentAt: new Date(),
        })
        sentCount++

        // Add small delay between messages to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 1000))
      } catch (error: any) {
        // Mark as failed
        updatedRecipients.push({
          ...recipient,
          status: BroadcastRecipientStatus.FAILED,
          failedAt: new Date(),
          errorMessage: error.message || "Failed to send message",
        })
        failedCount++
      }
    }

    // Update broadcast with results
    const pendingCount = broadcast.totalTargets - sentCount - failedCount
    const isCompleted = pendingCount === 0

    await this.db.update(id, {
      recipients: updatedRecipients,
      sentCount,
      failedCount,
      pendingCount,
      status: isCompleted ? BroadcastStatus.COMPLETED : BroadcastStatus.SENDING,
      completedAt: isCompleted ? new Date() : undefined,
      updatedBy: context.user.id,
    })

    return true
  }

  async getStats(context: SessionContext): Promise<BroadcastStats> {
    const contextFilters = this.buildContextFilters(context)

    // Get total broadcasts
    const totalResult = await this.db.getAll({
      filters: [...contextFilters, { field: "deletedAt", value: null }],
    })

    // Get active broadcasts (sending)
    const activeResult = await this.db.getAll({
      filters: [
        ...contextFilters,
        { field: "status", value: BroadcastStatus.SENDING },
      ],
    })

    // Get completed broadcasts
    const completedResult = await this.db.getAll({
      filters: [
        ...contextFilters,
        { field: "status", value: BroadcastStatus.COMPLETED },
      ],
    })

    // Calculate total messages sent
    const totalMessagesSent = totalResult.items.reduce(
      (sum, broadcast) => sum + broadcast.sentCount,
      0,
    )

    // Calculate average success rate
    const broadcastsWithMessages = totalResult.items.filter(
      (b) => b.totalTargets > 0,
    )
    const averageSuccessRate =
      broadcastsWithMessages.length > 0
        ? (broadcastsWithMessages.reduce(
          (sum, b) => sum + b.sentCount / b.totalTargets,
          0,
        ) /
          broadcastsWithMessages.length) *
        100
        : 0

    return {
      totalBroadcasts: totalResult.total,
      activeBroadcasts: activeResult.total,
      completedBroadcasts: completedResult.total,
      totalMessagesSent,
      averageSuccessRate: Math.round(averageSuccessRate),
      recentActivity: [], // TODO: Implement recent activity tracking
    }
  }

  async getBroadcastProgress(
    id: string,
    context: SessionContext,
  ): Promise<{
    totalTargets: number
    sentCount: number
    failedCount: number
    pendingCount: number
    successRate: number
    status: BroadcastStatus
  }> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    const successRate =
      broadcast.totalTargets > 0
        ? Math.round((broadcast.sentCount / broadcast.totalTargets) * 100)
        : 0

    return {
      totalTargets: broadcast.totalTargets,
      sentCount: broadcast.sentCount,
      failedCount: broadcast.failedCount,
      pendingCount: broadcast.pendingCount,
      successRate,
      status: broadcast.status,
    }
  }

  async getBroadcastRecipients(
    id: string,
    context: SessionContext,
  ): Promise<BroadcastRecipient[]> {
    const broadcast = await this.getById(id, context)
    if (!broadcast) {
      throw createError("Broadcast not found", "NOT_FOUND")
    }

    return broadcast.recipients || []
  }
}
