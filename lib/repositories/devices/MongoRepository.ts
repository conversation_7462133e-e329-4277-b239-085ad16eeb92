import { Collection, Document } from "mongodb"
import { v4 as uuidv4 } from "uuid"
import { MongoDriver } from "../MongoDriver"
import { DeviceDBRepository } from "./DBRepository"
import {
  Device,
  DeviceCreateInput,
  DeviceUpdateInput,
  DeviceQueryParams,
  DeviceStatus,
} from "./interface"
import { BaseQueryParams } from "../BaseDBRepository"
import { buildMongoQuery } from "../queryBuilder"

export class MongoDeviceRepository implements DeviceDBRepository {
  private collection: Collection<Document>

  constructor(private driver: MongoDriver) {
    this.collection = driver.getCollection("devices")
    this.ensureIndexes()
  }

  delete(id: string, hardDelete?: boolean): Promise<boolean> {
    throw new Error("Method not implemented.")
  }
  restore(id: string): Promise<boolean> {
    throw new Error("Method not implemented.")
  }
  private async ensureIndexes() {
    await this.collection.createIndex({ id: 1 }, { unique: true })
    await this.collection.createIndex({ sessionId: 1 }, { unique: true })
    await this.collection.createIndex({ organizationId: 1 })
    await this.collection.createIndex({ createdBy: 1 })
    await this.collection.createIndex({ status: 1 })
    await this.collection.createIndex({ isActive: 1 })
  }

  async getById(id: string, includeDeleted = false): Promise<Device | null> {
    const filter: any = { id }
    if (!includeDeleted) {
      filter.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(filter)
    return doc ? this.mapToEntity(doc) : null
  }

  async getBySessionId(sessionId: string): Promise<Device | null> {
    const doc = await this.collection.findOne({
      sessionId,
      deletedAt: { $exists: false }
    })
    return doc ? this.mapToEntity(doc) : null
  }

  async getAll(params: DeviceQueryParams): Promise<{ items: Device[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.offset,
      },
      [],
    )


    const [items, total] = await Promise.all([
      this.collection
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit)
        .toArray(),
      this.collection.countDocuments(query),
    ])

    return {
      items: items.map(doc => this.mapToEntity(doc)),
      total,
    }
  }

  async getCount(params: DeviceQueryParams): Promise<{ total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.offset,
      },
      [],
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: DeviceCreateInput): Promise<Device> {
    const now = new Date()
    const doc = {
      ...data,
      id: uuidv4(),
      isActive: data.isActive ?? true,
      linkedAt: data.linkedAt || now,
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(doc)
    return this.mapToEntity(doc)
  }

  async update(id: string, data: DeviceUpdateInput): Promise<Device | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" }
    )

    return result ? this.mapToEntity(result) : null
  }

  async updateBySessionId(sessionId: string, data: DeviceUpdateInput): Promise<Device | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { sessionId, deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" }
    )

    return result ? this.mapToEntity(result) : null
  }

  private mapToEntity(doc: any): Device {
    return {
      id: doc.id,
      sessionId: doc.sessionId,
      name: doc.name,
      platform: doc.platform,
      status: doc.status,
      providerName: doc.providerName,
      providerData: doc.providerData,
      me: doc.me,
      isActive: doc.isActive,
      lastSeenAt: doc.lastSeenAt,
      linkedAt: doc.linkedAt,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      deletedAt: doc.deletedAt,
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      organizationId: doc.organizationId,
    }
  }
}
