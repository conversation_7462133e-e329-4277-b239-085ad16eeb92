import {
  BaseQueryParams,
  IDbGetById,
  IDbGet<PERSON>ll,
  IDbCreate,
  IDbUpdate,
  IDbDelete
} from "../BaseDBRepository"
import {
  Device,
  DeviceCreateInput,
  DeviceUpdateInput,
  DeviceQueryParams,
} from "./interface"

export interface DeviceDBRepository
  extends IDbGetById<Device>,
  IDbGetAll<Device, DeviceQueryParams>,
  IDbCreate<Device, DeviceCreateInput>,
  IDbUpdate<Device, DeviceUpdateInput>,
  IDbDelete {
  getCount(params: DeviceQueryParams): Promise<{ total: number }>
  getBySessionId(sessionId: string): Promise<Device | null>
  updateBySessionId(sessionId: string, data: DeviceUpdateInput): Promise<Device | null>
}
