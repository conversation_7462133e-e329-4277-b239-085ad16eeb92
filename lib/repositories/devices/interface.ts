import { SessionContext } from "../auth/types"
import { BaseQueryParams } from "../BaseDBRepository"

export interface Device {
  id: string
  sessionId: string // The session ID from the provider (e.g., WAHA)
  name: string
  platform: string
  status: DeviceStatus
  providerName: string // e.g., "WAHA API"
  providerData?: any // Raw data from provider
  me?: {
    pushName?: string
    id?: string
    [key: string]: any
  }
  isActive: boolean
  lastSeenAt?: Date
  linkedAt: Date
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export enum DeviceStatus {
  CONNECTED = "CONNECTED",
  DISCONNECTED = "DISCONNECTED",
  SCANNING = "SCANNING",
  FAILED = "FAILED",
  STARTING = "STARTING",
  STOPPING = "STOPPING",
}

export interface DeviceCreateInput {
  sessionId: string
  name: string
  platform: string
  status: DeviceStatus
  providerName: string
  providerData?: any
  me?: {
    pushName?: string
    id?: string
    [key: string]: any
  }
  isActive?: boolean
  lastSeenAt?: Date
  linkedAt?: Date
}

export interface DeviceUpdateInput {
  name?: string
  platform?: string
  status?: DeviceStatus
  providerData?: any
  me?: {
    pushName?: string
    id?: string
    [key: string]: any
  }
  isActive?: boolean
  lastSeenAt?: Date
  updatedBy?: string
  organizationId?: string
}

export interface DeviceQueryParams extends BaseQueryParams<Device> {
  status?: DeviceStatus
  providerName?: string
  isActive?: boolean
  organizationId?: string
  createdBy?: string
}

export interface DeviceBusinessLogic {
  getById(id: string, context: SessionContext): Promise<Device | null>
  getAll(params: DeviceQueryParams, context: SessionContext): Promise<{
    items: Device[]
    total: number
  }>
  getBySessionId(sessionId: string, context: SessionContext): Promise<Device | null>
  create(data: DeviceCreateInput, context: SessionContext): Promise<Device>
  update(id: string, data: DeviceUpdateInput, context: SessionContext): Promise<Device | null>
  delete(id: string, context: SessionContext): Promise<boolean>
  syncWithProvider(context: SessionContext): Promise<{
    synced: number
    created: number
    updated: number
    errors: string[]
  }>
  updateDeviceStatus(sessionId: string, status: DeviceStatus, context: SessionContext): Promise<Device | null>
}
