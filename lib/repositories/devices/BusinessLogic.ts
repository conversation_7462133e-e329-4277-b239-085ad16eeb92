import { SessionContext } from "../auth/types"
import { DeviceDBRepository } from "./DBRepository"
import {
  Device,
  DeviceCreateInput,
  DeviceUpdateInput,
  DeviceQueryParams,
  DeviceBusinessLogic,
  DeviceStatus,
} from "./interface"
import { providers } from "@/lib/providers"

export class DeviceBusinessLogicImpl implements DeviceBusinessLogic {
  constructor(private deviceDb: DeviceDBRepository) { }

  async getById(id: string, context: SessionContext): Promise<Device | null> {
    const device = await this.deviceDb.getById(id)

    if (device && context.organization && device.organizationId !== context.organization.id) {
      return null
    }

    return device
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getAll(params: DeviceQueryParams, context: SessionContext): Promise<{
    items: Device[]
    total: number
  }> {
    const queryParams = {
      ...params,
      filters: [
        ...(params.filters || []),
        ...this.buildContextFilters(context),
      ],
    }


    const result = await this.deviceDb.getAll(queryParams)
    return result
  }

  async getBySessionId(sessionId: string, context: SessionContext): Promise<Device | null> {
    const device = await this.deviceDb.getBySessionId(sessionId)

    if (device && context.organization && device.organizationId !== context.organization.id) {
      return null
    }

    return device
  }

  async create(data: DeviceCreateInput, context: SessionContext): Promise<Device> {
    const deviceData = {
      ...data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return await this.deviceDb.create(deviceData)
  }

  async update(id: string, data: DeviceUpdateInput, context: SessionContext): Promise<Device | null> {
    const existingDevice = await this.getById(id, context)
    if (!existingDevice) return null

    return await this.deviceDb.update(id, data)
  }

  async delete(id: string, context: SessionContext): Promise<boolean> {
    const existingDevice = await this.getById(id, context)
    if (!existingDevice) return false

    return await this.deviceDb.delete(id)
  }

  async updateDeviceStatus(
    sessionId: string,
    status: DeviceStatus,
    context: SessionContext,
  ): Promise<Device | null> {
    const updateData: DeviceUpdateInput = {
      status,
      lastSeenAt: new Date(),
    }

    return await this.deviceDb.updateBySessionId(sessionId, updateData)
  }

  async syncWithProvider(context: SessionContext): Promise<{
    synced: number
    created: number
    updated: number
    errors: string[]
  }> {
    const result = {
      synced: 0,
      created: 0,
      updated: 0,
      errors: [] as string[],
    }

    try {
      const provider = providers[process.env.WHATSAPP_PROVIDER!]
      if (!provider) {
        result.errors.push("No provider configured")
        return result
      }

      const providerDevices = await provider.listDevices()
      if (!Array.isArray(providerDevices)) {
        result.errors.push("Invalid response from provider")
        return result
      }

      const existingDevices = await this.getAll(
        { offset: 1, limit: 1000, sort: [] },
        context,
      )

      const existingDeviceMap = new Map(
        existingDevices.items.map(device => [device.sessionId, device]),
      )

      for (const providerDevice of providerDevices) {
        try {
          const sessionId = providerDevice.name || providerDevice.id
          if (!sessionId) {
            result.errors.push("Device missing session ID")
            continue
          }

          const existingDevice = existingDeviceMap.get(sessionId)
          const deviceData = this.mapProviderDeviceToDevice(providerDevice, provider.name)

          if (existingDevice) {
            await this.deviceDb.updateBySessionId(sessionId, deviceData)
            result.updated++
          } else {
            await this.create({ ...deviceData, sessionId }, context)
            result.created++
          }

          result.synced++
        } catch (error) {
          result.errors.push(`Error processing device: ${error}`)
        }
      }

      const providerSessionIds = new Set(
        providerDevices.map(d => d.name || d.id).filter(Boolean),
      )

      for (const existingDevice of existingDevices.items) {
        if (!providerSessionIds.has(existingDevice.sessionId) && existingDevice.isActive) {
          await this.deviceDb.update(existingDevice.id, {
            isActive: false,
            status: DeviceStatus.DISCONNECTED,
          })
        }
      }
    } catch (error) {
      result.errors.push(`Sync failed: ${error}`)
    }

    return result
  }

  private mapProviderDeviceToDevice(
    providerDevice: any,
    providerName: string,
  ): Omit<DeviceCreateInput, "sessionId" | "createdBy"> {
    return {
      name: providerDevice.me?.pushName || providerDevice.name || "Unknown Device",
      platform: providerDevice.platform || "phone",
      status: this.mapProviderStatus(providerDevice.status),
      providerName,
      providerData: providerDevice,
      me: providerDevice.me,
      isActive: providerDevice.status === "WORKING" || providerDevice.status === "CONNECTED",
      lastSeenAt: new Date(),
    }
  }

  private mapProviderStatus(providerStatus: string): DeviceStatus {
    switch (providerStatus?.toUpperCase()) {
      case "WORKING":
      case "CONNECTED":
        return DeviceStatus.CONNECTED
      case "DISCONNECTED":
      case "STOPPED":
        return DeviceStatus.DISCONNECTED
      case "SCANNING":
        return DeviceStatus.SCANNING
      case "FAILED":
        return DeviceStatus.FAILED
      case "STARTING":
        return DeviceStatus.STARTING
      case "STOPPING":
        return DeviceStatus.STOPPING
      default:
        return DeviceStatus.DISCONNECTED
    }
  }
}
