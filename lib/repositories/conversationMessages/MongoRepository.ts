import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { MongoDriver } from "../MongoDriver"
import { buildMongoQuery } from "../queryBuilder"
import { ConversationMessageDBRepository } from "./DBRepository"
import {
  ConversationMessage,
  ConversationMessageCreateInput,
  ConversationMessageQueryParams,
  ConversationMessageUpdateInput,
} from "./interface"

/**
 * Generates a unique ID with a given prefix.
 */
function generateId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Maps a MongoDB document to the ConversationMessage interface.
 */
function mapMongoDocToConversationMessage(
  doc: any,
): ConversationMessage | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  }
}

export class MongoConversationMessageRepository
  implements ConversationMessageDBRepository
{
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.CHAT_MESSAGES)
    this.ensureIndexes()
  }

  /**
   * Ensures necessary indexes are created for the collection.
   */
  private async ensureIndexes() {
    await this.collection.createIndexes([
      { key: { id: 1 }, unique: true },
      { key: { conversationId: 1 } },
      { key: { createdAt: -1 } },
      { key: { isActive: 1 } },
    ])
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<ConversationMessage | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }
    const doc = await this.collection.findOne(query)
    return mapMongoDocToConversationMessage(doc)
  }

  async getAll(
    params: ConversationMessageQueryParams,
  ): Promise<{ items: ConversationMessage[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      params,
      ["conversationId", "senderId", "recipientId"],
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToConversationMessage)
      .filter((i): i is ConversationMessage => i !== null)
    const total = await this.collection.countDocuments(query)

    return { items, total }
  }

  async getCount(
    params: ConversationMessageQueryParams,
  ): Promise<{ total: number }> {
    const { query } = buildMongoQuery({ query: {}, sort: {} }, params, [
      "conversationId",
      "senderId",
    ])
    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(
    data: ConversationMessageCreateInput,
  ): Promise<ConversationMessage> {
    const now = new Date()
    const doc = {
      ...data,
      id: generateId("conversation-msg"),
      createdAt: now,
      updatedAt: now,
    }
    await this.collection.insertOne(doc)
    return mapMongoDocToConversationMessage(doc)!
  }

  async update(
    id: string,
    data: ConversationMessageUpdateInput,
  ): Promise<ConversationMessage | null> {
    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const result = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (result.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return mapMongoDocToConversationMessage(updated)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async bulkCreate(
    data: ConversationMessageCreateInput[],
  ): Promise<ConversationMessage[]> {
    const now = new Date()
    const docs = data.map((d) => ({
      ...d,
      id: generateId("conversation-msg"),
      createdAt: now,
      updatedAt: now,
    }))

    await this.collection.insertMany(docs)
    return docs
      .map(mapMongoDocToConversationMessage)
      .filter((i): i is ConversationMessage => i !== null)
  }

  async bulkUpdate(
    updates: { id: string; data: ConversationMessageUpdateInput }[],
  ): Promise<number> {
    let count = 0
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { id },
        { $set: { ...data, updatedAt: new Date() } },
      )
      if (res.modifiedCount) count++
    }
    return count
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({ id: { $in: ids } })
      return result.deletedCount ?? 0
    } else {
      const result = await this.collection.updateMany(
        { id: { $in: ids } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount ?? 0
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
