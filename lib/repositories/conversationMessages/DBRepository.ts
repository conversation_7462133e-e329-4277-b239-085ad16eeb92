import {
  ConversationMessage,
  ConversationMessageCreateInput,
  ConversationMessageUpdateInput,
} from "./interface"
import { BaseDbRepository, BaseQueryParams } from "../BaseDBRepository"

export type ConversationMessageDbQueryParams =
  BaseQueryParams<ConversationMessage>

export interface ConversationMessageDBRepository
  extends BaseDbRepository<
    ConversationMessage,
    ConversationMessageCreateInput,
    ConversationMessageUpdateInput,
    ConversationMessageDbQueryParams
  > {}
