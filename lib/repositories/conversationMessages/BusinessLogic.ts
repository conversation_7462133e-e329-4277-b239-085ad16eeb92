import {
  ConversationMessage,
  ConversationMessageCreateInput,
  ConversationMessageUpdateInput,
  ConversationMessageQueryParams,
  ConversationMessageBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { ConversationMessageDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"

export class ConversationMessageBusinessLogic
  implements ConversationMessageBusinessLogicInterface {
  constructor(private readonly db: ConversationMessageDBRepository) { }

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("ConversationMessage ID is required", "INVALID_ID")
  }

  private trimCreateInput(
    data: ConversationMessageCreateInput,
  ): ConversationMessageCreateInput {
    return {
      ...data,
      conversationId: data.conversationId.trim(),
      content: data.content.trim(),
      messageType: data.messageType?.trim(),
      sender: data.sender?.trim(),
    }
  }

  private trimUpdateInput(
    data: ConversationMessageUpdateInput,
  ): ConversationMessageUpdateInput {
    return {
      ...data,
      content: data.content?.trim(),
      messageType: data.messageType?.trim(),
      sender: data.sender?.trim(),
      recipient: data.recipient?.trim(),
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<ConversationMessage | null> {
    this.validateId(id)
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      filters: [{ field: "id", value: id }, ...contextFilters],
      includeDeleted,
    }
    const result = await this.db.getAll(paramsWithContext)
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    conversationId: string,
    params: ConversationMessageQueryParams,
    context: SessionContext,
  ): Promise<{ items: ConversationMessage[]; total: number }> {
    // Add context-based filtering to ensure data isolation
    const contextFilters = this.buildContextFilters(context)
    const paramsWithContext = {
      ...params,
      filters: [
        { field: "conversationId", value: conversationId },
        ...(params.filters || []),
        ...contextFilters],
      sort: [
        // default oldest to newest
        { field: "createdAt", direction: "ASC" },
        ...(params.sort || []),
      ],
    }
    return this.db.getAll(paramsWithContext)
  }

  async create(
    data: ConversationMessageCreateInput,
    context: SessionContext,
  ): Promise<ConversationMessage> {
    const trimmedData = this.trimCreateInput(data)

    // Handle backward compatibility for sender/recipient fields
    const processedData = {
      ...trimmedData,
      sender: trimmedData.sender,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return this.db.create(processedData)
  }

  async update(
    id: string,
    data: ConversationMessageUpdateInput,
    context: SessionContext,
  ): Promise<ConversationMessage | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if message exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Conversation message not found", "NOT_FOUND")
    }

    const trimmedData = {
      ...this.trimUpdateInput(data),
      updatedBy: context.user.id,
    }
    return this.db.update(id, trimmedData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if message exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Conversation message not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id)

    const conversationMessage = await this.db.getById(id, true)
    if (!conversationMessage || !conversationMessage.deletedAt) return false

    const conflict = await this.db.getAll({})
    if (conflict.items.length > 0) return false

    return this.db.restore(id)
  }

  async bulkCreate(
    data: ConversationMessageCreateInput[],
  ): Promise<ConversationMessage[]> {
    throw createError(
      "Bulk create not supported for conversation messages",
      "NOT_SUPPORTED",
    )
  }

  async bulkUpdate(
    updates: { id: string; data: ConversationMessageUpdateInput }[],
  ): Promise<number> {
    throw createError(
      "Bulk update not supported for conversation messages",
      "NOT_SUPPORTED",
    )
  }

  async bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete = false,
  ): Promise<number> {
    throw createError(
      "Bulk delete not supported for conversation messages",
      "NOT_SUPPORTED",
    )
  }
}
