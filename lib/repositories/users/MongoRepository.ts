import {
  User,
  UserCreateInput,
  UserUpdateInput,
  UserQueryParams,
} from "./interface"
import { WithId, Document } from "mongodb"
import { MongoDriver } from "../MongoDriver"
import { UserDBRepository } from "./DBRepository"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { buildMongoQuery } from "../queryBuilder"
import { nanoid } from "nanoid"

function generateId(): string {
  return `user-${nanoid(10)}`
}

function mapMongoDocToUser(doc: WithId<Document> | null): User | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as unknown as User
}

export class MongoUserRepository implements UserDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.USERS)
    this.ensureIndexes()
  }

  private async ensureIndexes() {
    await this.collection.createIndex({ id: 1 }, { unique: true })
  }

  async getById(id: string, includeDeleted = false): Promise<User | null> {
    const query: any = { id }
    if (!includeDeleted) query.deletedAt = { $exists: false }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToUser(doc)
  }

  async getAll(
    params: UserQueryParams,
  ): Promise<{ items: User[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      params,
      ["STRING_FIELD", "STRING_FIELD2", "ARRAY_FIELD"],
    )

    const cursor = this.collection
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)

    const docs = await cursor.toArray()
    const items = docs
      .map(mapMongoDocToUser)
      .filter((i): i is User => i !== null)

    const total = await this.collection.countDocuments(query)
    return { items, total }
  }

  async getCount(params: UserQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery({ query: {}, sort: {} }, params, [
      "STRING_FIELD",
      "STRING_FIELD2",
      "ARRAY_FIELD",
    ])

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: UserCreateInput): Promise<User> {
    const now = new Date()
    const id = generateId()

    const doc = {
      id,
      ...data,
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(doc)
    return doc as User
  }

  async update(id: string, data: UserUpdateInput): Promise<User | null> {
    const existing = await this.collection.findOne({
      id,
      deletedAt: { $exists: false },
    })

    if (!existing) return null

    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() }).filter(
        ([_, value]) => value !== undefined,
      ),
    )

    const updateResult = await this.collection.updateOne(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
    )

    if (updateResult.modifiedCount === 0) return null

    const updated = await this.collection.findOne({ id })
    return updated ? mapMongoDocToUser(updated) : null
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount === 1
    } else {
      const result = await this.collection.updateOne(
        { id },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount === 1
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() },
      },
    )
    return result.modifiedCount === 1
  }

  async bulkCreate(data: UserCreateInput[]): Promise<User[]> {
    const now = new Date()
    const docs = data.map((d) => ({
      id: generateId(),
      ...d,
      createdAt: now,
      updatedAt: now,
    }))

    await this.collection.insertMany(docs)
    return docs as User[]
  }

  async bulkUpdate(
    updates: { id: string; data: UserUpdateInput }[],
  ): Promise<number> {
    let count = 0
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { id },
        { $set: { ...data, updatedAt: new Date() } },
      )
      if (res.modifiedCount) count++
    }
    return count
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({ id: { $in: ids } })
      return result.deletedCount ?? 0
    } else {
      const result = await this.collection.updateMany(
        { id: { $in: ids } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount ?? 0
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }
}
