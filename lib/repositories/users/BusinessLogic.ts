import {
  User,
  UserCreateInput,
  UserUpdateInput,
  UserQueryParams,
  UserBusinessLogicInterface,
} from "./interface"
import { createError } from "@/lib/utils/common"
import { UserDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"

export class UserBusinessLogic implements UserBusinessLogicInterface {
  constructor(private readonly db: UserDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim())
      throw createError("User ID is required", "INVALID_ID")
  }

  private trimCreateInput(data: UserCreateInput): UserCreateInput {
    return {
      ...data,
      email: data.email.trim(),
      name: data.name.trim() ?? "",
      ARRAY_FIELD: data.ARRAY_FIELD?.map((t) => t.trim()) ?? [],
      ARRAY_FIELD2: data.ARRAY_FIELD2?.map((c) => c.trim()) ?? [],
      tags: data.tags?.map((a) => a.trim()) ?? [],
      isActive: data.isActive ?? true,
    }
  }

  private trimUpdateInput(data: UserUpdateInput): UserUpdateInput {
    return {
      ...data,
      email: data.email?.trim(),
      name: data.name.trim(),
      ARRAY_FIELD: data.ARRAY_FIELD?.map((t) => t.trim()),
      ARRAY_FIELD2: data.ARRAY_FIELD2?.map((c) => c.trim()),
      tags: data.tags?.map((a) => a.trim()),
    }
  }

  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<User | null> {
    this.validateId(id)
    return this.db.getById(id, includeDeleted)
  }

  async getAll(
    params: UserQueryParams,
    context: SessionContext,
  ): Promise<{ items: User[]; total: number }> {
    // Optionally validate filters/sort here if needed
    return this.db.getAll(params)
  }

  async create(data: UserCreateInput, context: SessionContext): Promise<User> {
    const trimmedData = this.trimCreateInput(data)

    // Check for duplicate email
    const existing = await this.db.getAll({
      filters: [{ field: "email", value: trimmedData.email }],
    })
    if (existing.items.length > 0) {
      throw createError(
        "User with the same email already exists",
        "DUPLICATE_NAME",
      )
    }

    return this.db.create(trimmedData)
  }

  async update(
    id: string,
    data: UserUpdateInput,
    context: SessionContext,
  ): Promise<User | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    const existingRule = await this.db.getById(id)
    if (!existingRule) return null

    if (data.email && data.email.trim() !== existingRule.email) {
      const duplicates = await this.db.getAll({
        filters: [{ field: "email", value: data.email.trim() }],
      })
      if (duplicates.items.some((r) => r.id !== id)) {
        throw createError(
          "Another User with this email exists",
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = this.trimUpdateInput(data)
    return this.db.update(id, trimmedData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    const existingRule = await this.db.getById(id)
    if (!existingRule) throw createError("User not found", "NOT_FOUND")

    return this.db.delete(id, hardDelete)
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    const user = await this.db.getById(id, true)
    if (!user || !user.deletedAt) return false

    // Check for conflicts by email
    const conflict = await this.db.getAll({
      filters: [{ field: "email", value: user.email }],
    })
    if (conflict.items.length > 0) return false

    return this.db.restore(id)
  }

  async bulkCreate(
    data: UserCreateInput[],
    context: SessionContext,
  ): Promise<User[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_CREATE_DATA",
      )
    }

    for (const entry of data) {
      this.trimCreateInput(entry) // Will throw if invalid
      const existing = await this.db.getAll({
        filters: [{ field: "email", value: entry.email.trim() }],
      })
      if (existing.items.length > 0) {
        throw createError(
          `Duplicate email found: ${entry.email}`,
          "DUPLICATE_NAME",
        )
      }
    }

    const trimmedData = data.map((d) => this.trimCreateInput(d))
    return this.db.bulkCreate(trimmedData)
  }

  async bulkUpdate(
    updates: { id: string; data: UserUpdateInput }[],
    context: SessionContext,
  ): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError(
        "Input must be a non-empty array",
        "INVALID_BULK_UPDATE_DATA",
      )
    }

    for (const { id, data } of updates) {
      this.validateId(id)

      if (!data || Object.keys(data).length === 0) {
        throw createError(
          `No data provided for update of ID ${id}`,
          "INVALID_UPDATE_DATA",
        )
      }

      const existingRule = await this.db.getById(id)
      if (!existingRule)
        throw createError(`Rule with ID ${id} not found`, "NOT_FOUND")

      if (data.email && data.email.trim() !== existingRule.email) {
        const duplicates = await this.db.getAll({
          filters: [{ field: "email", value: data.email.trim() }],
        })
        if (duplicates.items.some((r) => r.id !== id)) {
          throw createError(
            `Duplicate email in update: ${data.email}`,
            "DUPLICATE_NAME",
          )
        }
      }

      this.trimUpdateInput(data) // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.trimUpdateInput(data),
    }))

    return this.db.bulkUpdate(trimmedUpdates)
  }

  async bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete = false,
  ): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError(
        "IDs must be a non-empty array",
        "INVALID_BULK_DELETE_DATA",
      )
    }

    for (const id of ids) {
      this.validateId(id)
      const user = await this.db.getById(id)
      if (!user) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND")
    }

    return this.db.bulkDelete(ids, hardDelete)
  }
}
