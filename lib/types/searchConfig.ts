// ✨ Search configuration types
export interface SortOption {
  value: string
  label: string
  field: string // Backend field name
  type?: "string" | "number" | "date" | "boolean"
}

export interface DateFilterOption {
  value: string
  label: string
  description?: string
}

export interface FilterOption {
  id: string
  name: string
  field: string // Backend field name
  type:
    | "boolean"
    | "select"
    | "multiselect"
    | "range"
    | "text"
    | "number"
    | "date"
  options?: Array<{ value: string }>
}

export interface SearchConfig {
  entity: string
  sortOptions: SortOption[]
  dateFilterOptions: DateFilterOption[]
  filters: FilterOption[]
  defaultSort?: {
    field: string
    direction: "ASC" | "DESC"
  }
  searchableFields: string[] // Fields that can be searched
}

export const transformSearchConfigForDataPage = (config: SearchConfig) => {
  return {
    sortOptions: config.sortOptions.map((option) => ({
      value: option.value,
      label: option.label,
    })),
    dateFilterOptions: config.dateFilterOptions.map((option) => ({
      value: option.value,
      label: option.label,
    })),
    filters: config.filters.map((filter) => ({
      id: filter.id,
      name: filter.name,
    })),
    defaultSort: config.defaultSort,
    searchableFields: config.searchableFields,
  }
}

// ✨ Helper function to build backend query from frontend filters
export const buildBackendQuery = (
  config: SearchConfig,
  frontendParams: {
    search?: string
    selectedFilters?: string[]
    sortField?: string
    sortOrder?: "ASC" | "DESC"
    dateFilter?: string
    customStartDate?: string
    customEndDate?: string
  },
) => {
  const backendQuery: Record<string, any> = {}

  // Handle search
  if (frontendParams.search) {
    backendQuery.search = frontendParams.search
    backendQuery.searchFields = config.searchableFields
  }

  // Handle filters
  if (
    frontendParams.selectedFilters &&
    frontendParams.selectedFilters.length > 0
  ) {
    const filters: Array<{ field: string; value: any; type: string }> = []

    frontendParams.selectedFilters.forEach((filterId) => {
      const filterConfig = config.filters.find((f) => f.id === filterId)
      if (filterConfig) {
        filters.push({
          field: filterConfig.field,
          value: true, // For boolean filters
          type: filterConfig.type,
        })
      }
    })

    if (filters.length > 0) {
      backendQuery.filters = filters
    }
  }

  // Handle sorting
  if (frontendParams.sortField) {
    const sortConfig = config.sortOptions.find(
      (s) => s.value === frontendParams.sortField,
    )
    if (sortConfig) {
      backendQuery.sort = [
        {
          field: sortConfig.field,
          direction: frontendParams.sortOrder || "ASC",
        },
      ]
    }
  } else if (config.defaultSort) {
    backendQuery.sort = [
      {
        field: config.defaultSort.field,
        direction: config.defaultSort.direction,
      },
    ]
  }

  // Handle date filtering
  if (frontendParams.dateFilter) {
    backendQuery.dateFilter = frontendParams.dateFilter
    if (frontendParams.dateFilter === "custom") {
      if (frontendParams.customStartDate) {
        backendQuery.dateFrom = frontendParams.customStartDate
      }
      if (frontendParams.customEndDate) {
        backendQuery.dateTo = frontendParams.customEndDate
      }
    }
  }

  return backendQuery
}
