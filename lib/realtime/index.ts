// lib/realtime/index.ts
import { RealtimeInterface } from "./interface"
import { createPusherRealtime } from "./pusher"
import { createWebSocketRealtime } from "./websocket"
import { createSocketIORealtime } from "./socketio"

// Configuration for different realtime providers
export type RealtimeProvider = "pusher" | "websocket" | "socketio"

// Environment variable to control which provider to use
const REALTIME_PROVIDER =
  (process.env.REALTIME_PROVIDER as RealtimeProvider) || "pusher"

// Provider factory functions
const createProviders = (): Record<
  RealtimeProvider,
  () => RealtimeInterface
> => ({
  pusher: () => createPusherRealtime(),
  websocket: () =>
    createWebSocketRealtime({
      clientUrl: process.env.WEBSOCKET_URL || "ws://localhost:8080",
      serverApiEndpoint:
        process.env.WEBSOCKET_API_ENDPOINT || "/api/v1/websocket/trigger",
    }),
  socketio: () =>
    createSocketIORealtime({
      clientUrl: process.env.SOCKETIO_URL || "http://localhost:3001",
      serverApiEndpoint:
        process.env.SOCKETIO_API_ENDPOINT || "/api/v1/socketio/trigger",
      clientOptions: {
        transports: ["websocket", "polling"],
      },
    }),
})

// Get the configured realtime provider
export const getRealtime = (provider?: RealtimeProvider): RealtimeInterface => {
  const selectedProvider = provider || REALTIME_PROVIDER
  const providers = createProviders()

  if (!providers[selectedProvider]) {
    console.warn(
      `Realtime provider "${selectedProvider}" not found, falling back to pusher`,
    )
    return providers.pusher()
  }

  return providers[selectedProvider]()
}

// Default export - the currently configured provider
export const realtime: RealtimeInterface = getRealtime()

// Re-export types and interfaces
export * from "./interface"
export { createPusherRealtime } from "./pusher"
export { createWebSocketRealtime } from "./websocket"
export { createSocketIORealtime, createSocketIORealtimeAsync } from "./socketio"
