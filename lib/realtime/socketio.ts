// lib/realtime/socketio.ts
import {
  RealtimeInterface,
  RealtimeServer,
  RealtimeClient,
  RealtimeChannel,
  RealtimeConnection,
} from "./interface"

// Socket.IO implementation
class SocketIOChannel implements RealtimeChannel {
  private eventListeners: Map<string, ((data: any) => void)[]> = new Map()
  private isSubscribed = true

  constructor(
    private channelName: string,
    private socket: any, // Socket.IO client socket
    private onUnsubscribe: (channel: string) => void,
  ) {}

  bind(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)

    // Listen to Socket.IO events with channel prefix
    const eventName = `${this.channelName}:${event}`
    this.socket.on(eventName, callback)
  }

  unbind_all(): void {
    // Remove all Socket.IO listeners for this channel
    this.eventListeners.forEach((callbacks, event) => {
      const eventName = `${this.channelName}:${event}`
      this.socket.off(eventName)
    })
    this.eventListeners.clear()
  }

  unsubscribe(): void {
    if (this.isSubscribed) {
      this.isSubscribed = false
      this.unbind_all()
      this.onUnsubscribe(this.channelName)

      // Leave the Socket.IO room
      this.socket.emit("leave-channel", this.channelName)
    }
  }
}

class SocketIOConnection implements RealtimeConnection {
  private connectionListeners: Map<string, (() => void)[]> = new Map()
  public socket_id: string | null = null

  constructor(private socket: any) {
    this.setupConnectionListeners()
  }

  private setupConnectionListeners(): void {
    this.socket.on("connect", () => {
      this.socket_id = this.socket.id
      this._triggerEvent("connected")
    })

    this.socket.on("disconnect", () => {
      this.socket_id = null
      this._triggerEvent("disconnected")
    })

    this.socket.on("connect_error", () => {
      this._triggerEvent("error")
    })
  }

  bind(event: string, callback: () => void): void {
    if (!this.connectionListeners.has(event)) {
      this.connectionListeners.set(event, [])
    }
    this.connectionListeners.get(event)!.push(callback)
  }

  private _triggerEvent(event: string): void {
    if (this.connectionListeners.has(event)) {
      const callbacks = this.connectionListeners.get(event)!
      callbacks.forEach((callback) => callback())
    }
  }
}

class SocketIOClientImpl implements RealtimeClient {
  private socket: any
  private channels: Map<string, SocketIOChannel> = new Map()
  private _connection!: SocketIOConnection

  constructor(
    private url: string,
    private options: any = {},
  ) {
    // Dynamic import to avoid SSR issues
    this.initializeSocket()
  }

  private async initializeSocket(): Promise<void> {
    try {
      // Dynamic import for client-side only
      const { io } = await import("socket.io-client")

      this.socket = io(this.url, {
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        ...this.options,
      })

      this._connection = new SocketIOConnection(this.socket)
    } catch (error) {
      console.error("Failed to initialize Socket.IO client:", error)
      throw error
    }
  }

  subscribe(channelName: string): RealtimeChannel {
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!
    }

    const channel = new SocketIOChannel(channelName, this.socket, (name) =>
      this.channels.delete(name),
    )

    this.channels.set(channelName, channel)

    // Join the Socket.IO room
    if (this.socket?.connected) {
      this.socket.emit("join-channel", channelName)
    } else {
      // Queue subscription for when connection is ready
      this.socket?.on("connect", () => {
        this.socket.emit("join-channel", channelName)
      })
    }

    return channel
  }

  get connection(): RealtimeConnection {
    return this._connection
  }

  disconnect(): void {
    this.channels.forEach((channel) => channel.unsubscribe())
    this.channels.clear()
    this.socket?.disconnect()
  }
}

class SocketIOServerImpl implements RealtimeServer {
  constructor(private apiEndpoint: string) {}

  async trigger(channel: string, event: string, data: any): Promise<void> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "trigger",
          channel,
          event,
          data,
        }),
      })

      if (!response.ok) {
        throw new Error(
          `Failed to trigger Socket.IO event: ${response.statusText}`,
        )
      }
    } catch (error) {
      console.error("Error triggering Socket.IO event:", error)
      throw error
    }
  }
}

class SocketIOImplementation implements RealtimeInterface {
  public server: RealtimeServer
  public client: RealtimeClient

  constructor(options: {
    clientUrl: string
    serverApiEndpoint: string
    clientOptions?: any
  }) {
    this.server = new SocketIOServerImpl(options.serverApiEndpoint)
    this.client = new SocketIOClientImpl(
      options.clientUrl,
      options.clientOptions,
    )
  }
}

// Export the Socket.IO implementation
export const createSocketIORealtimeAsync = async (options: {
  clientUrl: string
  serverApiEndpoint: string
  clientOptions?: any
}): Promise<RealtimeInterface> => {
  const implementation = new SocketIOImplementation(options)
  // Wait for socket initialization
  await new Promise((resolve) => setTimeout(resolve, 100))
  return implementation
}

// Synchronous version (socket will initialize asynchronously)
export const createSocketIORealtime = (options: {
  clientUrl: string
  serverApiEndpoint: string
  clientOptions?: any
}): RealtimeInterface => {
  return new SocketIOImplementation(options)
}
