import { z } from "zod"

// Step Schema
export const AiWorkflowStepSchema = z.object({
  id: z.string().min(1, "Step ID is required"),
  name: z.string().min(1, "Step name is required"),
  status: z.enum(["success", "failure", "in-progress", "skipped"]),
  timestamp: z.string().datetime("Invalid timestamp format"),
  duration: z.number().min(0, "Duration must be a positive number"),
  details: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
})

// Create Schema
export const AiWorkflowExecutionCreateSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
  originalMessage: z.string().min(1, "Original message is required"),
})

export const AiWorkflowStepUpdateSchema = z.object({
  name: z.string().min(1, "Step name is required"),
  status: z.enum(["success", "failure", "in-progress", "skipped"]),
  details: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
})

// Update Schema
export const AiWorkflowExecutionUpdateSchema = z.object({
  customerName: z
    .string()
    .min(2, "Customer name must be at least 2 characters")
    .max(100, "Customer name must be less than 100 characters")
    .trim()
    .optional(),

  originalMessage: z.string().optional(),
  startTime: z.string().datetime("Invalid startTime format").optional(),
  endTime: z.string().datetime("Invalid endTime format").optional(),
  totalDuration: z
    .number()
    .min(0, "Total duration must be a positive number")
    .optional(),
  finalStatus: z.enum(["resolved", "escalated", "failed"]).optional(),
  steps: z.array(AiWorkflowStepSchema).optional(),

  finalResponse: z.string().optional(),
  csAgentId: z.string().optional(),
  feedbackScore: z.number().min(1).max(5).optional(),
  updatedBy: z.string().optional(),
})
