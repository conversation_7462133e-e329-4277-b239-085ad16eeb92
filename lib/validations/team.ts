import { z } from "zod"

export const TeamCreateSchema = z.object({
  STRING_FIELD: z.string().min(1, "Name is required"),
  STRING_FIELD2: z.string().optional(),
  ARRAY_FIELD: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required"),
  ARRAY_FIELD2: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required"),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string().min(1, "Created by is required"),
})

export const TeamUpdateSchema = z.object({
  STRING_FIELD: z.string().min(1, "Name is required").optional(),
  STRING_FIELD2: z.string().optional(),
  ARRAY_FIELD: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required")
    .optional(),
  ARRAY_FIELD2: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required")
    .optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
})

export const TeamIdSchema = z.object({
  id: z.string().min(1, "Team ID is required"),
})

export type TeamCreateInput = z.infer<typeof TeamCreateSchema>
export type TeamUpdateInput = z.infer<typeof TeamUpdateSchema>
export type TeamIdInput = z.infer<typeof TeamIdSchema>
