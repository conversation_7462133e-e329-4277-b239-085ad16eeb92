import { z } from "zod"

export const AiRuleCreateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  conditions: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required"),
  actions: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required"),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  // createdBy: z.string().min(1, "Created by is required"),
})

export const AiRuleUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  description: z.string().optional(),
  conditions: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required")
    .optional(),
  actions: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required")
    .optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  // updatedBy: z.string().min(1, "Updated by is required").optional(),
})

export const AiRuleIdSchema = z.object({
  id: z.string().min(1, "AiRule ID is required"),
})

export type AiRuleCreateInput = z.infer<typeof AiRuleCreateSchema>
export type AiRuleUpdateInput = z.infer<typeof AiRuleUpdateSchema>
export type AiRuleIdInput = z.infer<typeof AiRuleIdSchema>
