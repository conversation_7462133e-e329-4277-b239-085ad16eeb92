import { z } from "zod"

export const RoleCreateSchema = z.object({
  STRING_FIELD: z.string().min(1, "Name is required"),
  STRING_FIELD2: z.string().optional(),
  ARRAY_FIELD: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required"),
  ARRAY_FIELD2: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required"),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string().min(1, "Created by is required"),
})

export const RoleUpdateSchema = z.object({
  STRING_FIELD: z.string().min(1, "Name is required").optional(),
  STRING_FIELD2: z.string().optional(),
  ARRAY_FIELD: z
    .array(z.string().min(1, "Condition cannot be empty"))
    .min(1, "At least one condition is required")
    .optional(),
  ARRAY_FIELD2: z
    .array(z.string().min(1, "Action cannot be empty"))
    .min(1, "At least one action is required")
    .optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
})

export const RoleIdSchema = z.object({
  id: z.string().min(1, "Role ID is required"),
})

export type RoleCreateInput = z.infer<typeof RoleCreateSchema>
export type RoleUpdateInput = z.infer<typeof RoleUpdateSchema>
export type RoleIdInput = z.infer<typeof RoleIdSchema>
