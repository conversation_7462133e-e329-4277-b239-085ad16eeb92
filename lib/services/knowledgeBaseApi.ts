import { BaseAPI } from "./baseApi"

export interface KnowledgeBaseStats {
  score: number // 0–100
  totalEntries: number
  keywords: string[]
  inferredRules: string[]
  suggestedTemplates: string[]
}

export interface KnowledgeBaseEntry {
  id: string
  title: string
  content: string
  keywords: string[]
  category?: string
  tags?: string[]
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  organizationId?: string
}

export interface CreateKnowledgeBasePayload {
  title: string
  content: string
  keywords?: string[]
  category?: string
  tags?: string[]
}

export interface UpdateKnowledgeBasePayload {
  title?: string
  content?: string
  keywords?: string[]
  category?: string
  tags?: string[]
}

export interface Document {
  id: string
  name: string
  size: number
}

export class KnowledgeBaseAPI extends BaseAPI {
  // Get all knowledge base entries
  static GetAllEntries(params?: {
    page?: number
    limit?: number
    search?: string
    category?: string
    tags?: string[]
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append("page", params.page.toString())
    if (params?.limit) searchParams.append("limit", params.limit.toString())
    if (params?.search) searchParams.append("search", params.search)
    if (params?.category) searchParams.append("category", params.category)
    if (params?.tags) searchParams.append("tags", params.tags.join(","))

    const url = `/knowledge-base${searchParams.toString() ? `?${searchParams.toString()}` : ""}`

    return new BaseAPI(url, undefined, "GET", true, {}).build<{
      items: KnowledgeBaseEntry[]
      total: number
      page: number
      limit: number
      totalPages: number
    }>()
  }

  // Get a specific knowledge base entry by ID
  static GetEntryById(id: string) {
    return new BaseAPI(
      `/knowledge-base/${id}`,
      undefined,
      "GET",
      true,
      {},
    ).build<KnowledgeBaseEntry>()
  }

  // Create a new knowledge base entry
  static CreateEntry(payload: CreateKnowledgeBasePayload) {
    return new BaseAPI(
      `/knowledge-base`,
      payload,
      "POST",
      true,
      {},
    ).build<KnowledgeBaseEntry>()
  }

  // Update an existing knowledge base entry
  static UpdateEntry(id: string, payload: UpdateKnowledgeBasePayload) {
    return new BaseAPI(
      `/knowledge-base/${id}`,
      payload,
      "PUT",
      true,
      {},
    ).build<KnowledgeBaseEntry>()
  }

  // Delete a knowledge base entry
  static DeleteEntry(id: string, hardDelete = false) {
    const url = `/knowledge-base/${id}${hardDelete ? "?hardDelete=true" : ""}`
    return new BaseAPI(url, undefined, "DELETE", true, {}).build<{
      deleted: boolean
    }>()
  }

  // Legacy method - now handles the single knowledge base per account
  static UpdateKnowledgeBase(payload: { content: string }) {
    return new BaseAPI(
      `/knowledge-base`,
      {
        title: "Knowledge Base",
        content: payload.content,
        category: "general",
        tags: ["knowledge-base"],
      },
      "POST",
      true,
      {},
    ).build<KnowledgeBaseEntry>()
  }

  static GetScore() {
    return new BaseAPI(
      `/knowledge-base/score`,
      undefined,
      "GET",
      true,
      {},
    ).build<{ score: number; totalEntries: number }>()
  }

  static GetKeywords() {
    return new BaseAPI(
      `/knowledge-base/keywords`,
      undefined,
      "GET",
      true,
      {},
    ).build<{ keywords: string[] }>()
  }

  static GetInferredRules() {
    return new BaseAPI(
      `/knowledge-base/inferred-rules`,
      undefined,
      "GET",
      true,
      {},
    ).build<{ inferredRules: string[] }>()
  }

  static GetSuggestedTemplates() {
    return new BaseAPI(
      `/knowledge-base/suggested-templates`,
      undefined,
      "GET",
      true,
      {},
    ).build<{ suggestedTemplates: string[] }>()
  }

  static GetAllStats() {
    return new BaseAPI(
      `/knowledge-base/stats`,
      undefined,
      "GET",
      true,
      {},
    ).build<KnowledgeBaseStats>()
  }

  // NEW: List uploaded documents
  static ListDocuments() {
    return new BaseAPI(
      `/knowledge-base/documents`,
      undefined,
      "GET",
      true,
      {},
    ).build<{ documents: Document[] }>()
  }

  // NEW: Upload a document (payload: { file: File })
  static UploadDocument(payload: { file: File }) {
    // Note: BaseAPI might need to handle FormData if not done yet
    const formData = new FormData()
    formData.append("file", payload.file)

    return new BaseAPI(
      `/knowledge-base/documents/upload`,
      formData,
      "POST",
      true,
      { "Content-Type": "multipart/form-data" },
      { success: true },
    ).build<{ success: boolean }>()
  }

  // NEW: Remove a document by id
  static RemoveDocument(payload: { id: string }) {
    return new BaseAPI(
      `/knowledge-base/documents/${payload.id}`,
      undefined,
      "DELETE",
      true,
      {},
      { success: true },
    ).build<{ success: boolean }>()
  }
}
