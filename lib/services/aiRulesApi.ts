import { AiRule } from "@/lib/repositories/aiRules/interface"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type AiRulePayload = Partial<AiRule>

export class AiRulesAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/ai-rules${queryString}`).build<
      PaginatedResponse<AiRule>
    >()
  }

  static Detail = (contactId: string) =>
    new BaseAPI(`/ai-rules/${contactId}`).build<AiRule>()

  static Create = (body: AiRulePayload) =>
    new BaseAPI(`/ai-rules`, body, "POST").build<AiRule>()

  static Update = (contactId: string, body: AiRulePayload) =>
    new BaseAPI(`/ai-rules/${contactId}`, body, "PUT").build<AiRule>()

  static Delete = (contactId: string) =>
    new BaseAPI(`/ai-rules/${contactId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreate = (data: AiRulePayload[]) =>
    new BaseAPI(`/ai-rules/bulk`, { operation: "import", data }, "POST").build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkUpdate = (data: Array<{ id: string; [key: string]: any }>) =>
    new BaseAPI(`/ai-rules/bulk`, { operation: "update", data }, "PUT").build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkDelete = (ids: string[]) =>
    new BaseAPI(
      `/ai-rules/bulk`,
      { operation: "delete", ids },
      "DELETE",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ id?: string; message: string }>
    }>()

  static Stats = (params: {
    search?: string
    includeDeleted?: boolean
    filters?: Array<{ field: string; value: any }>
    dateFrom?: string
    dateTo?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""
    return new BaseAPI(`/ai-rules/stats${queryString}`).build<{
      totalAiRules: number
      activeAiRules: number
      deletedAiRules: number
      contactsWithEmail: number
      contactsWithTags: number
      recentAiRules: number
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
      }>
      tagBreakdown: Array<{ tag: string; count: number; percentage: number }>
      createdByBreakdown: Array<{
        createdBy: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; created: number; deleted: number }>
    }>()
  }
}
