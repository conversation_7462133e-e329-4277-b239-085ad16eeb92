import { BaseAPI } from "./baseApi"

export interface UpdateAccountNamePayload {
  name: string
}

export interface AccountInfo {
  id: string
  name: string
  email: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export class AccountAPI extends BaseAPI {
  static GetAccount() {
    return new BaseAPI(`/account`, {}, "GET").build<AccountInfo>()
  }

  static UpdateAccountName(name: string) {
    return new BaseAPI(`/account/update-name`, { name }, "PUT").build<{
      success: boolean
    }>()
  }
}
