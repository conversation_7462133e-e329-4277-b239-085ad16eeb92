import { BaseAPI } from "./baseApi"

export interface SendPresencePayload {
  provider: string
  presence: string
  conversationId?: string
  session?: string
}

export interface SendPresenceResponse {
  success: boolean
  message?: string
  error?: string
}

export class ConversationsPresenceAPI extends BaseAPI {
  static SendPresence = (payload: SendPresencePayload) =>
    new BaseAPI(
      `/functions/conversations/send-presence`,
      payload,
      "POST",
    ).build<SendPresenceResponse>()
}
