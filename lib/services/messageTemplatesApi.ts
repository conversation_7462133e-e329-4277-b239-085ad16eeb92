import { MessageTemplate } from "@/lib/repositories/messageTemplates/interface"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type MessageTemplatePayload = Partial<MessageTemplate>

export class MessageTemplatesAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/message-templates${queryString}`).build<
      PaginatedResponse<MessageTemplate>
    >()
  }

  static Detail = (contactId: string) =>
    new BaseAPI(`/message-templates/${contactId}`).build<MessageTemplate>()

  static Create = (body: MessageTemplatePayload) =>
    new BaseAPI(`/message-templates`, body, "POST").build<MessageTemplate>()

  static Update = (contactId: string, body: MessageTemplatePayload) =>
    new BaseAPI(
      `/message-templates/${contactId}`,
      body,
      "PUT",
    ).build<MessageTemplate>()

  static Delete = (contactId: string) =>
    new BaseAPI(`/message-templates/${contactId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreate = (data: MessageTemplatePayload[]) =>
    new BaseAPI(
      `/message-templates/bulk`,
      { operation: "import", data },
      "POST",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkUpdate = (data: Array<{ id: string; [key: string]: any }>) =>
    new BaseAPI(
      `/message-templates/bulk`,
      { operation: "update", data },
      "PUT",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkDelete = (ids: string[]) =>
    new BaseAPI(
      `/message-templates/bulk`,
      { operation: "delete", ids },
      "DELETE",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ id?: string; message: string }>
    }>()

  static Stats = (params: {
    search?: string
    includeDeleted?: boolean
    filters?: Array<{ field: string; value: any }>
    dateFrom?: string
    dateTo?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""
    return new BaseAPI(`/message-templates/stats${queryString}`).build<{
      totalMessageTemplates: number
      activeMessageTemplates: number
      deletedMessageTemplates: number
      contactsWithEmail: number
      contactsWithTags: number
      recentMessageTemplates: number
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
      }>
      tagBreakdown: Array<{ tag: string; count: number; percentage: number }>
      createdByBreakdown: Array<{
        createdBy: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; created: number; deleted: number }>
    }>()
  }
}
