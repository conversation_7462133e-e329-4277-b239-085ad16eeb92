import { ConversationMessage } from "@/lib/repositories/conversationMessages/interface"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type ConversationMessagePayload = Partial<ConversationMessage>

export class ConversationMessagesAPI extends BaseAPI {
  static All(conversationId: string, params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(
      `/conversations/${conversationId}/messages${queryString}`,
    ).build<PaginatedResponse<ConversationMessage>>()
  }

  static Detail = (messageId: string) =>
    new BaseAPI(`/messages/${messageId}`).build<ConversationMessage>()

  static Create = (conversationId: string, body: ConversationMessagePayload) =>
    new BaseAPI(
      `/conversations/${conversationId}/messages`,
      body,
      "POST",
    ).build<ConversationMessage>()

  static Update = (messageId: string, body: ConversationMessagePayload) =>
    new BaseAPI(
      `/messages/${messageId}`,
      body,
      "PUT",
    ).build<ConversationMessage>()

  static Delete = (messageId: string) =>
    new BaseAPI(`/messages/${messageId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreate = (
    conversationId: string,
    data: ConversationMessagePayload[],
  ) =>
    new BaseAPI(
      `/conversations/${conversationId}/messages/bulk`,
      { operation: "import", data },
      "POST",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkUpdate = (data: Array<{ id: string; [key: string]: any }>) =>
    new BaseAPI(`/messages/bulk`, { operation: "update", data }, "PUT").build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkDelete = (ids: string[]) =>
    new BaseAPI(
      `/messages/bulk`,
      { operation: "delete", ids },
      "DELETE",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ id?: string; message: string }>
    }>()

  static Stats = (
    conversationId?: string,
    params: {
      search?: string
      includeDeleted?: boolean
      filters?: Array<{ field: string; value: any }>
      dateFrom?: string
      dateTo?: string
    },
  ) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""

    const url = conversationId
      ? `/conversations/${conversationId}/messages/stats${queryString}`
      : `/messages/stats${queryString}`

    return new BaseAPI(url).build<{
      totalMessages: number
      activeMessages: number
      deletedMessages: number
      unreadMessages: number
      messagesBySender: Array<{
        sender: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; sent: number; deleted: number }>
    }>()
  }
}
