import { BaseAPI } from "./baseApi"

export interface SendMessagePayload {
  conversationId: string
  text: string
}

export interface SendMessageResponse {
  success: boolean
  result?: any
  error?: string
}

export interface SendTypingPayload {
  conversationId: string
  status: string
}

export interface SendTypingResponse {
  success: boolean
  message?: string
  error?: string
}

export class Messages<PERSON>I extends BaseAPI {
  static SendMessage = (payload: SendMessagePayload) =>
    new BaseAPI(
      `/functions/messages/send`,
      payload,
      "POST",
    ).build<SendMessageResponse>()

  static SendTyping = (payload: SendTypingPayload) =>
    new BaseAPI(
      `/functions/messages/send-typing`,
      payload,
      "POST",
    ).build<SendTypingResponse>()
}
