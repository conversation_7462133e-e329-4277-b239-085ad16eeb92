import { CustomerProfile } from "@/lib/repositories/customerProfiles/interface"
import { BaseAPI } from "./baseApi"

export interface CustomerProfileApiResponse<T = any> {
  status: "success" | "failed"
  data: T
  errors?: string[]
  errorCodes?: string[]
}

export interface CustomerProfileListResponse {
  items: CustomerProfile[]
  page: number
  total: number
}

export class CustomerProfileAPI extends BaseAPI {
  static Detail = (id: string) =>
    new BaseAPI(`/customer-profiles/${id}`).build<CustomerProfile>()
}
