import { Contact } from "@/lib/repositories/contacts/interface"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type ContactPayload = Partial<Contact>

export class ContactsAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/contacts${queryString}`).build<
      PaginatedResponse<Contact>
    >()
  }

  static Detail = (contactId: string) =>
    new BaseAPI(`/contacts/${contactId}`).build<Contact>()

  static Create = (body: ContactPayload) =>
    new BaseAPI(`/contacts`, body, "POST").build<Contact>()

  static Update = (contactId: string, body: ContactPayload) =>
    new BaseAPI(`/contacts/${contactId}`, body, "PUT").build<Contact>()

  static Delete = (contactId: string) =>
    new BaseAPI(`/contacts/${contactId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreate = (data: ContactPayload[]) =>
    new BaseAPI(`/contacts/bulk`, { operation: "import", data }, "POST").build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkUpdate = (data: Array<{ id: string; [key: string]: any }>) =>
    new BaseAPI(`/contacts/bulk`, { operation: "update", data }, "PUT").build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ row?: number; field?: string; message: string }>
    }>()

  static BulkDelete = (ids: string[]) =>
    new BaseAPI(
      `/contacts/bulk`,
      { operation: "delete", ids },
      "DELETE",
    ).build<{
      total: number
      successful: number
      failed: number
      errors: Array<{ id?: string; message: string }>
    }>()

  static Stats = (params: {
    search?: string
    includeDeleted?: boolean
    filters?: Array<{ field: string; value: any }>
    dateFrom?: string
    dateTo?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""
    return new BaseAPI(`/contacts/stats${queryString}`).build<{
      totalContacts: number
      activeContacts: number
      deletedContacts: number
      contactsWithEmail: number
      contactsWithTags: number
      recentContacts: number
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
      }>
      tagBreakdown: Array<{ tag: string; count: number; percentage: number }>
      createdByBreakdown: Array<{
        createdBy: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; created: number; deleted: number }>
    }>()
  }
}
