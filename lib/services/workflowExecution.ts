import { AiWorkflowExecution } from "../repositories/aiWorkflowExecutions"
import { BaseAPI } from "./baseApi"
import { PaginatedResponse, PagingAndSearch } from "./types"

type AiWorkflowExecutionPayload = Partial<AiWorkflowExecution>

export class AiWorkflowExecutionsAPI extends BaseAPI {
  static All(params: PagingAndSearch<{}>) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/ai-workflow-executions${queryString}`).build<
      PaginatedResponse<AiWorkflowExecution>
    >()
  }

  static Detail = (workflowExecutionId: string) =>
    new BaseAPI(
      `/ai-workflow-executions/${workflowExecutionId}`,
    ).build<AiWorkflowExecution>()

  static Create = (body: AiWorkflowExecutionPayload) =>
    new BaseAPI(
      `/ai-workflow-executions`,
      body,
      "POST",
    ).build<AiWorkflowExecution>()

  static Update = (
    workflowExecutionId: string,
    body: AiWorkflowExecutionPayload,
  ) =>
    new BaseAPI(
      `/ai-workflow-executions/${workflowExecutionId}`,
      body,
      "PUT",
    ).build<AiWorkflowExecution>()

  static Delete = (workflowExecutionId: string) =>
    new BaseAPI(
      `/ai-workflow-executions/${workflowExecutionId}`,
      undefined,
      "DELETE",
    ).build<{
      success: boolean
    }>()

  static Stats = (params: {
    search?: string
    includeDeleted?: boolean
    filters?: Array<{ field: string; value: any }>
    dateFrom?: string
    dateTo?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.search) queryParams.append("search", params.search)
    if (params?.includeDeleted) queryParams.append("includeDeleted", "true")
    if (params?.filters)
      queryParams.append("filters", JSON.stringify(params.filters))
    if (params?.dateFrom) queryParams.append("dateFrom", params.dateFrom)
    if (params?.dateTo) queryParams.append("dateTo", params.dateTo)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""
    return new BaseAPI(`/ai-workflow-executions/stats${queryString}`).build<{
      totalAiWorkflowExecutions: number
      activeAiWorkflowExecutions: number
      deletedAiWorkflowExecutions: number
      workflowExecutionsWithEmail: number
      workflowExecutionsWithTags: number
      recentAiWorkflowExecutions: number
      statusBreakdown: Array<{
        status: string
        count: number
        percentage: number
      }>
      tagBreakdown: Array<{ tag: string; count: number; percentage: number }>
      createdByBreakdown: Array<{
        createdBy: string
        count: number
        percentage: number
      }>
      dailyStats: Array<{ date: string; created: number; deleted: number }>
    }>()
  }
}
