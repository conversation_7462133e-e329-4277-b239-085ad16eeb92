import axios, { AxiosRequestConfig, AxiosResponse } from "axios"
import { BaseAPI } from "./baseApi"
import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseWrapper } from "../types/responseWrapper"

const BASE_URL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1`

// 🔁 Convert ISO strings to Date objects
function jsonDateReviver(key: string, value: any) {
  if (
    typeof value === "string" &&
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*Z$/.test(value)
  ) {
    const parsed = new Date(value)
    if (!isNaN(parsed.getTime())) return parsed
  }
  return value
}

interface Functions {
  refreshToken: () => Promise<void>
  registerRefreshTokenFunction(fn: () => Promise<void>): void
}

class FunctionsImplementation implements Functions {
  private refreshTokenFn: () => Promise<void> = async () => {
    throw new Error("Refresh token function not registered")
  }

  private refreshInProgressPromise: Promise<void> | null = null

  async refreshToken(): Promise<void> {
    if (this.refreshInProgressPromise) {
      // ✅ Someone else is refreshing — just wait
      return this.refreshInProgressPromise
    }

    // ✅ You're the first — initiate refresh
    this.refreshInProgressPromise = this.refreshTokenFn()
      .catch((err) => {
        // Optional: handle/log refresh failure
        throw err
      })
      .finally(() => {
        this.refreshInProgressPromise = null
      })

    return this.refreshInProgressPromise
  }

  registerRefreshTokenFunction(fn: () => Promise<void>): void {
    this.refreshTokenFn = fn
  }
}

export class ApiService {
  private static instance: ApiService

  private constructor(private functions: Functions) {}

  // ⚠️ Only use this if you need to call instance.
  // If you want to call API. Use API.request function.
  static __getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService(new FunctionsImplementation())
    }

    return ApiService.instance
  }

  static getFunctions(): Functions {
    return this.__getInstance().functions
  }

  // ⚠️ Deprecated method
  async __request<T>(api: BaseAPI, retry = true): Promise<T> {
    if (api.devMock) {
      return api.devMock
    }

    try {
      const response: AxiosResponse = await axios(
        this.buildAxiosRequestConfig(api),
      )

      const rawData = JSON.stringify(response.data)
      const responseBody: ResponseWrapper<T> = JSON.parse(
        rawData,
        jsonDateReviver,
      )

      if (
        responseBody.status === "success" &&
        responseBody.data !== undefined
      ) {
        return responseBody.data
      }

      if (
        (retry &&
          responseBody.errorCodes?.includes(ERROR_CODES.INVALID_TOKEN)) ||
        responseBody.errorCodes?.includes(ERROR_CODES.UNAUTHORIZED)
      ) {
        await this.functions.refreshToken()
        return this.__request<T>(api, false) // retry once
      }

      const firstError = responseBody.error?.[0] || "Unknown error"
      throw new Error(firstError)
    } catch (error) {
      throw new Error(this.getErrorString(error, "Error in API request"))
    }
  }

  private buildAxiosRequestConfig(api: BaseAPI): AxiosRequestConfig {
    return {
      method: api.method,
      url: `${BASE_URL}${api.url}`,
      data: api.getRequestBody(),
      headers: api.headers(),
      validateStatus: () => true,
    }
  }

  private getErrorString(error: any, defaultError: string): string {
    if (axios.isAxiosError(error)) {
      const err = error.response?.data?.error
      if (Array.isArray(err) && err.length > 0) {
        return err[0]
      }
    }
    return defaultError
  }
}
