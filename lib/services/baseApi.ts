import { StorageKeys, secureStorage } from "@/lib/utils/SecureStorage"
import { ApiService } from "./apiService"

export class BaseAPI {
  constructor(
    public url: string,
    public body?: any,
    public method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH" = "GET",
    public authorization = true,
    public customHeaders: Record<string, string> = {},
    public devMock?: any,
  ) {}

  headers(): Record<string, string> {
    const organizationStr = secureStorage.getItem(
      StorageKeys.CurrentOrganization,
    )

    let organizationID = ""
    if (organizationStr) {
      try {
        const organization = JSON.parse(organizationStr)
        organizationID = organization?.id ?? ""
      } catch (e) {
        console.warn("Invalid organization data in secure storage")
      }
    }

    return {
      "Content-Type": "application/json",
      "X-Organization-ID": organizationID,
      ...this.customHeaders,
    }
  }

  getRequestBody(): string | FormData | undefined {
    if (this.body instanceof FormData) return this.body
    if (typeof this.body === "string") return this.body
    if (this.body) return JSON.stringify(this.body)
    return undefined
  }

  build<Response>(): TypedResponseBaseAPI<Response> {
    return new TypedResponseBaseAPI<Response>(this)
  }

  /**
   * @deprecated Use `API.build() in the API then use the TypedResponseBaseAPI` instead.
   */
  async request<T>() {
    return await ApiService.__getInstance().__request<T>(this)
  }
}
export class TypedResponseBaseAPI<Response> {
  constructor(private api: BaseAPI) {}

  async request(): Promise<Response> {
    return await ApiService.__getInstance().__request<Response>(this.api)
  }
}
