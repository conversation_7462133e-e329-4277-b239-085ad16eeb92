# Realtime Communication System

This system provides a provider-agnostic interface for realtime communication, allowing you to easily switch between different providers (Pusher, WebSocket, etc.) without changing your application code.

## Architecture

- **`realtime.ts`** - Core interfaces and types
- **`pusher.ts`** - Pusher implementation
- **`websocket.ts`** - WebSocket implementation
- **`realtime-config.ts`** - Configuration and provider switching
- **`websocket-server.ts`** - Example WebSocket server implementation

## Usage

### Basic Usage (Recommended)

```typescript
import { realtime } from "@/lib/realtime-config"

// Server-side (API routes)
await realtime.server.trigger("conversation-room-channel", "new-message", {
  from: "user123",
  message: "Hello world!",
})

// Client-side (React components)
const channel = realtime.client.subscribe("conversation-room-channel")
channel.bind("new-message", (data) => {
  console.log("New message:", data)
})

// Cleanup
channel.unbind_all()
channel.unsubscribe()
```

### Legacy Usage (Still Works)

```typescript
import { pusher, pusherClient } from "@/lib/pusher"

// Your existing code continues to work unchanged
await pusher.trigger("conversation-room-channel", "new-message", data)
const channel = pusherClient.subscribe("conversation-room-channel")
```

## Switching Providers

### Environment Variable

Set the `REALTIME_PROVIDER` environment variable:

```bash
# Use Pusher (default)
REALTIME_PROVIDER=pusher

# Use WebSocket
REALTIME_PROVIDER=websocket

# Use Socket.IO
REALTIME_PROVIDER=socketio
```

### Programmatic Switching

```typescript
import { getRealtime } from "@/lib/realtime"

// Use specific provider
const pusherRealtime = getRealtime("pusher")
const websocketRealtime = getRealtime("websocket")
const socketiorealtime = getRealtime("socketio")
```

## WebSocket Setup

### Environment Variables

```bash
# WebSocket client URL
WEBSOCKET_URL=ws://localhost:8080

# WebSocket server API endpoint
WEBSOCKET_API_ENDPOINT=/api/websocket/trigger

# WebSocket server port
WEBSOCKET_PORT=8080
```

### Starting WebSocket Server

```typescript
import { getWebSocketServer } from "@/lib/websocket-server"

// Start the WebSocket server (typically in a separate process)
const wsServer = getWebSocketServer()
```

## Socket.IO Setup

### Environment Variables

```bash
# Socket.IO client URL
SOCKETIO_URL=http://localhost:3001

# Socket.IO server API endpoint
SOCKETIO_API_ENDPOINT=/api/socketio/trigger

# Socket.IO server port
SOCKETIO_PORT=3001

# Socket.IO CORS origin
SOCKETIO_CORS_ORIGIN=*
```

### Starting Socket.IO Server

```typescript
import { getSocketIOServer } from "@/lib/socketio-server"

// Start the Socket.IO server (typically in a separate process)
const socketIOServer = getSocketIOServer({
  port: 3001,
  corsOptions: {
    origin: "*",
    methods: ["GET", "POST"],
  },
})
```

### WebSocket Server Integration

The WebSocket implementation requires:

1. **WebSocket Server** - Handles client connections and subscriptions
2. **HTTP API Endpoint** - Receives trigger requests from server-side code
3. **Message Broadcasting** - Forwards messages from API to connected clients

## Migration Guide

### From Direct Pusher Usage

**Before:**

```typescript
import { pusher } from "@/lib/pusher"
await pusher.trigger("channel", "event", data)
```

**After:**

```typescript
import { realtime } from "@/lib/realtime-config"
await realtime.server.trigger("channel", "event", data)
```

### From Direct PusherClient Usage

**Before:**

```typescript
import { pusherClient } from "@/lib/pusher"
const channel = pusherClient.subscribe("channel")
```

**After:**

```typescript
import { realtime } from "@/lib/realtime-config"
const channel = realtime.client.subscribe("channel")
```

## Creating Custom Providers

Implement the `RealtimeInterface`:

```typescript
import {
  RealtimeInterface,
  RealtimeServer,
  RealtimeClient,
} from "@/lib/realtime"

class MyCustomImplementation implements RealtimeInterface {
  public server: RealtimeServer
  public client: RealtimeClient

  constructor() {
    this.server = new MyCustomServer()
    this.client = new MyCustomClient()
  }
}
```

## Features

### Pusher Implementation

- ✅ Full Pusher compatibility
- ✅ Server-side triggering
- ✅ Client-side subscriptions
- ✅ Connection management
- ✅ Socket ID access

### WebSocket Implementation

- ✅ Native WebSocket support
- ✅ Automatic reconnection
- ✅ Channel subscriptions
- ✅ Event binding/unbinding
- ✅ Connection state management
- ✅ Server-side broadcasting

### Socket.IO Implementation

- ✅ Socket.IO v4+ support
- ✅ Automatic reconnection with exponential backoff
- ✅ Room-based channel subscriptions
- ✅ Event binding/unbinding with namespacing
- ✅ Connection state management
- ✅ Server-side broadcasting to rooms
- ✅ Fallback transports (WebSocket → Polling)
- ✅ Built-in error handling

## Benefits

1. **Provider Agnostic** - Switch between providers without code changes
2. **Type Safety** - Full TypeScript support
3. **Backward Compatible** - Existing code continues to work
4. **Extensible** - Easy to add new providers
5. **Consistent API** - Same interface across all providers
6. **Production Ready** - Includes error handling and reconnection logic
