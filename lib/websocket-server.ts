// lib/websocket-server.ts
// Example WebSocket server implementation
// This would typically run as a separate service or integrated into your Next.js app

import { WebSocketServer } from "ws"

interface WebSocketMessage {
  type: "subscribe" | "unsubscribe" | "ping"
  channel?: string
  event?: string
  data?: any
}

class RealtimeWebSocketServer {
  private wss: WebSocketServer
  private channels: Map<string, Set<any>> = new Map()
  private connectionChannels: Map<any, Set<string>> = new Map()

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port })
    this.setupServer()
    console.log(`WebSocket server running on port ${port}`)
  }

  private setupServer(): void {
    this.wss.on("connection", (ws) => {
      console.log("New WebSocket connection")
      this.connectionChannels.set(ws, new Set())

      ws.on("message", (data) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString())
          this.handleMessage(ws, message)
        } catch (error) {
          console.error("Error parsing WebSocket message:", error)
        }
      })

      ws.on("close", () => {
        console.log("WebSocket connection closed")
        this.handleDisconnection(ws)
      })

      ws.on("error", (error) => {
        console.error("WebSocket error:", error)
      })

      // Send connection confirmation
      ws.send(
        JSON.stringify({
          type: "connected",
          socket_id: this.generateSocketId(),
        }),
      )
    })
  }

  private handleMessage(ws: any, message: WebSocketMessage): void {
    switch (message.type) {
      case "subscribe":
        if (message.channel) {
          this.subscribeToChannel(ws, message.channel)
        }
        break

      case "unsubscribe":
        if (message.channel) {
          this.unsubscribeFromChannel(ws, message.channel)
        }
        break

      case "ping":
        ws.send(JSON.stringify({ type: "pong" }))
        break

      default:
        console.log("Unknown message type:", message.type)
    }
  }

  private subscribeToChannel(ws: any, channel: string): void {
    // Add connection to channel
    if (!this.channels.has(channel)) {
      this.channels.set(channel, new Set())
    }
    this.channels.get(channel)!.add(ws)

    // Track channels for this connection
    this.connectionChannels.get(ws)!.add(channel)

    console.log(`Connection subscribed to channel: ${channel}`)
  }

  private unsubscribeFromChannel(ws: any, channel: string): void {
    // Remove connection from channel
    const channelConnections = this.channels.get(channel)
    if (channelConnections) {
      channelConnections.delete(ws)
      if (channelConnections.size === 0) {
        this.channels.delete(channel)
      }
    }

    // Remove channel from connection tracking
    this.connectionChannels.get(ws)?.delete(channel)

    console.log(`Connection unsubscribed from channel: ${channel}`)
  }

  private handleDisconnection(ws: any): void {
    // Remove connection from all channels
    const connectionChannels = this.connectionChannels.get(ws)
    if (connectionChannels) {
      connectionChannels.forEach((channel) => {
        this.unsubscribeFromChannel(ws, channel)
      })
      this.connectionChannels.delete(ws)
    }
  }

  private generateSocketId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  // Public method to broadcast messages (called from API routes)
  public broadcast(channel: string, event: string, data: any): void {
    const channelConnections = this.channels.get(channel)
    if (channelConnections) {
      const message = JSON.stringify({
        type: "event",
        channel,
        event,
        data,
      })

      channelConnections.forEach((ws) => {
        if (ws.readyState === 1) {
          // WebSocket.OPEN
          ws.send(message)
        }
      })

      console.log(
        `Broadcasted event "${event}" to ${channelConnections.size} connections on channel "${channel}"`,
      )
    }
  }

  public getChannelStats(): Record<string, number> {
    const stats: Record<string, number> = {}
    this.channels.forEach((connections, channel) => {
      stats[channel] = connections.size
    })
    return stats
  }
}

// Singleton instance
let wsServer: RealtimeWebSocketServer | null = null

export const getWebSocketServer = (): RealtimeWebSocketServer => {
  if (!wsServer) {
    const port = parseInt(process.env.WEBSOCKET_PORT || "8080")
    wsServer = new RealtimeWebSocketServer(port)
  }
  return wsServer
}

// Export for use in API routes
export { RealtimeWebSocketServer }
