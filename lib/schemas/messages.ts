import { z } from "zod"

export const SendMessageSchema = z.object({
  conversationId: z.string().min(1, "Conversation ID is required"),
  text: z.string().min(1, "Message text is required"),
})

export const ReceiveMessagesSchema = z.object({
  provider: z.string().optional(),
  conversationId: z.string().min(1, "Conversation ID is required"),
  limit: z.string().optional().default("20"),
  offset: z.string().optional().default("0"),
})

export const ReadMessageSchema = z.object({
  conversationId: z.string().min(1, "Conversation ID is required"),
  session: z.string().optional(),
  provider: z.string().optional(),
})

export const SendTypingSchema = z.object({
  status: z.string().min(1, "Status is required"),
  conversationId: z.string().min(1, "Conversation ID is required"),
})

export type SendMessageInput = z.infer<typeof SendMessageSchema>
export type ReceiveMessagesInput = z.infer<typeof ReceiveMessagesSchema>
export type ReadMessageInput = z.infer<typeof ReadMessageSchema>
export type SendTypingInput = z.infer<typeof SendTypingSchema>
