import { z } from "zod"

export const LoginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

export const RegisterSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  name: z.string().optional(),
})

export const RefreshTokenSchema = z.object({
  token: z.string().min(1, "Token is required"),
  refresh_token: z.string().min(1, "Refresh token is required"),
})

export const UserProfileUpdateSchema = z.object({
  name: z.string().optional(),
  email: z.string().email("Invalid email format").optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
})

export const ChangePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z
      .string()
      .min(6, "New password must be at least 6 characters"),
  })
  .refine((data) => data.newPassword !== data.currentPassword, {
    message: "New password must be different from current password",
    path: ["newPassword"],
  })

export const ForgotPasswordSchema = z.object({
  email: z.string().email("Invalid email format"),
})

export const ResetPasswordSchema = z
  .object({
    token: z.string().min(1, "Reset token is required"),
    newPassword: z
      .string()
      .min(6, "New password must be at least 6 characters"),
  })

export const VerifyEmailSchema = z.object({
  token: z.string().min(1, "Verification token is required"),
})

export const ResendVerificationSchema = z.object({
  email: z.string().email("Invalid email format"),
})

export const DeleteAccountSchema = z.object({
  password: z.string().min(1, "Password is required for account deletion"),
  confirmDeletion: z.literal("DELETE", {
    errorMap: () => ({
      message: "Please type 'DELETE' to confirm account deletion",
    }),
  }),
})

export const LogoutSchema = z.object({
  token: z.string().min(1, "Token is required"),
})

export type LoginInput = z.infer<typeof LoginSchema>
export type RegisterInput = z.infer<typeof RegisterSchema>
export type RefreshTokenInput = z.infer<typeof RefreshTokenSchema>
export type UserProfileUpdateInput = z.infer<typeof UserProfileUpdateSchema>
export type ChangePasswordInput = z.infer<typeof ChangePasswordSchema>
export type ForgotPasswordInput = z.infer<typeof ForgotPasswordSchema>
export type ResetPasswordInput = z.infer<typeof ResetPasswordSchema>
export type VerifyEmailInput = z.infer<typeof VerifyEmailSchema>
export type ResendVerificationInput = z.infer<typeof ResendVerificationSchema>
export type DeleteAccountInput = z.infer<typeof DeleteAccountSchema>
export type LogoutInput = z.infer<typeof LogoutSchema>
