Feature: Delete Contact

  @background
  Background: Logged in user with existing contact
    Given the user is logged in
    And a contact named "<PERSON>" exists with phone "1234567890"

  Scenario: Delete an existing contact from the contact list
    Given the user is on the contact page
    When the user clicks the "Delete" button for contact "<PERSON>"
    Then the contact "<PERSON>" is removed from the list
    And a success message is displayed
