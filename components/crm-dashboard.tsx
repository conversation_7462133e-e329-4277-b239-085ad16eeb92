"use client"

import { useEffect, useState } from "react"
import { ConversationList } from "@/components/conversation-list"
import { ConversationInterface } from "@/components/conversation-interface"
import { CustomerProfile } from "@/components/customer-profile"
import { AgentsSidebar } from "@/components/agents-sidebar"
import { TemplatesPanel } from "@/components/templates-panel"
import { twMerge } from "tailwind-merge"
import { useSendPresence } from "@/hooks/useSendPresence"
import { Conversation } from "@/lib/repositories/conversations"
import { SessionContext } from "@/lib/repositories/auth/types"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "@/components/crm-dashboard/locales"

export function CRMDashboard(props: { context: SessionContext }) {
  const { t } = useLocalization("crm-dashboard", locales)
  const { sendPresence } = useSendPresence()
  const [messageInput, setMessageInput] = useState("")

  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null)
  const [showTemplates, setShowTemplates] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [isProfileCollapsed, setIsProfileCollapsed] = useState(false)

  // Handle conversation updates (for both list and interface)
  const handleConversationUpdated = (updatedConversation: Conversation) => {
    setSelectedConversation(updatedConversation)
  }

  // Presence handling (disabled for now, uncomment if needed)
  useEffect(() => {
    // sendPresence({ presence: "online" })
    // const handleBeforeUnload = () => {
    //   navigator.sendBeacon("/api/v1/functions/conversations/send-presence", JSON.stringify({
    //     presence: "offline",
    //   }))
    // }
    // window.addEventListener("beforeunload", handleBeforeUnload)
    // return () => window.removeEventListener("beforeunload", handleBeforeUnload)
  }, [])

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      {/* Sidebar: Conversation list */}
      <div className="flex-shrink-0">
        <ConversationList
          selectedConversation={selectedConversation}
          onSelectConversation={setSelectedConversation}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
          onConversationUpdated={handleConversationUpdated}
        />
      </div>

      {/* Main conversation interface */}
      <div className="flex-1 min-w-0 flex flex-col border-l border-r">
        {selectedConversation && (
          <ConversationInterface
            key={selectedConversation.id}
            conversation={selectedConversation}
            messageInput={messageInput}
            setMessageInput={setMessageInput}
            onToggleTemplates={() => setShowTemplates((prev) => !prev)}
            showTemplates={showTemplates}
            isProfileCollapsed={isProfileCollapsed}
            currentUserId={props.context.user.id}
            onConversationUpdated={handleConversationUpdated}
          />
        )}
      </div>

      {/* Right sidebar: Customer profile and agents */}
      {/* <div
        className={twMerge(
          "flex-shrink-0 bg-muted/30 flex flex-col overflow-hidden transition-all duration-300 ease-in-out",
          isProfileCollapsed ? "w-[56px]" : "w-80",
        )}
      >
        <CustomerProfile
          conversationId={selectedConversation?.id || ""}
          customerProfileId={selectedConversation?.customerProfileId}
          isCollapsed={isProfileCollapsed}
          onToggle={() => setIsProfileCollapsed((prev) => !prev)}
        />
        <AgentsSidebar conversationId={selectedConversation?.id || ""} />
      </div> */}

      {/* Templates panel */}
      {showTemplates && (
        <div className="right-80 top-0 bottom-0 z-10 shadow-lg">
          <TemplatesPanel onClose={() => setShowTemplates(false)}
            onUseTemplate={(template) => {
              setMessageInput(template)
            }}
          />
        </div>
      )}
    </div>
  )
}
