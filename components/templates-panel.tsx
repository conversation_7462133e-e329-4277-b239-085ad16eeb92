"use client"

import { useState, useEffect } from "react"
import { <PERSON>, X, Co<PERSON>, <PERSON>, <PERSON>, Tag, Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { componentLocales } from "./locales"
import { useLocalization } from "@/localization/functions/client"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"
import { MessageTemplate } from "@/lib/repositories/messageTemplates/interface"
import { toast } from "sonner"

// We'll load templates from API instead of hardcoded data

interface TemplatesPanelProps {
  onClose: () => void
  onUseTemplate?: (template: string) => void
}

export function TemplatesPanel({ onClose, onUseTemplate }: TemplatesPanelProps) {
  const { t } = useLocalization("templatesPanel", componentLocales)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [templates, setTemplates] = useState<MessageTemplate[]>([])
  const [categories, setCategories] = useState<string[]>(["All"])
  const [loading, setLoading] = useState(true)
  const [copiedId, setCopiedId] = useState<string | null>(null)

  // Load templates from API
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true)
        const response = await MessageTemplatesAPI.All({
          page: 1,
          per_page: 100,
          search: "",
          sort: [],
          filters: []
        }).request()

        if (response && response.items) {
          setTemplates(response.items)

          // Extract unique categories
          const uniqueCategories = ["All", ...new Set(
            response.items
              .map((template: MessageTemplate) => template.category)
              .filter(Boolean)
          )] as string[]
          setCategories(uniqueCategories)
        }
      } catch (error) {
        console.error("Failed to load templates:", error)
        toast.error("Failed to load message templates")
      } finally {
        setLoading(false)
      }
    }

    loadTemplates()
  }, [])

  // Copy to clipboard function
  const copyToClipboard = async (text: string, templateId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedId(templateId)
      toast.success("Template copied to clipboard!")
      setTimeout(() => setCopiedId(null), 2000)
    } catch (error) {
      console.error("Failed to copy:", error)
      toast.error("Failed to copy template")
    }
  }

  // Use template function
  const useTemplate = (template: MessageTemplate) => {
    if (onUseTemplate) {
      onUseTemplate(template.template)
      toast.success("Template applied!")
    }
  }

  // Handle use template (for existing button)
  const handleUseTemplate = (template: MessageTemplate) => {
    useTemplate(template)
  }

  // Toggle favorite (placeholder function)
  const toggleFavorite = (templateId: string) => {
    // TODO: Implement favorite toggle API call
    toast.info("Favorite feature coming soon!")
  }

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.template.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.tags && template.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      ))

    const matchesCategory =
      selectedCategory === "All" || template.category === selectedCategory

    return matchesSearch && matchesCategory
  })



  return (
    <div className="w-80 bg-background flex flex-col h-full shadow-xl border-l">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Tag className="h-5 w-5" />
            {t("messageTemplates")}
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="text-xs"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Templates List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {loading ? (
          <div className="text-center text-muted-foreground py-8">
            <Tag className="h-8 w-8 mx-auto mb-2 opacity-50 animate-pulse" />
            <p>Loading templates...</p>
          </div>
        ) : filteredTemplates.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Tag className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>{t("templatesNotFound")}</p>
            <p className="text-sm">{t("tryAdjusting")}</p>
          </div>
        ) : (
          filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className="hover:shadow-sm transition-shadow"
            >
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-sm font-medium leading-tight">
                    {template.title}
                  </CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFavorite(template.id)}
                          className="h-6 w-6 p-0"
                        >
                          <Star
                            className="h-3 w-3 text-muted-foreground"
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        Add to favorites
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {template.category}
                  </Badge>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {template.createdAt ? new Date(template.createdAt).toLocaleDateString() : 'Recently'}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                  {template.template}
                </p>

                {template.tags && template.tags.length > 0 && (
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-1">
                      {template.tags.slice(0, 2).map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs px-1.5 py-0"
                        >
                          {tag}
                        </Badge>
                      ))}
                      {template.tags.length > 2 && (
                        <Badge
                          variant="secondary"
                          className="text-xs px-1.5 py-0"
                        >
                          +{template.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleUseTemplate(template)}
                  >
                    Use Template
                  </Button>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="px-2"
                          onClick={() => copyToClipboard(template.template, template.id)}
                        >
                          {copiedId === template.id ? (
                            <Check className="h-3 w-3 text-green-600" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {copiedId === template.id ? "Copied!" : "Copy to clipboard"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t bg-muted/30">
        <div className="text-xs text-muted-foreground text-center">
          {filteredTemplates.length} {t("template")}
          {filteredTemplates.length !== 1 ? "s" : ""} found
        </div>
      </div>
    </div >
  )
}
