import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Info, Edit2, XCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import clsx from "clsx"
import { Conversation } from "@/lib/repositories/conversations"

interface ConversationListItemProps {
  conversation: Conversation
  isSelected: boolean
  isCompact: boolean
  onSelect: (props: Conversation) => void
  onEdit?: (conversation: Conversation) => void
}

export function ConversationListItem({
  conversation,
  isSelected,
  isCompact,
  onSelect,
  onEdit,
}: ConversationListItemProps) {
  // Use actual tags from conversation object
  const tags = conversation.tags || []
  const isClosed = conversation.status === "CLOSED"
  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "typing":
        return "bg-green-500"
      case "pending":
        return "bg-yellow-500"
      case "resolved":
      default:
        return "bg-gray-500"
    }
  }
  return (
    <div
      onClick={() => onSelect(conversation)}
      className={`group w-full text-left rounded-md transition ${isSelected ? "bg-muted" : "hover:bg-accent"
        } ${isCompact ? "p-3" : "p-3"} ${isClosed ? "opacity-75" : ""
        }`}
    >
      <div className="flex items-center gap-3 w-full">
        <div className="relative">
          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-4 w-4" />
          </div>
          <div
            className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(
              conversation.status_presence,
            )}`}
          />
        </div>

        {!isCompact && (
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <span className="font-medium text-sm truncate">
                  {conversation.name || "You"}
                </span>
                {conversation.status === "CLOSED" && (
                  <Badge
                    variant="secondary"
                    className="text-xs px-1.5 py-0.5 bg-red-50 text-red-700 border-red-200"
                  >
                    <XCircle className="h-3 w-3 mr-1" />
                    Closed
                  </Badge>
                )}
                {onEdit && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onEdit(conversation)
                    }}
                    className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Edit conversation"
                  >
                    <Edit2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-1">
                {conversation.lastMessage && (
                  <span className="text-xs text-muted-foreground">
                    {format(
                      new Date(
                        conversation.lastMessage._data?.messageTimestamp ||
                        conversation.lastMessageAt ||
                        conversation.updatedAt
                      ),
                      "HH:mm",
                    )}
                  </span>
                )}
                {!conversation.lastMessage?.fromMe &&
                  conversation.lastMessage?.ack == 2 && (
                    <Badge
                      variant="destructive"
                      className="text-xs px-1.5 py-1.5"
                    />
                  )}
              </div>
            </div>
            {conversation.status_presence == "typing" ? (
              <p className="text-xs text-muted-foreground mb-2 italic">
                mengetik...
              </p>
            ) : (
              <p className="text-xs text-muted-foreground truncate mb-2">
                {conversation.lastMessage?.fromMe && "You: "}
                {conversation.lastMessage?.body || "No messages yet"}
              </p>
            )}
            <div className="flex items-center justify-between">
              <div className="flex gap-1">
                {tags.slice(0, 2).map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="text-xs px-1.5 py-0"
                  >
                    {tag}
                  </Badge>
                ))}
                {tags.length > 2 && (
                  <Badge variant="outline" className="text-xs px-1.5 py-0">
                    +{tags.length - 2}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {conversation.lastMessage?.ack &&
                  conversation.lastMessage?.ack >= 2 ? (
                  <CheckCheck
                    className={clsx(
                      "h-4 w-4",
                      conversation.lastMessage?.ack == 2
                        ? ""
                        : "text-sp-primary",
                    )}
                  />
                ) : conversation.lastMessage?.ack == 0 ? (
                  <Clock className={clsx("h-4 w-4")} />
                ) : conversation.lastMessage?.ack == -1 ? (
                  <Info className={clsx("h-4 w-4 text-destructive")} />
                ) : null}
                {conversation.lastMessageAt ?
                  format(new Date(conversation.lastMessageAt), "MMM d") :
                  conversation.timestamp
                }
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
