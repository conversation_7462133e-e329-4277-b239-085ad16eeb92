import { FieldConfig, ValidationRule } from "./data-editor-types"

/**
 * Validates a single field based on its configuration and validation rules
 * @param field - The field configuration
 * @param value - The current value of the field
 * @returns Error message if validation fails, null if validation passes
 */
export const validateField = (
  field: FieldConfig,
  value: any,
): string | null => {
  const { validation } = field
  if (!validation) return null

  // Required validation
  if (
    validation.required &&
    (!value || (Array.isArray(value) && value.length === 0))
  ) {
    return `${field.label} is required`
  }

  // Skip other validations if field is empty and not required
  if (!value && !validation.required) return null

  // String length validations
  if (typeof value === "string") {
    if (validation.minLength && value.length < validation.minLength) {
      return `${field.label} must be at least ${validation.minLength} characters`
    }
    if (validation.maxLength && value.length > validation.maxLength) {
      return `${field.label} must not exceed ${validation.maxLength} characters`
    }
  }

  // Number validations
  if (field.type === "number" && value !== "") {
    const numValue = Number(value)
    if (isNaN(numValue)) {
      return `${field.label} must be a valid number`
    }
    if (validation.min !== undefined && numValue < validation.min) {
      return `${field.label} must be at least ${validation.min}`
    }
    if (validation.max !== undefined && numValue > validation.max) {
      return `${field.label} must not exceed ${validation.max}`
    }
  }

  // Pattern validation
  if (
    validation.pattern &&
    typeof value === "string" &&
    !validation.pattern.test(value)
  ) {
    if (field.type === "email") {
      return `${field.label} must be a valid email address`
    }
    if (field.type === "phone") {
      return `${field.label} must be a valid phone number`
    }
    if (field.type === "url") {
      return `${field.label} must be a valid URL`
    }
    return `${field.label} format is invalid`
  }

  // Custom validation
  if (validation.custom) {
    return validation.custom(value)
  }

  return null
}

/**
 * Validates all fields in a form
 * @param fields - Array of field configurations
 * @param formData - Current form data
 * @returns Object with field names as keys and error messages as values
 */
export const validateForm = (
  fields: FieldConfig[],
  formData: Record<string, any>,
): Record<string, string> => {
  const errors: Record<string, string> = {}

  fields.forEach((field) => {
    if (field.hidden) return

    const error = validateField(field, formData[field.name])
    if (error) {
      errors[field.name] = error
    }
  })

  return errors
}

/**
 * Validates file upload based on configuration
 * @param file - The file to validate
 * @param maxFileSize - Maximum file size in MB
 * @param allowedFileTypes - Array of allowed MIME types
 * @returns Error message if validation fails, null if validation passes
 */
export const validateFile = (
  file: File,
  maxFileSize?: number,
  allowedFileTypes?: string[],
): string | null => {
  // Validate file size
  if (maxFileSize && file.size > maxFileSize * 1024 * 1024) {
    return `File size must not exceed ${maxFileSize}MB`
  }

  // Validate file type
  if (allowedFileTypes && !allowedFileTypes.includes(file.type)) {
    return `File type not allowed. Allowed types: ${allowedFileTypes.join(", ")}`
  }

  return null
}

/**
 * Gets default value for a field based on its type
 * @param field - The field configuration
 * @returns Default value for the field
 */
export const getDefaultFieldValue = (field: FieldConfig): any => {
  if (field.defaultValue !== undefined) {
    return field.defaultValue
  }

  // Set appropriate default values based on field type
  switch (field.type) {
    case "multiselect":
      return []
    case "number":
      return ""
    default:
      return ""
  }
}

/**
 * Initializes form data with default values
 * @param fields - Array of field configurations
 * @returns Initial form data object
 */
export const initializeFormData = (
  fields: FieldConfig[],
): Record<string, any> => {
  const initialData: Record<string, any> = {}

  fields.forEach((field) => {
    initialData[field.name] = getDefaultFieldValue(field)
  })

  return initialData
}
