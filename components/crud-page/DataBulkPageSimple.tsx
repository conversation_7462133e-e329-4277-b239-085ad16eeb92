"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Upload, Edit, Trash2 } from "lucide-react"
import BulkImportComponent from "./BulkImportComponent"
import BulkUpdateComponent from "./BulkUpdateComponent"
import BulkDeleteComponent from "./BulkDeleteComponent"
import type { BulkImportConfig } from "./BulkImportComponent"
import type { BulkUpdateConfig } from "./BulkUpdateComponent"
import type { BulkDeleteConfig } from "./BulkDeleteComponent"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

// Main configuration interface
export interface DataBulkConfig {
  title: string
  subtitle?: string
  backRoute: string

  // Operation configurations
  importConfig?: BulkImportConfig
  updateConfig?: BulkUpdateConfig
  deleteConfig?: BulkDeleteConfig

  // Enabled operations
  enableImport?: boolean
  enableUpdate?: boolean
  enableDelete?: boolean
}

interface DataBulkPageProps {
  config: DataBulkConfig
}

export default function DataBulkPage({ config }: DataBulkPageProps) {
  const { t } = useLocalization("crud-page", locales)
  const router = useRouter()
  const [activeTab, setActiveTab] = useState(() => {
    if (config.enableImport) return "import"
    if (config.enableUpdate) return "update"
    if (config.enableDelete) return "delete"
    return "import"
  })

  const handleBack = () => {
    router.push(config.backRoute)
  }

  const handleSuccess = () => {
    // Refresh parent component or show success message
    console.log("Bulk operation completed successfully")
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {config.title} - {t("bulk_ops_title")}
          </h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>

      {/* Operation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {config.enableImport && (
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              {t("data_bulk_import")}
            </TabsTrigger>
          )}
          {config.enableUpdate && (
            <TabsTrigger value="update" className="flex items-center gap-2">
              <Edit className="w-4 h-4" />
              {t("data_bulk_update")}
            </TabsTrigger>
          )}
          {config.enableDelete && (
            <TabsTrigger value="delete" className="flex items-center gap-2">
              <Trash2 className="w-4 h-4" />
              {t("data_bulk_delete")}
            </TabsTrigger>
          )}
        </TabsList>

        {/* Import Tab */}
        {config.enableImport && config.importConfig && (
          <TabsContent value="import" className="space-y-6">
            <BulkImportComponent
              config={config.importConfig}
              onSuccess={handleSuccess}
            />
          </TabsContent>
        )}

        {/* Update Tab */}
        {config.enableUpdate && config.updateConfig && (
          <TabsContent value="update" className="space-y-6">
            <BulkUpdateComponent
              config={config.updateConfig}
              onSuccess={handleSuccess}
            />
          </TabsContent>
        )}

        {/* Delete Tab */}
        {config.enableDelete && config.deleteConfig && (
          <TabsContent value="delete" className="space-y-6">
            <BulkDeleteComponent
              config={config.deleteConfig}
              onSuccess={handleSuccess}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}

// Keep the simple config for backward compatibility
export interface DataBulkConfigSimple {
  title: string
  subtitle?: string
  backRoute: string
}
