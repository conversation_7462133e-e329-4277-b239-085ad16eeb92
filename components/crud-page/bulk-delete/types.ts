// Types for bulk delete functionality

export interface DeleteResult {
  total: number
  successful: number
  failed: number
  errors: Array<{
    id: string
    message: string
  }>
}

export interface BulkDeleteConfig {
  // Data fetching
  fetchData: () => Promise<Record<string, any>[]>
  deleteData: (ids: string[]) => Promise<DeleteResult>

  // Table configuration
  headers: string[]
  transformToTableRow: (item: Record<string, any>) => {
    id: string
    columns: string[]
  }

  // Display configuration
  itemName: string // e.g., "contact", "product"
  itemNamePlural: string // e.g., "contacts", "products"
}
