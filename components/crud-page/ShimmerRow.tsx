"use client"

import {
  PinnedColumn,
  createPinnedColumnConfig,
} from "./PinnedColumn/PinnedColumn"

interface ShimmerRowProps {
  rowIndex: number
  headers: string[]
  pinnedColumns: string[]
  columnWidth: Record<string, string>
  defaultColumnWidth: string
  isCanShowAction: () => boolean
  action?: {
    edit?: boolean
    delete?: boolean
  }
}

export default function ShimmerRow({
  rowIndex,
  headers,
  pinnedColumns,
  columnWidth,
  defaultColumnWidth,
  isCanShowAction,
  action,
}: ShimmerRowProps) {
  return (
    <tr className={`group/row-body ${rowIndex % 2 === 0 ? "bg-white" : ""}`}>
      {/* Row number column */}
      <td
        className={`sticky left-0 px-6 py-3 w-10 text-center ${
          rowIndex % 2 !== 0 ? "bg-[#F5F5F5]" : "bg-white"
        }`}
      >
        <div className="animate-pulse bg-gray-200 h-4 w-6 rounded mx-auto"></div>
      </td>

      {/* Data columns */}
      {headers.map((header, index) => {
        const config = createPinnedColumnConfig(
          header,
          pinnedColumns,
          columnWidth,
          defaultColumnWidth,
        )
        return (
          <PinnedColumn
            key={index}
            {...config}
            style={{
              minWidth: columnWidth[headers[index]] || defaultColumnWidth,
            }}
            className="px-2 py-3 text-center"
            isEvenRow={rowIndex % 2 === 0}
            zIndex={15}
          >
            <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
          </PinnedColumn>
        )
      })}

      {/* Spacer column */}
      <td className="w-full"></td>

      {/* Action column */}
      {isCanShowAction() && (
        <td
          style={{ minWidth: defaultColumnWidth }}
          className={`flex items-center justify-center px-6 py-3 space-x-4 sticky right-0 z-10 h-[58px] ${
            rowIndex % 2 !== 0 ? "bg-[#F5F5F5]" : "bg-white"
          }`}
        >
          {action?.edit && (
            <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
          )}
          {action?.delete && (
            <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
          )}
        </td>
      )}
    </tr>
  )
}
