"use client"

import { ComponentType } from "react"
import { Edit, Trash2 } from "lucide-react"
import {
  PinnedColumn,
  createPinnedColumnConfig,
} from "./PinnedColumn/PinnedColumn"
import { TableRowData } from "./types"

interface TableRowProps {
  item: TableRowData
  index: number
  headers: string[]
  pinnedColumns: string[]
  columnWidth: Record<string, string>
  defaultColumnWidth: string
  selected?: { id: string }
  currentPage: number
  perPage: number
  onSelectRow: (id: string) => void
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  isCanShowAction: () => boolean
  action?: {
    edit?: boolean
    delete?: boolean
  }
  rowComponents?: Record<
    string,
    {
      component: ComponentType<any>
      props?: Record<string, any>
    }
  >
}

export default function TableRow({
  item,
  index,
  headers,
  pinnedColumns,
  columnWidth,
  defaultColumnWidth,
  selected,
  currentPage,
  perPage,
  onSelectRow,
  onEdit,
  onDelete,
  isCanShowAction,
  action,
  rowComponents,
}: TableRowProps) {
  const isCustomComponent = (columnIndex: number): boolean => {
    return !!(
      rowComponents &&
      rowComponents.hasOwnProperty(headers[columnIndex]) &&
      rowComponents[headers[columnIndex]] !== null &&
      rowComponents[headers[columnIndex]] !== undefined
    )
  }

  const getCustomComponent = (columnIndex: number): ComponentType<any> => {
    return rowComponents![headers[columnIndex]].component
  }

  const getCustomComponentProps = (
    columnIndex: number,
  ): Record<string, any> => {
    return rowComponents![headers[columnIndex]].props || {}
  }

  const handleEdit = (id: string) => {
    if (onEdit) {
      onEdit(id)
    }
  }

  const handleDelete = (id: string) => {
    if (onDelete) {
      onDelete(id)
    }
  }

  return (
    <tr
      key={`tr-${index}`}
      className={`group/row-body cursor-pointer ${
        index % 2 === 0 ? "bg-white" : ""
      } ${selected?.id === item.id ? "bg-[#000090] text-white font-bold" : ""}`}
      onClick={() => onSelectRow(item.id)}
    >
      <td
        scope="row"
        className={`group-hover/row-body:bg-[#000080] group-hover/row-body:text-white sticky left-0 px-6 py-3 w-10 text-center ${
          index % 2 !== 0 ? "bg-[#F5F5F5]" : "bg-white"
        } ${
          selected?.id === item.id ? "bg-[#000090] text-white font-bold" : ""
        }`}
      >
        {(currentPage - 1) * perPage + index + 1}
      </td>
      {headers.map((header, columnIndex) => {
        const config = createPinnedColumnConfig(
          header,
          pinnedColumns,
          columnWidth,
          defaultColumnWidth,
        )
        return (
          <PinnedColumn
            key={columnIndex}
            {...config}
            style={{
              minWidth: columnWidth[headers[columnIndex]] || defaultColumnWidth,
            }}
            className="group-hover/row-body:bg-[#000080] group-hover/row-body:text-white px-2 py-3 text-center"
            isSelected={selected?.id === item.id}
            isEvenRow={index % 2 === 0}
            zIndex={15}
          >
            {isCustomComponent(columnIndex) ? (
              (() => {
                const CustomComponent = getCustomComponent(columnIndex)
                const customProps = getCustomComponentProps(columnIndex)
                return (
                  <CustomComponent
                    {...customProps}
                    data={item.columns[columnIndex]}
                  />
                )
              })()
            ) : (
              <span>{item.columns[columnIndex]}</span>
            )}
          </PinnedColumn>
        )
      })}
      <td className="w-full no-print group-hover/row-body:bg-[#000080] group-hover/row-body:text-white"></td>
      {isCanShowAction() && (
        <td
          style={{ minWidth: defaultColumnWidth }}
          className={`group-hover/row-body:bg-[#000080] group-hover/row-body:text-white flex items-center justify-center px-6 py-3 space-x-4 sticky right-0 z-10 no-print h-[58px] ${
            index % 2 !== 0 ? "bg-[#F5F5F5]" : "bg-white"
          } ${
            selected?.id === item.id ? "bg-[#000090] text-white font-bold" : ""
          }`}
        >
          {action?.edit && (
            <button
              className="bg-yellow-500 rounded-md p-0.5"
              onClick={(e) => {
                e.stopPropagation()
                handleEdit(item.id)
              }}
            >
              <Edit className="w-6 h-6" />
            </button>
          )}
          {action?.delete && (
            <button
              className="bg-red-500 rounded-md p-0.5"
              onClick={(e) => {
                e.stopPropagation()
                handleDelete(item.id)
              }}
            >
              <Trash2 className="w-6 h-6" />
            </button>
          )}
        </td>
      )}
    </tr>
  )
}
