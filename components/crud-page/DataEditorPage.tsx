"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

// Import extracted modules
import { DataEditorPageProps } from "./data-editor-types"
import {
  validateForm,
  validateFile,
  initializeFormData,
} from "./data-editor-validation"
import FieldRenderer from "./Field-Renderer/FieldRenderer"
import { LoadingState, ErrorState } from "./DataEditorStates"
import StickyActions from "./StickyActions"

// Re-export types for backward compatibility
export type {
  FieldType,
  ValidationRule,
  FieldConfig,
  FormSection,
  DataEditorConfig,
} from "./data-editor-types"

export default function DataEditorPage({ config, id }: DataEditorPageProps) {
  const { t } = useLocalization("crud-page", locales)
  const router = useRouter()
  const isEditMode = !!id

  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [imagePreview, setImagePreview] = useState<Record<string, string>>({})
  const [loadError, setLoadError] = useState<string | null>(null)

  // Initialize form data
  useEffect(() => {
    const initializeForm = async () => {
      setIsLoading(true)
      try {
        let initialData: Record<string, any> = {}

        if (isEditMode && config.fetchData) {
          // Fetch existing data for edit mode
          initialData = await config.fetchData(id!)
        } else {
          // Set default values for create mode
          initialData = initializeFormData(config.fields)
        }

        setFormData(initialData)
        setLoadError(null)
      } catch (error) {
        console.error("Error initializing form:", error)
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load data"
        setLoadError(errorMessage)
        toast({
          title: t("error_title"),
          description: errorMessage,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    initializeForm()
  }, [id, isEditMode, config])

  // Handle retry for error state
  const handleRetry = async () => {
    setLoadError(null)
    setIsLoading(true)
    try {
      let initialData: Record<string, any> = {}

      if (isEditMode && config.fetchData) {
        initialData = await config.fetchData(id!)
      } else {
        initialData = initializeFormData(config.fields)
      }

      setFormData(initialData)
      setLoadError(null)
    } catch (error) {
      console.error("Error initializing form:", error)
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load data"
      setLoadError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle input change
  const handleInputChange = (fieldName: string, value: any) => {
    setFormData((prev) => ({ ...prev, [fieldName]: value }))

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[fieldName]
        return newErrors
      })
    }
  }

  // Handle file upload
  const handleFileUpload = (fieldName: string, files: FileList | null) => {
    if (!files || files.length === 0) return

    const field = config.fields.find((f) => f.name === fieldName)
    if (!field) return

    const file = files[0]

    // Validate file using extracted validation function
    const fileError = validateFile(
      file,
      config.maxFileSize,
      config.allowedFileTypes,
    )
    if (fileError) {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: fileError,
      }))
      return
    }

    // Handle image preview
    if (field.type === "image" && config.showImagePreview) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview((prev) => ({
          ...prev,
          [fieldName]: e.target?.result as string,
        }))
      }
      reader.readAsDataURL(file)
    }

    handleInputChange(fieldName, file)
  }

  // Validate all fields using extracted validation function
  const validateFormData = (): boolean => {
    const newErrors = validateForm(config.fields, formData)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateFormData()) {
      toast({
        title: t("error_title"),
        description: "Please fix the errors before submitting",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)
    try {
      await config.saveData(formData, isEditMode)

      toast({
        title: t("success_title"),
        description: isEditMode
          ? "Data updated successfully"
          : "Data created successfully",
        variant: "default",
      })

      // Navigate to success route or back
      if (config.successRoute) {
        router.push(config.successRoute)
      } else {
        router.push(config.backRoute)
      }
    } catch (error) {
      console.error("Error saving data:", error)
      toast({
        title: t("error_title"),
        description: "Failed to save data",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    router.push(config.backRoute)
  }

  // Show loading state
  if (isLoading) {
    return (
      <LoadingState
        config={config}
        isEditMode={isEditMode}
        onCancel={handleCancel}
      />
    )
  }

  // Show error state if data loading failed
  if (loadError) {
    return (
      <ErrorState
        config={config}
        isEditMode={isEditMode}
        error={loadError}
        onCancel={handleCancel}
        onRetry={handleRetry}
      />
    )
  }

  return (
    <>
      <div className="container mx-auto p-6 max-w-4xl pb-24">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" size="icon" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isEditMode ? `Edit ${config.title}` : `Create ${config.title}`}
            </h1>
            {config.subtitle && (
              <p className="text-gray-600">{config.subtitle}</p>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Render fields by sections or all together */}
          {config.sections ? (
            config.sections.map((section, sectionIndex) => (
              <Card key={sectionIndex}>
                <CardHeader>
                  <CardTitle>{section.title}</CardTitle>
                  {section.description && (
                    <p className="text-sm text-gray-600">
                      {section.description}
                    </p>
                  )}
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.fields.map((fieldName) => {
                    const field = config.fields.find(
                      (f) => f.name === fieldName,
                    )
                    if (!field || field.hidden) return null
                    return (
                      <FieldRenderer
                        key={field.name}
                        field={field}
                        value={formData[field.name] || ""}
                        error={errors[field.name]}
                        onChange={handleInputChange}
                        onFileUpload={handleFileUpload}
                        imagePreview={imagePreview}
                        setImagePreview={setImagePreview}
                        config={config}
                      />
                    )
                  })}
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="space-y-4 pt-6">
                {config.fields.map((field) => {
                  if (field.hidden) return null
                  return (
                    <FieldRenderer
                      key={field.name}
                      field={field}
                      value={formData[field.name] || ""}
                      error={errors[field.name]}
                      onChange={handleInputChange}
                      onFileUpload={handleFileUpload}
                      imagePreview={imagePreview}
                      setImagePreview={setImagePreview}
                      config={config}
                    />
                  )
                })}
              </CardContent>
            </Card>
          )}
        </form>
      </div>

      {/* Sticky Actions */}
      <StickyActions
        isEditMode={isEditMode}
        isSaving={isSaving}
        config={config}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
      />
    </>
  )
}
