"use client"

import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import {
  createPinnedColumnConfig,
  PinnedColumnHeader,
} from "./PinnedColumn/PinnedColumn"

interface TableHeaderProps {
  headers: string[]
  pinnedColumns: string[]
  columnWidth: Record<string, string>
  defaultColumnWidth: string
  isCanShowAction: () => boolean
}

export default function TableHeader({
  headers,
  pinnedColumns,
  columnWidth,
  defaultColumnWidth,
  isCanShowAction,
}: TableHeaderProps) {
  const { t } = useLocalization("crud-page", locales)

  return (
    <thead className="sticky top-0 bg-gray-300 z-20">
      <tr>
        <th
          scope="col"
          className="sticky left-0 bg-gray-300 px-6 py-3 w-10 text-center z-30"
        >
          {t("table_no_column")}
        </th>
        {headers.map((item, index) => {
          const config = createPinnedColumnConfig(
            item,
            pinnedColumns,
            columnWidth,
            defaultColumnWidth,
          )
          return (
            <PinnedColumnHeader
              key={`th-${index}`}
              {...config}
              style={{ minWidth: columnWidth[item] || defaultColumnWidth }}
              className="px-6 py-3 text-center bg-gray-300"
              zIndex={25}
            >
              {item}
            </PinnedColumnHeader>
          )
        })}
        <th className="w-full no-print"></th>
        {isCanShowAction() && (
          <th
            scope="col"
            className="text-center sticky right-0 bg-gray-300 z-10 no-print"
            style={{ minWidth: defaultColumnWidth }}
          >
            {t("table_action_column")}
          </th>
        )}
      </tr>
    </thead>
  )
}
