"use client"

import { apiLocales } from "@/app/api/locales"
import { useLocalization } from "@/localization/functions/client"
import { SearchConfig } from "@/lib/services/searchConfigApi"
import { deepMerge } from "@/lib/utils/deepMerge"
import { Minus, Plus, Printer } from "lucide-react"
import { useRef } from "react"
import { locales } from "../locales"
import { MultipleFilter, MultipleSort } from "../types"

interface AdvancedFiltersProps {
  multipleFilters: MultipleFilter[]
  multipleSorts: MultipleSort[]
  onAddFilter: () => void
  onRemoveFilter: (filterId: string) => void
  onUpdateFilter: (
    filterId: string,
    field: keyof MultipleFilter,
    value: any,
  ) => void
  onAddSort: () => void
  onRemoveSort: (sortId: string) => void
  onUpdateSort: (sortId: string, field: keyof MultipleSort, value: any) => void
  onCetak?: () => void
  cetakRef?: React.RefObject<HTMLElement>
  title: string
  searchConfig?: SearchConfig | null
}

export default function AdvancedFilters({
  searchConfig,
  multipleFilters,
  multipleSorts,
  onAddFilter,
  onRemoveFilter,
  onUpdateFilter,
  onAddSort,
  onRemoveSort,
  onUpdateSort,
  onCetak,
  cetakRef,
  title,
}: AdvancedFiltersProps) {
  const { t } = useLocalization("crud-page", deepMerge(locales, apiLocales))
  const containerRef = useRef<HTMLDivElement>(null)

  const getOperatorOptions = (filterType: string) => {
    switch (filterType) {
      case "text":
        return [
          { value: "contains", label: t("contains") || "Contains" },
          { value: "equals", label: t("equals") || "Equals" },
          { value: "startsWith", label: t("starts_with") || "Starts with" },
          { value: "endsWith", label: t("ends_with") || "Ends with" },
        ]
      case "number":
      case "date":
        return [
          { value: "equals", label: t("equals") || "Equals" },
          { value: "greaterThan", label: t("greater_than") || "Greater than" },
          { value: "lessThan", label: t("less_than") || "Less than" },
          { value: "between", label: t("between") || "Between" },
        ]
      default:
        return [{ value: "equals", label: t("equals") || "Equals" }]
    }
  }

  const handleCetak = () => {
    if (onCetak) {
      onCetak()
    } else if (cetakRef?.current) {
      const clone = cetakRef.current.cloneNode(true) as HTMLElement

      clone.querySelectorAll(".no-print").forEach((el) => el.remove())
      clone
        .querySelectorAll("[style]")
        .forEach((el) => el.removeAttribute("style"))
      clone
        .querySelectorAll("[class]")
        .forEach((el) => el.removeAttribute("class"))

      const html = clone.outerHTML
      const printWindow = window.open("", "", "height=800,width=1000")
      if (printWindow) {
        printWindow.document.write(`<html><head><title>${title}</title>`)
        printWindow.document.write(
          "<style>table { width: 100%; border-collapse: collapse; } th, td { border: 1px solid black; padding: 8px; } th { background-color: #f4f4f4; }</style>",
        )
        printWindow.document.write("</head><body>")
        printWindow.document.write(html)
        printWindow.document.write("</body></html>")
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
      }
    } else {
      alert(t("print_not_available"))
    }
  }

  return (
    <div className="mb-3 p-4 bg-gray-50 rounded-lg border" ref={containerRef}>
      <div className="space-y-4">
        {/* Filters */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">
              {t("filters") || "Filters"}
            </h3>
            <button
              onClick={onAddFilter}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <Plus className="w-3 h-3" />
              {t("add_filter") || "Add Filter"}
            </button>
          </div>

          {multipleFilters.length === 0 ? (
            <p className="text-sm text-gray-500">
              {t("no_filters_added") || "No filters added"}
            </p>
          ) : (
            <div className="space-y-2">
              {multipleFilters.map((filter: MultipleFilter) => {
                const selectedFilterOption = searchConfig?.filters?.find(
                  (opt) => opt.id === filter.field,
                )
                const operatorOptions = selectedFilterOption
                  ? getOperatorOptions(selectedFilterOption.type)
                  : []

                return (
                  <div
                    key={filter.id}
                    className="flex items-center gap-2 p-2 bg-white rounded border"
                  >
                    <input
                      type="checkbox"
                      className="h-4 w-4"
                      checked={filter.enabled}
                      onChange={(e) =>
                        onUpdateFilter(filter.id, "enabled", e.target.checked)
                      }
                    />
                    <select
                      className="h-8 w-72 bg-white rounded border text-xs px-2"
                      value={filter.field}
                      onChange={(e) =>
                        onUpdateFilter(filter.id, "field", e.target.value)
                      }
                    >
                      <option value="">
                        {t("select_field") || "Select Field"}
                      </option>
                      {searchConfig?.filters.map((option) => (
                        <option key={option.id} value={option.id}>
                          {t(option.name)}
                        </option>
                      ))}
                    </select>

                    {/* Operator */}
                    {!["boolean", "select", "multiselect"].includes(
                      selectedFilterOption?.type || "",
                    ) && (
                      <select
                        className="h-8 w-24 bg-white rounded border text-xs px-2"
                        value={filter.operator}
                        onChange={(e) =>
                          onUpdateFilter(filter.id, "operator", e.target.value)
                        }
                        disabled={!filter.field}
                      >
                        {operatorOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {t(option.label)}
                          </option>
                        ))}
                      </select>
                    )}

                    {/* Value */}
                    {selectedFilterOption?.type === "boolean" ? (
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={!!filter.value}
                        onChange={(e) =>
                          onUpdateFilter(filter.id, "value", e.target.checked)
                        }
                        disabled={!filter.field}
                      />
                    ) : ["select", "multiselect"].includes(
                        selectedFilterOption?.type || "",
                      ) ? (
                      <select
                        className="h-8 w-80 bg-white rounded border text-xs px-2"
                        value={
                          Array.isArray(filter.value)
                            ? filter.value[0] || ""
                            : filter.value
                        }
                        onChange={(e) =>
                          onUpdateFilter(filter.id, "value", e.target.value)
                        }
                        disabled={!filter.field}
                      >
                        <option value="">
                          {t("select_value") || "Select Value"}
                        </option>
                        {selectedFilterOption?.options?.map((option) => (
                          <option key={option.value} value={option.value}>
                            {t(option.value)}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <input
                        type={
                          selectedFilterOption?.type === "number"
                            ? "number"
                            : selectedFilterOption?.type === "date"
                              ? "date"
                              : "text"
                        }
                        className="h-8 w-80 bg-white rounded border text-xs px-2"
                        placeholder={t("enter_value") || "Enter value"}
                        value={
                          Array.isArray(filter.value)
                            ? filter.value[0] || ""
                            : filter.value
                        }
                        onChange={(e) =>
                          onUpdateFilter(filter.id, "value", e.target.value)
                        }
                        disabled={!filter.field}
                      />
                    )}

                    {/* Remove */}
                    <button
                      onClick={() => onRemoveFilter(filter.id)}
                      className="flex items-center justify-center w-6 h-6 text-red-500 hover:bg-red-50 rounded"
                    >
                      <Minus className="w-3 h-3" />
                    </button>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Sorts */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">
              {t("sorting") || "Sorting"}
            </h3>
            <button
              onClick={onAddSort}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
            >
              <Plus className="w-3 h-3" />
              {t("add_sort") || "Add Sort"}
            </button>
          </div>

          {multipleSorts.length === 0 ? (
            <p className="text-sm text-gray-500">
              {t("no_sorts_added") || "No sorting added"}
            </p>
          ) : (
            <div className="space-y-2">
              {multipleSorts.map((sort, index) => (
                <div
                  key={sort.id}
                  className="flex items-center gap-2 p-2 bg-white rounded border"
                >
                  <span className="text-xs text-gray-500 w-4">
                    {index + 1}.
                  </span>

                  {/* Field */}
                  <select
                    className="h-8 w-40 bg-white rounded border text-xs px-2"
                    value={sort.field}
                    onChange={(e) =>
                      onUpdateSort(sort.id, "field", e.target.value)
                    }
                  >
                    <option value="">
                      {t("select_column") || "Select Column"}
                    </option>
                    {searchConfig?.sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {t(option.label)}
                      </option>
                    ))}
                  </select>

                  {/* Order */}
                  <select
                    className="h-8 w-20 bg-white rounded border text-xs px-2"
                    value={sort.direction}
                    onChange={(e) =>
                      onUpdateSort(
                        sort.id,
                        "direction",
                        e.target.value as "ASC" | "DESC",
                      )
                    }
                    disabled={!sort.field}
                  >
                    <option value="ASC">{t("asc_order") || "ASC"}</option>
                    <option value="DESC">{t("desc_order") || "DESC"}</option>
                  </select>

                  {/* Remove */}
                  <button
                    onClick={() => onRemoveSort(sort.id)}
                    className="flex items-center justify-center w-6 h-6 text-red-500 hover:bg-red-50 rounded"
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Print */}
        {(onCetak || cetakRef) && (
          <div className="flex justify-end">
            <button
              className="h-9 bg-white rounded-[10px] border flex items-center px-3 gap-2 hover:bg-gray-50"
              onClick={handleCetak}
            >
              <Printer className="w-4 h-4" />
              <span>{t("print_button")}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
