"use client"

import { useState, useRef, useEffect } from "react"
import {
  Search,
  Plus,
  Settings,
  MoreHorizontal,
  ChevronDown,
} from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { apiLocales } from "@/app/api/locales"
import { deepMerge } from "@/lib/utils/deepMerge"
import { PageHeaderProps } from "../types"

export default function SimpleHeader({
  title,
  total,
  onAdd,
  onBulk,
  onCetak,
  searchConfig,
  currentFilters,
  onFiltersChange,
  showAdvancedToggle,
  onToggleAdvanced,
}: PageHeaderProps & {
  showAdvancedToggle: boolean
  onToggleAdvanced: () => void
}) {
  const { t } = useLocalization("crud-page", deepMerge(locales, apiLocales))

  const [showDateFilterDropdown, setShowDateFilterDropdown] = useState(false)
  const dateFilterRef = useRef<HTMLDivElement>(null)

  const selectedDateFilter = currentFilters.dateFilter || ""
  const selectedSortField = currentFilters.sort?.field || ""
  const selectedSortDirection = currentFilters.sort?.direction || "ASC"
  const searchQuery = currentFilters.search

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dateFilterRef.current &&
        !dateFilterRef.current.contains(event.target as Node)
      ) {
        setShowDateFilterDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSearchChange = (value: string) => {
    onFiltersChange(
      value,
      selectedDateFilter,
      currentFilters.sort,
      currentFilters.filters,
      currentFilters.sort,
    )
  }

  const handleDateFilterChange = (value: string) => {
    onFiltersChange(
      searchQuery,
      value,
      currentFilters.sort,
      currentFilters.filters,
      currentFilters.sort,
    )
  }

  const handleSortFieldChange = (field: string) => {
    onFiltersChange(
      searchQuery,
      selectedDateFilter,
      { field, direction: selectedSortDirection },
      currentFilters.filters,
      currentFilters.sort,
    )
  }

  const handleSortDirectionChange = (direction: "ASC" | "DESC") => {
    if (!selectedSortField) return
    onFiltersChange(
      searchQuery,
      selectedDateFilter,
      { field: selectedSortField, direction: direction },
      currentFilters.filters,
      currentFilters.sort,
    )
  }

  const selectedDateFilterLabel =
    (searchConfig &&
      searchConfig?.dateFilterOptions?.find(
        (option) => option.value === selectedDateFilter,
      )?.label) ||
    t("select_date_range")

  return (
    <div className="flex flex-col">
      <h2 className="text-black text-[22px] font-bold font-['DM Sans'] tracking-tight">
        {title}
      </h2>

      <div className="mb-3 flex justify-between items-center">
        <div className="text-black text-[22px] font-medium font-['DM Sans'] tracking-tight">
          {t("total_label")}: {total}
        </div>

        <div className="flex items-center gap-2">
          {/* Search Input */}
          <div className="relative">
            <input
              className="h-9 w-96 p-3 bg-white rounded bdirection"
              type="text"
              placeholder={t("search_placeholder")}
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
            <button
              className="absolute right-2 top-2 text-gray-500"
              onClick={() => handleSearchChange(searchQuery)}
            >
              <Search className="w-6 h-6" />
            </button>
          </div>

          {/* Date Filter Dropdown */}
          {searchConfig && searchConfig?.dateFilterOptions?.length > 0 && (
            <div className="relative" ref={dateFilterRef}>
              <button
                className="h-9 w-48 bg-white rounded bdirection flex justify-between items-center px-3"
                onClick={() =>
                  setShowDateFilterDropdown(!showDateFilterDropdown)
                }
              >
                <span className="text-sm">{t(selectedDateFilterLabel)}</span>
                <ChevronDown className="w-4 h-4 ml-2" />
              </button>
              {showDateFilterDropdown && (
                <div className="absolute bg-white bdirection rounded shadow-md mt-2 w-full z-30">
                  {searchConfig.dateFilterOptions.map((option) => (
                    <button
                      key={option.value}
                      className="flex items-center p-2 hover:bg-gray-100 w-full text-left"
                      onClick={() => {
                        handleDateFilterChange(option.value)
                        setShowDateFilterDropdown(false)
                      }}
                    >
                      <input
                        type="radio"
                        id={option.value}
                        value={option.value}
                        checked={selectedDateFilter === option.value}
                        readOnly
                      />
                      <label htmlFor={option.value} className="ml-2 text-sm">
                        {t(option.label)}
                      </label>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Sort Field Dropdown */}
          {searchConfig && searchConfig?.sortOptions?.length > 0 && (
            <select
              className="h-9 w-40 bg-white rounded bdirection text-sm px-2"
              value={selectedSortField}
              onChange={(e) => handleSortFieldChange(e.target.value)}
            >
              <option value="">{t("select_column")}</option>
              {searchConfig.sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {t(option.label)}
                </option>
              ))}
            </select>
          )}

          {/* Sort Direction Dropdown */}
          {selectedSortField && (
            <select
              className="h-9 w-20 bg-white rounded bdirection text-sm px-2"
              value={selectedSortDirection}
              onChange={(e) =>
                handleSortDirectionChange(e.target.value as "ASC" | "DESC")
              }
            >
              <option value="ASC">{t("asc_direction")}</option>
              <option value="DESC">{t("desc_direction")}</option>
            </select>
          )}

          {/* Add Button */}
          {onAdd && (
            <button
              className="h-9 bg-blue-500 text-white rounded-[10px] bdirection flex items-center px-3 gap-2"
              onClick={onAdd}
            >
              <Plus className="w-4 h-4" />
              <span>{t("add_button")}</span>
            </button>
          )}

          {/* Bulk Button */}
          {onBulk && (
            <button
              className="h-9 bg-purple-500 text-white rounded-[10px] bdirection flex items-center px-3 gap-2"
              onClick={onBulk}
            >
              <Settings className="w-4 h-4" />
              <span>{t("bulk_ops_button")}</span>
            </button>
          )}

          {/* Advanced */}
          {showAdvancedToggle && (
            <button
              className="h-9 bg-gray-100 hover:bg-gray-200 rounded-[10px] bdirection flex items-center px-3 gap-2"
              onClick={onToggleAdvanced}
            >
              <MoreHorizontal className="w-4 h-4" />
              <span>{t("more_button")}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
