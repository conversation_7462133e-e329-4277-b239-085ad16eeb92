"use client"

import { useState, useEffect } from "react"
import <PERSON><PERSON>ead<PERSON> from "./header/SimpleHeader"
import AdvancedFilters from "./header/AdvancedFilters"
import { PageHeaderProps, MultipleFilter, MultipleSort } from "./types"

// Utility to generate a random ID
const generateId = () => Math.random().toString(36).substring(2, 11)

export default function PageHeaderComponent({
  title,
  total,
  onAdd,
  onBulk,
  onCetak,
  cetakRef,
  searchConfig,
  currentFilters,
  onFiltersChange,
}: PageHeaderProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [currentMultipleFilters, setCurrentMultipleFilters] = useState<
    MultipleFilter[]
  >([])
  const [currentMultipleSorts, setCurrentMultipleSorts] = useState<
    MultipleSort[]
  >([])

  // Load filters/sort from localStorage
  useEffect(() => {
    const fromLocal = localStorage.getItem(
      `pageheadercomponent-multiplestate-${title}`,
    )
    if (!fromLocal) return

    try {
      const parsed = JSON.parse(fromLocal)
      if (parsed.filters) setCurrentMultipleFilters(parsed.filters)
      if (parsed.sort) setCurrentMultipleSorts(parsed.sort)
    } catch (error) {
      console.warn("Failed to parse saved filters/sort from localStorage")
    }
  }, [title])

  // Persist filters/sort to localStorage on change
  useEffect(() => {
    localStorage.setItem(
      `pageheadercomponent-multiplestate-${title}`,
      JSON.stringify({
        filters: currentMultipleFilters,
        sort: currentMultipleSorts,
      }),
    )
    onFiltersChange(
      currentFilters.search,
      currentFilters.dateFilter,
      currentFilters.sort,
      currentMultipleFilters.filter((f) => f.enabled && f.value),
      currentMultipleSorts,
    )
  }, [currentMultipleFilters, currentMultipleSorts])

  // === Filter Handlers ===
  const handleAddFilter = () => {
    const newFilter: MultipleFilter = {
      id: generateId(),
      enabled: true,
      field: "",
      operator: "equals",
      value: "",
    }
    setCurrentMultipleFilters((prev) => [...prev, newFilter])
  }

  const handleRemoveFilter = (filterId: string) => {
    setCurrentMultipleFilters((prev) => prev.filter((f) => f.id !== filterId))
  }

  const handleUpdateFilter = (
    filterId: string,
    field: keyof MultipleFilter,
    value: any,
  ) => {
    setCurrentMultipleFilters((prev) =>
      prev.map((filter) =>
        filter.id === filterId ? { ...filter, [field]: value } : filter,
      ),
    )
  }

  // === Sort Handlers ===
  const handleAddSort = () => {
    const newSort: MultipleSort = {
      id: generateId(),
      field: "",
      direction: "ASC",
    }
    setCurrentMultipleSorts((prev) => [...prev, newSort])
  }

  const handleRemoveSort = (sortId: string) => {
    setCurrentMultipleSorts((prev) => prev.filter((s) => s.id !== sortId))
  }

  const handleUpdateSort = (
    sortId: string,
    field: keyof MultipleSort,
    value: any,
  ) => {
    setCurrentMultipleSorts((prev) =>
      prev.map((sort) =>
        sort.id === sortId ? { ...sort, [field]: value } : sort,
      ),
    )
  }

  // === UI Logic ===
  const showAdvancedToggle = Boolean(
    (searchConfig?.filters?.length || 0) > 0 ||
      (searchConfig?.sortOptions?.length || 0) > 0 ||
      (searchConfig?.dateFilterOptions?.length || 0) > 0 ||
      onCetak ||
      cetakRef,
  )

  return (
    <div className="flex flex-col">
      <SimpleHeader
        title={title}
        total={total}
        onAdd={onAdd}
        onBulk={onBulk}
        onToggleAdvanced={() => setShowAdvancedFilters(!showAdvancedFilters)}
        showAdvancedToggle={showAdvancedToggle}
        searchConfig={searchConfig}
        currentFilters={currentFilters}
        onFiltersChange={onFiltersChange}
        onCetak={onCetak}
        cetakRef={cetakRef}
      />

      {showAdvancedFilters && (
        <AdvancedFilters
          searchConfig={searchConfig}
          multipleFilters={currentMultipleFilters}
          multipleSorts={currentMultipleSorts}
          onAddFilter={handleAddFilter}
          onRemoveFilter={handleRemoveFilter}
          onUpdateFilter={handleUpdateFilter}
          onAddSort={handleAddSort}
          onRemoveSort={handleRemoveSort}
          onUpdateSort={handleUpdateSort}
          onCetak={onCetak}
          cetakRef={cetakRef}
          title={title}
        />
      )}
    </div>
  )
}
