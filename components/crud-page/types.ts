import { SearchConfig } from "@/lib/services/searchConfigApi"
import { ComponentType } from "react"

export interface MultipleFilter {
  id: string
  enabled: boolean
  field: string
  operator:
    | "equals"
    | "contains"
    | "startsWith"
    | "endsWith"
    | "greaterThan"
    | "lessThan"
    | "between"
  value: string | string[]
}

export interface MultipleSort {
  id: string
  field: string
  direction: "ASC" | "DESC"
}

// Common table types
export interface TableRowData {
  id: string
  columns: any[]
}

export interface ActionConfig {
  edit?: boolean
  delete?: boolean
}

export interface RowComponent {
  component: ComponentType<any>
  props?: Record<string, any>
}

// Header component types
export interface SortOption {
  value: string
  label: string
}

export interface DateFilterOption {
  value: string
  label: string
}

export interface PageHeaderProps {
  title: string
  total: number
  onAdd?: () => void
  onBulk?: () => void
  onCetak?: () => void
  searchConfig?: SearchConfig | null
  cetakRef?: React.RefObject<HTMLElement>
  currentFilters: {
    search: string
    dateFilter?: string
    sort?: { field: string; direction: "ASC" | "DESC" }
    filters?: { field: string; operator?: string; value: any }[]
    sort?: { field: string; direction: "ASC" | "DESC" }[]
  }
  onFiltersChange: (
    search: string,
    dateFilter?: string,
    sort?: { field: string; direction: "ASC" | "DESC" },
    filters?: { field: string; operator?: string; value: any }[],
    sort?: { field: string; direction: "ASC" | "DESC" }[],
  ) => void
}

// Table component types
export interface TableComponentProps {
  isScroll?: boolean
  headers: string[]
  data: TableRowData[]
  action?: ActionConfig
  selected?: { id: string }
  didSelectAt?: (id: string) => void
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  rowComponents?: Record<string, RowComponent>
  columnWidth?: Record<string, string>
  defaultColumnWidth?: string
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
  perPage?: number
  pinnedColumns?: string[] // Array of column names to pin to the left
  isLoading?: boolean // Loading state for shimmer effect
}
