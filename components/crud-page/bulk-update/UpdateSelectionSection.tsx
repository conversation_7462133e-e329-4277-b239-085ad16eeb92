"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Download, Upload, CheckCircle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { BulkUpdateConfig } from "./types"

interface UpdateSelectionSectionProps {
  config: BulkUpdateConfig
  data: Record<string, any>[]
  selectedIds: string[]
  isLoading: boolean
  onRowSelect: (id: string, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  onExport: () => void
  onImport: () => void
}

export default function UpdateSelectionSection({
  config,
  data,
  selectedIds,
  isLoading,
  onRowSelect,
  onSelectAll,
  onExport,
  onImport,
}: UpdateSelectionSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t("bulk_update_data_selection")}</span>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {selectedIds.length} {t("pagination_of")} {data.length}{" "}
              {t("bulk_delete_selected")}
            </Badge>
            <Button
              variant="outline"
              onClick={onExport}
              disabled={selectedIds.length === 0}
            >
              <Download className="w-4 h-4 mr-2" />
              {t("bulk_update_export_selected")}
            </Button>
            <Button onClick={onImport} disabled={selectedIds.length === 0}>
              <Upload className="w-4 h-4 mr-2" />
              {t("bulk_update_import_updates")}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Select All Checkbox */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            id="select-all"
            checked={selectedIds.length === data.length && data.length > 0}
            onCheckedChange={onSelectAll}
          />
          <label htmlFor="select-all" className="text-sm font-medium">
            {t("bulk_delete_select_all")} ({data.length}{" "}
            {t("bulk_import_records")})
          </label>
        </div>

        {/* Data Table with Selection */}
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-2 text-left font-medium text-gray-900 w-12">
                  {t("bulk_delete_select")}
                </th>
                {config.headers.map((header) => (
                  <th
                    key={header}
                    className="px-3 py-2 text-left font-medium text-gray-900"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td
                    colSpan={config.headers.length + 1}
                    className="px-3 py-8 text-center text-gray-500"
                  >
                    {t("loading_message")}
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td
                    colSpan={config.headers.length + 1}
                    className="px-3 py-8 text-center text-gray-500"
                  >
                    {t("no_data_message")}
                  </td>
                </tr>
              ) : (
                data.map((item, idx) => {
                  const row = config.transformToTableRow(item)
                  const isSelected = selectedIds.includes(item.id)
                  return (
                    <tr
                      key={idx}
                      className={`border-t ${isSelected ? "bg-blue-50" : ""}`}
                    >
                      <td className="px-3 py-2">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) =>
                            onRowSelect(item.id, checked as boolean)
                          }
                        />
                      </td>
                      {row.columns.map((cell, cellIndex) => (
                        <td key={cellIndex} className="px-3 py-2">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Selection Info */}
        {selectedIds.length > 0 && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-800">
                {selectedIds.length} {t("bulk_import_record")}
                {selectedIds.length !== 1 ? "s" : ""}{" "}
                {t("bulk_update_selected_for_update")}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
