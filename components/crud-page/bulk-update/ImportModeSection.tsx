"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import BulkImportComponent from "../BulkImportComponent"
import type { BulkImportConfig } from "../bulk-import/FileParsingUtils"

interface ImportModeSectionProps {
  importConfig: BulkImportConfig
  onBack: () => void
  onSuccess: () => void
}

export default function ImportModeSection({
  importConfig,
  onBack,
  onSuccess,
}: ImportModeSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  return (
    <div className="space-y-6">
      {/* Back to selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{t("bulk_update_import_title")}</span>
            <Button variant="outline" onClick={onBack}>
              {t("bulk_update_back_to_selection")}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">
                  {t("bulk_update_workflow_title")}
                </h4>
                <p className="text-sm text-blue-700 mt-1">
                  {t("bulk_update_import_description")}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Import component */}
      <BulkImportComponent config={importConfig} onSuccess={onSuccess} />
    </div>
  )
}
