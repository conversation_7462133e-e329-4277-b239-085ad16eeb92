"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Edit } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function UpdateInstructionsSection() {
  const { t } = useLocalization("crud-page", locales)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit className="w-5 h-5" />
          {t("bulk_update_workflow_title")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">
            {t("bulk_update_how_to")}:
          </h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
            <li>{t("bulk_update_step_1")}</li>
            <li>{t("bulk_update_step_2")}</li>
            <li>{t("bulk_update_step_3")}</li>
            <li>{t("bulk_update_step_4")}</li>
            <li>{t("bulk_update_step_5")}</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  )
}
