// Types for bulk update functionality

import type {
  ImportResult,
  BulkImportConfig,
} from "../bulk-import/FileParsingUtils"

export interface BulkUpdateConfig {
  // Data fetching
  fetchData: () => Promise<Record<string, any>[]>
  updateData: (data: Record<string, any>[]) => Promise<ImportResult>

  // Table configuration
  headers: string[]
  transformToTableRow: (item: Record<string, any>) => {
    id: string
    columns: string[]
  }

  // Import configuration for the update workflow
  importConfig: BulkImportConfig
}
