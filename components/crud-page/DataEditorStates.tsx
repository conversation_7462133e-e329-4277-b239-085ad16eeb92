"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, X } from "lucide-react"
import { LoadingStateProps, ErrorStateProps } from "./data-editor-types"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

export function LoadingState({
  config,
  isEditMode,
  onCancel,
}: LoadingStateProps) {
  const { t } = useLocalization("crud-page", locales)
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={onCancel} disabled>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {isEditMode
              ? t("error_state_title_edit", { title: config.title })
              : t("error_state_title_create", { title: config.title })}
          </h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>

      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-600">{t("loading_state_subtitle")}</p>
      </div>
    </div>
  )
}

export function ErrorState({
  config,
  isEditMode,
  error,
  onCancel,
  onRetry,
}: ErrorStateProps) {
  const { t } = useLocalization("crud-page", locales)
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {isEditMode
              ? t("error_state_title_edit", { title: config.title })
              : t("error_state_title_create", { title: config.title })}
          </h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>

      <div className="flex flex-col items-center justify-center py-12">
        <div className="text-red-500 mb-4">
          <X className="h-12 w-12" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {t("error_state_subtitle")}
        </h2>
        <p className="text-gray-600 mb-4 text-center">{error}</p>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            {t("error_state_back_button")}
          </Button>
          <Button onClick={onRetry}>{t("error_state_retry_button")}</Button>
        </div>
      </div>
    </div>
  )
}
