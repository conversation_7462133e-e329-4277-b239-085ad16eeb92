"use client"

import React from "react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { X } from "lucide-react"
import { FieldRendererProps } from "../data-editor-types"
import { fileURLToPath } from "url"

export default function FieldRenderer({
  field,
  value,
  error,
  onChange,
  onFileUpload,
  imagePreview = {},
  setImagePreview,
  config,
}: FieldRendererProps) {
  const { t } = useLocalization("crud-page", locales)
  return (
    <div key={field.name} className="space-y-2">
      <Label
        htmlFor={field.name}
        className={
          field.validation?.required
            ? "after:content-['*'] after:text-red-500"
            : ""
        }
      >
        {t(field.label)}
      </Label>

      {field.description && (
        <p className="text-sm text-gray-500">{t(field.description)}</p>
      )}

      {/* Render different input types */}
      {field.type === "text" && (
        <Input
          id={field.name}
          type="text"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "email" && (
        <Input
          id={field.name}
          type="email"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "phone" && (
        <Input
          id={field.name}
          type="tel"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={
            field.placeholder ? t(field.placeholder) : t("phone_placeholder")
          }
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "number" && (
        <Input
          id={field.name}
          type="number"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
          disabled={field.disabled}
          min={field.validation?.min}
          max={field.validation?.max}
          step={field.step}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "password" && (
        <Input
          id={field.name}
          type="password"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "url" && (
        <Input
          id={field.name}
          type="url"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={
            field.placeholder ? t(field.placeholder) : t("url_placeholder")
          }
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "date" && (
        <Input
          id={field.name}
          type="date"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
        />
      )}

      {field.type === "datetime" && (
        <Input
          id={field.name}
          type="datetime-local"
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          disabled={field.disabled}
          className={error ? "border-red-500" : ""}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
        />
      )}

      {field.type === "textarea" && (
        <Textarea
          id={field.name}
          value={value}
          onChange={(e) => onChange(field.name, e.target.value)}
          placeholder={field.placeholder ? t(field.placeholder) : undefined}
          disabled={field.disabled}
          rows={field.rows || 3}
          className={error ? "border-red-500" : ""}
        />
      )}

      {field.type === "select" && (
        <Select
          value={value}
          onValueChange={(newValue) => onChange(field.name, newValue)}
          disabled={field.disabled}
        >
          <SelectTrigger className={error ? "border-red-500" : ""}>
            <SelectValue
              placeholder={
                field.placeholder ? t(field.placeholder) : t("select_an_option")
              }
            />
          </SelectTrigger>
          <SelectContent>
            {field.options?.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {t(option.label)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {(field.type === "image" || field.type === "file") && (
        <div className="space-y-2">
          <Input
            id={field.name}
            type="file"
            onChange={(e) => onFileUpload(field.name, e.target.files)}
            accept={field.accept}
            multiple={field.multiple}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />

          {/* Image preview */}
          {field.type === "image" &&
            config.showImagePreview &&
            imagePreview[field.name] &&
            setImagePreview && (
              <div className="relative inline-block">
                <img
                  src={imagePreview[field.name]}
                  alt={t("image_preview_alt")}
                  className="max-w-xs max-h-48 rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 h-6 w-6"
                  onClick={() => {
                    setImagePreview((prev) => {
                      const newPreview = { ...prev }
                      delete newPreview[field.name]
                      return newPreview
                    })
                    onChange(field.name, "")
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
        </div>
      )}

      {/* Error message */}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  )
}
