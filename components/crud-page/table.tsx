"use client"

import { useState, useRef } from "react"
import { useLocalization } from "@/localization/functions/client"

import { TableComponentProps } from "./types"
import ShimmerRow from "./ShimmerRow"
import TableHeader from "./TableHeader"
import TableRow from "./TableRow"
import Pagination from "./Pagination"
import { locales } from "./locales"
import { DEFAULT_PER_PAGE } from "./default_per_page"

export default function TableComponent({
  isScroll = true,
  headers,
  data,
  action,
  selected,
  didSelectAt,
  onEdit,
  onDelete,
  rowComponents,
  columnWidth = {},
  defaultColumnWidth = "200px",
  currentPage = 1,
  totalPages = 1,
  onPageChange = () => {},
  perPage = DEFAULT_PER_PAGE,
  pinnedColumns = [],
  isLoading = false,
}: TableComponentProps) {
  const { t } = useLocalization("crud-page", locales)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const tableData = data || []

  // Generate 3 shimmer rows when loading
  const renderShimmerRows = () => {
    return Array.from({ length: 3 }, (_, index) => (
      <ShimmerRow
        key={`shimmer-${index}`}
        rowIndex={index}
        headers={headers}
        pinnedColumns={pinnedColumns}
        columnWidth={columnWidth}
        defaultColumnWidth={defaultColumnWidth}
        isCanShowAction={isCanShowAction}
        action={action}
      />
    ))
  }

  const onSelectRow = (id: string) => {
    if (didSelectAt) {
      didSelectAt(id)
    }
  }

  const isCanShowAction = (): boolean => {
    return action !== null && action !== undefined
  }

  const startDrag = (event: React.MouseEvent) => {
    setIsDragging(true)
    setStartX(event.pageX - (scrollContainerRef.current?.scrollLeft || 0))
  }

  const stopDrag = () => {
    setIsDragging(false)
  }

  const drag = (event: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return
    scrollContainerRef.current.scrollLeft = startX - event.pageX
  }

  return (
    <div
      className={`w-full h-full flex flex-col items-center ${isScroll ? "overflow-y-hidden" : ""}`}
    >
      <div
        className={`relative w-full rounded-b-lg rounded-t-lg h-full bg-[#F5F5F5] text-xs text-black ${isScroll ? "overflow-y-auto" : ""}`}
        ref={scrollContainerRef}
        onMouseDown={startDrag}
        onMouseUp={stopDrag}
        onMouseLeave={stopDrag}
        onMouseMove={drag}
      >
        <table
          className={`min-w-full max-w-full ${isScroll ? "absolute" : ""}`}
        >
          <TableHeader
            headers={headers}
            pinnedColumns={pinnedColumns}
            columnWidth={columnWidth}
            defaultColumnWidth={defaultColumnWidth}
            isCanShowAction={isCanShowAction}
          />
          <tbody>
            {isLoading ? (
              renderShimmerRows()
            ) : tableData.length > 0 ? (
              tableData.map((item, index) => (
                <TableRow
                  key={`tr-${index}`}
                  item={item}
                  index={index}
                  headers={headers}
                  pinnedColumns={pinnedColumns}
                  columnWidth={columnWidth}
                  defaultColumnWidth={defaultColumnWidth}
                  selected={selected}
                  currentPage={currentPage}
                  perPage={perPage}
                  onSelectRow={onSelectRow}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  isCanShowAction={isCanShowAction}
                  action={action}
                  rowComponents={rowComponents}
                />
              ))
            ) : (
              <tr>
                <td
                  colSpan={headers.length + (isCanShowAction() ? 2 : 1)}
                  className="text-center py-8"
                >
                  {t("table_no_data")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
      />
    </div>
  )
}
