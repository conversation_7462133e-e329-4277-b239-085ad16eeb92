"use client"

import { useState } from "react"
import { toast } from "@/hooks/use-toast"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

// Import components
import FileUploadSection from "./bulk-import/FileUploadSection"
import ProgressSection from "./bulk-import/ProgressSection"
import DataPreviewSection from "./bulk-import/DataPreviewSection"
import ImportResultsSection from "./bulk-import/ImportResultsSection"

// Import types and utilities
import {
  BulkImportConfig,
  ValidationError,
  ImportResult,
  parseFile,
  validateFile,
} from "./bulk-import/FileParsingUtils"

// Re-export types for backward compatibility
export type {
  ImportField,
  ValidationError,
  ImportResult,
  BulkImportConfig,
} from "./bulk-import/FileParsingUtils"

interface BulkImportComponentProps {
  config: BulkImportConfig
  onSuccess?: () => void
}

export default function BulkImportComponent({
  config,
  onSuccess,
}: BulkImportComponentProps) {
  const { t } = useLocalization("crud-page", locales)

  // State
  const [uploadedData, setUploadedData] = useState<Record<string, any>[]>([])
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>(
    [],
  )
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const [selectedIds, setSelectedIds] = useState<string[]>([])

  // File upload handler
  const handleFileUpload = async (file: File) => {
    // Validate file
    const validationError = validateFile(file, config)
    if (validationError) {
      toast({
        title: t("error_title"),
        description: validationError,
        variant: "destructive",
      })
      return
    }

    const fileExtension = file.name.split(".").pop()?.toLowerCase()
    setSelectedFile(file)
    setIsProcessing(true)
    setProgress(10)

    try {
      const data = await parseFile(file, fileExtension!, config)
      setProgress(50)

      // Validate data
      const errors = config.validateData(data)
      setValidationErrors(errors)
      setProgress(80)

      setUploadedData(data)
      setProgress(100)

      setSelectedIds(data.map((item) => item.id))

      toast({
        title: t("success_title"),
        description: `File uploaded successfully. ${data.length} records found.`,
        variant: "default",
      })
    } catch (error) {
      console.error("File parsing error:", error)
      toast({
        title: t("error_title"),
        description: "Failed to parse file. Please check the file format.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  // Execute import
  const executeImport = async () => {
    const selectedData = uploadedData.filter((item) =>
      selectedIds.includes(item.id),
    )
    if (selectedData.length === 0) {
      toast({
        title: t("error_title"),
        description: "No data to import",
        variant: "destructive",
      })
      return
    }

    // Check if there are validation errors
    const criticalErrors = validationErrors.filter(
      (error) =>
        config.fields.find((field) => field.name === error.field)?.required,
    )

    if (criticalErrors.length > 0) {
      toast({
        title: t("error_title"),
        description: "Please fix validation errors before importing",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const result = await config.importData(selectedData)
      setImportResult(result)
      setProgress(100)

      toast({
        title: "Import Complete",
        description: `${result.successful}/${result.total} records imported successfully`,
        variant: result.failed > 0 ? "destructive" : "default",
      })

      if (result.successful > 0 && onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Import error:", error)
      toast({
        title: t("error_title"),
        description: "Import failed. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Reset state
  const resetState = () => {
    setUploadedData([])
    setValidationErrors([])
    setImportResult(null)
    setSelectedFile(null)
    setProgress(0)
  }

  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds((prev) => [...prev, id])
    } else {
      setSelectedIds((prev) => prev.filter((selectedId) => selectedId !== id))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(uploadedData.map((item) => item.id))
    } else {
      setSelectedIds([])
    }
  }

  return (
    <div className="space-y-6">
      <FileUploadSection
        config={config}
        onFileUpload={handleFileUpload}
        isProcessing={isProcessing}
      />

      <ProgressSection isProcessing={isProcessing} progress={progress} />

      <DataPreviewSection
        uploadedData={uploadedData}
        validationErrors={validationErrors}
        isProcessing={isProcessing}
        onReset={resetState}
        onImport={executeImport}
        config={config}
        onRowSelect={handleRowSelect}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
      />

      <ImportResultsSection importResult={importResult} />
    </div>
  )
}
