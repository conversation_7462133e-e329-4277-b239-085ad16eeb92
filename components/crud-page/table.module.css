/* Loader animation */
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Sticky positioning support */
.sticky {
  position: -webkit-sticky;
  position: sticky;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Pinned column styles */
.pinnedColumn {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  /* border-right: 2px solid #e5e7eb; */
}

.pinnedColumnHeader {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  font-weight: 600;
}
