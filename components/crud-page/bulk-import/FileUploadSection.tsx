"use client"

import { useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, Download } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import {
  BulkImportConfig,
  convertToCSV,
  downloadFile,
} from "./FileParsingUtils"

interface FileUploadSectionProps {
  config: BulkImportConfig
  onFileUpload: (file: File) => void
  isProcessing: boolean
}

export default function FileUploadSection({
  config,
  onFileUpload,
  isProcessing,
}: FileUploadSectionProps) {
  const { t } = useLocalization("crud-page", locales)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Download template
  const downloadTemplate = () => {
    const templateData = config.generateTemplate()
    const csv = convertToCSV(templateData)
    downloadFile(csv, "import_template.csv", "text/csv")
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      onFileUpload(file)
    }
    event.target.value = "" // Reset file input
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5" />
          {t("bulk_import_title")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* File Upload Area */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <input
            ref={fileInputRef}
            type="file"
            accept={config.supportedFormats.map((f) => `.${f}`).join(",")}
            onChange={handleFileSelect}
            className="hidden"
            disabled={isProcessing}
          />
          <div className="space-y-2">
            <Upload className="w-12 h-12 mx-auto text-gray-400" />
            <div>
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
              >
                {t("bulk_import_choose_file")}
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                {t("bulk_import_supported_formats")}:{" "}
                {config.supportedFormats.join(", ").toUpperCase()}
              </p>
              {config.maxFileSize && (
                <p className="text-xs text-gray-400">
                  {t("bulk_import_max_file_size")}: {config.maxFileSize}MB
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Template Download */}
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">{t("bulk_import_need_template")}</h3>
            <p className="text-sm text-gray-600">
              {t("bulk_import_template_description")}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={downloadTemplate}
            disabled={isProcessing}
          >
            <Download className="w-4 h-4 mr-2" />
            {t("bulk_import_download_template")}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
