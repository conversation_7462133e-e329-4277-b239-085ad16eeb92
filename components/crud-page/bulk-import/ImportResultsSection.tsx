"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { ImportResult } from "./FileParsingUtils"

interface ImportResultsSectionProps {
  importResult: ImportResult | null
}

export default function ImportResultsSection({
  importResult,
}: ImportResultsSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  if (!importResult) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {importResult.failed === 0 ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <AlertCircle className="w-5 h-5 text-yellow-600" />
          )}
          {t("bulk_import_results")}
        </CardTitle>
      </<PERSON><PERSON><PERSON>er>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {importResult.total}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_total")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {importResult.successful}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_successful")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {importResult.failed}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_failed")}
            </div>
          </div>
        </div>

        {/* Error Details */}
        {importResult.errors.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">
              {t("bulk_import_import_errors")}
            </h4>
            <div className="max-h-48 overflow-auto border rounded-lg">
              {importResult.errors.map((error, index) => (
                <div
                  key={index}
                  className="p-3 border-b last:border-b-0 bg-red-50"
                >
                  <div className="flex items-start gap-2">
                    <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-red-900">
                        {t("bulk_import_row")} {error.row + 1},{" "}
                        {t("bulk_import_field")}: {error.field}
                      </div>
                      <div className="text-sm text-red-700">
                        {error.message}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
