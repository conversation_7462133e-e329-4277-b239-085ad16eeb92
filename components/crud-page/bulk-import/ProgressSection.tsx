"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

interface ProgressSectionProps {
  isProcessing: boolean
  progress: number
}

export default function ProgressSection({
  isProcessing,
  progress,
}: ProgressSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  if (!isProcessing) {
    return null
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{t("bulk_import_processing")}</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
      </CardContent>
    </Card>
  )
}
