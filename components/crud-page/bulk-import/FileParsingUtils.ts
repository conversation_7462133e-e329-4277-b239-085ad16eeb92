// File parsing utilities for bulk import

export interface ImportField {
  name: string
  label: string
  required: boolean
  type: "string" | "number" | "date" | "email" | "phone"
  example?: string
}

export interface ValidationError {
  row: number
  field: string
  message: string
}

export interface ImportResult {
  total: number
  successful: number
  failed: number
  errors: ValidationError[]
}

export interface BulkImportConfig {
  fields: ImportField[]
  maxFileSize?: number // MB
  maxRecords?: number
  supportedFormats: string[]
  generateTemplate: () => Record<string, any>[]
  validateData: (data: Record<string, any>[]) => ValidationError[]
  importData: (data: Record<string, any>[]) => Promise<ImportResult>
  headers?: string[]
  transformToTableRow?: (item: Record<string, any>) => {
    id: string
    columns: string[]
  }
}

// Parse file based on format
export const parseFile = async (
  file: File,
  format: string,
  config: BulkImportConfig,
): Promise<Record<string, any>[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        let data: Record<string, any>[] = []

        switch (format) {
          case "csv":
            data = parseCSV(content)
            break
          case "json":
            data = JSON.parse(content)
            if (!Array.isArray(data)) {
              throw new Error("JSON file must contain an array of objects")
            }
            break
          default:
            throw new Error(`Unsupported format: ${format}`)
        }

        // Validate record count
        if (config.maxRecords && data.length > config.maxRecords) {
          throw new Error(
            `Too many records. Maximum allowed: ${config.maxRecords}`,
          )
        }

        resolve(data)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error("Failed to read file"))
    reader.readAsText(file)
  })
}

// Simple CSV parser
export const parseCSV = (content: string): Record<string, any>[] => {
  const lines = content.trim().split("\n")
  if (lines.length < 2)
    throw new Error("CSV file must have at least a header and one data row")

  const parseLine = (line: string): string[] => {
    const result: string[] = []
    let current = ""
    let insideQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        if (insideQuotes && line[i + 1] === '"') {
          current += '"'
          i++
        } else {
          insideQuotes = !insideQuotes
        }
      } else if (char === "," && !insideQuotes) {
        result.push(current.trim())
        current = ""
      } else {
        current += char
      }
    }

    result.push(current.trim())
    return result
  }

  const headers = parseLine(lines[0]).map((h) => h.replace(/^"|"$/g, "").trim())
  const data: Record<string, any>[] = []

  for (let i = 1; i < lines.length; i++) {
    const values = parseLine(lines[i]).map((v) =>
      v.replace(/^"|"$/g, "").replace(/""/g, '"').trim(),
    )

    if (values.length !== headers.length) {
      throw new Error(
        `CSV row ${i + 1} has ${values.length} columns, expected ${headers.length}`,
      )
    }

    const row: Record<string, any> = {}

    headers.forEach((header, index) => {
      let value = values[index] || ""

      // ✅ Auto-fix birthday: from DD/MM/YYYY → YYYY-MM-DD
      if (
        header.toLowerCase() === "birthday" &&
        /^\d{2}\/\d{2}\/\d{4}$/.test(value)
      ) {
        const [day, month, year] = value.split("/")
        value = `${year}-${month}-${day}`
      }

      row[header] = value
    })

    data.push(row)
  }

  return data
}

// Convert data to CSV
export const convertToCSV = (data: Record<string, any>[]): string => {
  if (data.length === 0) return ""

  const headers = Object.keys(data[0])

  const escapeValue = (value: any) => {
    if (value == null) return "" // handles null and undefined
    const str = String(value)
    // Escape double quotes by doubling them, and wrap in quotes
    return `"${str.replace(/"/g, '""')}"`
  }

  const csvContent = [
    headers.join(","), // header row
    ...data.map((row) =>
      headers.map((header) => escapeValue(row[header])).join(","),
    ),
  ].join("\n")

  return csvContent
}

// Download file
export const downloadFile = (
  content: string,
  filename: string,
  mimeType: string,
) => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Validate file before processing
export const validateFile = (
  file: File,
  config: BulkImportConfig,
): string | null => {
  // Validate file size
  if (config.maxFileSize && file.size > config.maxFileSize * 1024 * 1024) {
    return `File size must not exceed ${config.maxFileSize}MB`
  }

  // Validate file format
  const fileExtension = file.name.split(".").pop()?.toLowerCase()
  if (!config.supportedFormats.includes(fileExtension || "")) {
    return `Unsupported file format. Supported: ${config.supportedFormats.join(", ")}`
  }

  return null
}
