"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { TableComponent } from "../index"
import { BulkImportConfig, ValidationError } from "./FileParsingUtils"
import { Checkbox } from "@/components/ui/checkbox"

interface DataPreviewSectionProps {
  config: BulkImportConfig
  uploadedData: Record<string, any>[]
  validationErrors: ValidationError[]
  selectedIds: string[]
  isProcessing: boolean
  onReset: () => void
  onImport: () => void
  onSelectAll: (checked: boolean) => void
  onRowSelect: (id: string, checked: boolean) => void
}

export default function DataPreviewSection({
  config,
  uploaded<PERSON><PERSON>,
  validationErrors,
  selectedIds,
  onSelectAll,
  onRowSelect,
  isProcessing,
  onReset,
  onImport,
}: DataPreviewSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  if (uploadedData.length === 0) {
    return null
  }

  // Prepare data for table display
  const tableHeaders = Object.keys(uploadedData[0])
  const tableData = uploadedData.map((row, index) => ({
    id: index.toString(),
    columns: tableHeaders.map((header) => row[header] || ""),
  }))

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>
            {t("bulk_import_data_preview")} ({uploadedData.length}{" "}
            {t("bulk_import_records")})
          </span>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {selectedIds.length} {t("pagination_of")} {uploadedData.length}{" "}
              {t("bulk_delete_selected")}
            </Badge>
            <Button variant="outline" onClick={onReset} disabled={isProcessing}>
              {t("bulk_import_reset")}
            </Button>
            <Button
              onClick={onImport}
              disabled={isProcessing || selectedIds.length === 0}
            >
              {t("bulk_import_import_data")}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      {!isProcessing && (
        <CardContent>
          {/* Validation Summary */}
          {validationErrors.length > 0 && (
            <div className="mb-4 space-y-2">
              <h4 className="font-medium">
                {t("bulk_import_validation_summary")}
              </h4>
              <div className="flex gap-4">
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800"
                >
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {t("bulk_import_valid")}:{" "}
                  {uploadedData.length - validationErrors.length}
                </Badge>
                <Badge variant="destructive">
                  <XCircle className="w-3 h-3 mr-1" />
                  {t("bulk_import_errors")}: {validationErrors.length}
                </Badge>
              </div>
            </div>
          )}

          {/* Data Table */}
          <div className="border rounded-lg overflow-hidden">
            <TableComponent
              headers={tableHeaders}
              data={tableData.slice(0, 100)} // Show first 100 rows
              action={{ delete: false, edit: false }}
              isScroll={true}
              defaultColumnWidth="150px"
            />
            {uploadedData.length > 100 && (
              <div className="bg-gray-50 px-3 py-2 text-sm text-gray-600 text-center">
                {t("bulk_import_showing_first")} 100{" "}
                {t("bulk_import_records_of")} {uploadedData.length}{" "}
                {t("bulk_import_total")}
              </div>
            )}
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">
                {t("bulk_import_validation_errors")}
              </h4>
              <div className="max-h-48 overflow-auto border rounded-lg">
                {validationErrors.slice(0, 50).map((error, index) => (
                  <div
                    key={index}
                    className="p-3 border-b last:border-b-0 bg-red-50"
                  >
                    <div className="flex items-start gap-2">
                      <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-red-900">
                          {t("bulk_import_row")} {error.row + 1},{" "}
                          {t("bulk_import_field")}: {error.field}
                        </div>
                        <div className="text-sm text-red-700">
                          {error.message}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {validationErrors.length > 50 && (
                  <div className="p-3 text-center text-sm text-gray-600">
                    ... {t("bulk_import_and")} {validationErrors.length - 50}{" "}
                    {t("bulk_import_more_errors")}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}

      <CardContent>
        {/* Select All Checkbox */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            id="select-all"
            checked={
              selectedIds.length === uploadedData.length &&
              uploadedData.length > 0
            }
            onCheckedChange={onSelectAll}
          />
          <label htmlFor="select-all" className="text-sm font-medium">
            {t("bulk_delete_select_all")} ({uploadedData.length}{" "}
            {t("bulk_import_records")})
          </label>
        </div>

        {/* Data Table with Selection */}
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-2 text-left font-medium text-gray-900 w-12">
                  {t("bulk_delete_select")}
                </th>
                {config?.headers?.map((header) => (
                  <th
                    key={header}
                    className="px-3 py-2 text-left font-medium text-gray-900"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {isProcessing ? (
                <tr>
                  <td
                    colSpan={(config?.headers || [])?.length + 1}
                    className="px-3 py-8 text-center text-gray-500"
                  >
                    {t("loading_message")}
                  </td>
                </tr>
              ) : uploadedData.length === 0 ? (
                <tr>
                  <td
                    colSpan={(config?.headers || [])?.length + 1}
                    className="px-3 py-8 text-center text-gray-500"
                  >
                    {t("no_data_message")}
                  </td>
                </tr>
              ) : (
                uploadedData.map((item, idx) => {
                  const row =
                    config?.transformToTableRow &&
                    config?.transformToTableRow(item)
                  const isSelected = selectedIds.includes(item.id)
                  return (
                    <tr
                      key={idx}
                      className={`border-t ${isSelected ? "bg-blue-50" : ""}`}
                    >
                      <td className="px-3 py-2">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) =>
                            onRowSelect(item.id, checked as boolean)
                          }
                        />
                      </td>
                      {row?.columns.map((cell, cellIndex) => (
                        <td key={cellIndex} className="px-3 py-2">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Selection Info */}
        {selectedIds.length > 0 && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-800">
                {selectedIds.length} {t("bulk_import_record")}
                {selectedIds.length !== 1 ? "s" : ""}{" "}
                {t("bulk_update_selected_for_update")}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
