import { StatsConfig, FieldConfig } from "./types"

// Contact data configuration
export const contactStatsConfig: StatsConfig = {
  title: "Contact Statistics",
  dateLabel: "Date Created",

  fields: [
    { key: "id", label: "ID", type: "string", required: true },
    { key: "name", label: "Name", type: "string", required: true },
    { key: "email", label: "Email", type: "string" },
    { key: "phone", label: "Phone", type: "string" },
    { key: "tags", label: "Tags", type: "array" },
    { key: "createdBy", label: "Created By", type: "string" },
    { key: "date", label: "Created Date", type: "date", required: true },
    { key: "status", label: "Status", type: "string" },
  ],

  calculations: {
    // Basic counts
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Status-based (for active/inactive contacts)
    statusField: "status",
    statusOptions: ["Active", "Inactive", "Pending", "Blocked"],
    enableStatusBreakdown: true,

    // Category-based (using tags)
    categoryField: "tags",
    enableCategoryBreakdown: true,
    categoryLabel: "Tag",

    // Assignment-based (who created the contact)
    assignedToField: "createdBy",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Created By",

    // No amount calculations for contacts
    enableAmountCalculations: false,

    // Client count not applicable for contacts
    enableClientCount: false,
  },
}

// Sales data configuration
export const salesStatsConfig: StatsConfig = {
  title: "Sales Statistics",
  dateLabel: "Sale Date",

  fields: [
    { key: "id", label: "ID", type: "string", required: true },
    { key: "amount", label: "Amount", type: "number", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "product", label: "Product", type: "string" },
    { key: "category", label: "Category", type: "string" },
    { key: "salesRep", label: "Sales Rep", type: "string" },
    { key: "customerId", label: "Customer ID", type: "string" },
    { key: "date", label: "Sale Date", type: "date", required: true },
  ],

  calculations: {
    // Basic counts
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Status-based (for sale pipeline)
    statusField: "status",
    statusOptions: [
      "Completed",
      "Processing",
      "Shipped",
      "Cancelled",
      "Refunded",
    ],
    enableStatusBreakdown: true,

    // Category-based (product categories)
    categoryField: "category",
    enableCategoryBreakdown: true,
    categoryLabel: "Product Category",

    // Assignment-based (sales rep)
    assignedToField: "salesRep",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Sales Rep",

    // Amount calculations (revenue)
    enableAmountCalculations: true,
    amountField: "amount",
    amountLabel: "Revenue",
    amountPrefix: "$",

    // Client count (unique customers)
    enableClientCount: true,
    clientField: "customerId",
    clientLabel: "Customer",
  },
}

// Task/Project data configuration
export const taskStatsConfig: StatsConfig = {
  title: "Task Statistics",
  dateLabel: "Created Date",

  fields: [
    { key: "id", label: "ID", type: "string", required: true },
    { key: "title", label: "Title", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "priority", label: "Priority", type: "string" },
    { key: "assignedTo", label: "Assigned To", type: "string" },
    { key: "projectId", label: "Project ID", type: "string" },
    { key: "estimatedHours", label: "Estimated Hours", type: "number" },
    { key: "date", label: "Created Date", type: "date", required: true },
  ],

  calculations: {
    // Basic counts
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Status-based (task status)
    statusField: "status",
    statusOptions: ["To Do", "In Progress", "Review", "Completed", "Cancelled"],
    enableStatusBreakdown: true,

    // Category-based (priority levels)
    categoryField: "priority",
    enableCategoryBreakdown: true,
    categoryLabel: "Priority",

    // Assignment-based
    assignedToField: "assignedTo",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Assignee",

    // Amount calculations (estimated hours)
    enableAmountCalculations: true,
    amountField: "estimatedHours",
    amountLabel: "Hours",
    amountPrefix: "",

    // Client count (unique projects)
    enableClientCount: true,
    clientField: "projectId",
    clientLabel: "Project",
  },
}

// Support ticket configuration
export const supportStatsConfig: StatsConfig = {
  title: "Support Statistics",
  dateLabel: "Created Date",

  fields: [
    { key: "id", label: "ID", type: "string", required: true },
    { key: "subject", label: "Subject", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "priority", label: "Priority", type: "string" },
    { key: "assignedTo", label: "Assigned To", type: "string" },
    { key: "customerId", label: "Customer ID", type: "string" },
    { key: "category", label: "Category", type: "string" },
    { key: "date", label: "Created Date", type: "date", required: true },
  ],

  calculations: {
    // Basic counts
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Status-based
    statusField: "status",
    statusOptions: ["Open", "In Progress", "Waiting", "Resolved", "Closed"],
    enableStatusBreakdown: true,

    // Category-based (ticket categories)
    categoryField: "category",
    enableCategoryBreakdown: true,
    categoryLabel: "Category",

    // Assignment-based
    assignedToField: "assignedTo",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Support Agent",

    // No amount calculations for support tickets
    enableAmountCalculations: false,

    // Client count (unique customers)
    enableClientCount: true,
    clientField: "customerId",
    clientLabel: "Customer",
  },
}

// Generic configuration builder
export const createCustomStatsConfig = (
  title: string,
  fields: FieldConfig[],
  overrides: Partial<StatsConfig["calculations"]> = {},
): StatsConfig => {
  return {
    title,
    dateLabel: "Date",
    fields,
    calculations: {
      enableTotalCount: true,
      enableTodayCount: true,
      enableWeekCount: true,
      enableMonthCount: true,
      enableStatusBreakdown: false,
      enableCategoryBreakdown: false,
      enableAssigneeBreakdown: false,
      enableAmountCalculations: false,
      enableClientCount: false,
      ...overrides,
    },
  }
}

// Export all presets
export const statsPresets = {
  contact: contactStatsConfig,
  sales: salesStatsConfig,
  task: taskStatsConfig,
  support: supportStatsConfig,
}

export type StatsPresetType = keyof typeof statsPresets
