// Example configurations for different data types using the modular stats system

import { StatsConfig, createCustomStatsConfig } from "./presets"

// Example 1: E-commerce Orders Configuration
export const ecommerceOrdersConfig: StatsConfig = {
  title: "Order Analytics",
  dateLabel: "Order Date",

  fields: [
    { key: "id", label: "Order ID", type: "string", required: true },
    { key: "amount", label: "Order Amount", type: "number", required: true },
    { key: "status", label: "Order Status", type: "string", required: true },
    { key: "product", label: "Product", type: "string" },
    { key: "category", label: "Product Category", type: "string" },
    { key: "customerId", label: "Customer ID", type: "string" },
    { key: "salesRep", label: "Sales Representative", type: "string" },
    { key: "date", label: "Order Date", type: "date", required: true },
  ],

  calculations: {
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Order status tracking
    statusField: "status",
    statusOptions: [
      "Pending",
      "Processing",
      "Shipped",
      "Delivered",
      "Cancelled",
      "Refunded",
    ],
    enableStatusBreakdown: true,

    // Product category analysis
    categoryField: "category",
    enableCategoryBreakdown: true,
    categoryLabel: "Product Category",

    // Sales rep performance
    assignedToField: "salesRep",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Sales Rep",

    // Revenue calculations
    enableAmountCalculations: true,
    amountField: "amount",
    amountLabel: "Revenue",
    amountPrefix: "$",

    // Customer analysis
    enableClientCount: true,
    clientField: "customerId",
    clientLabel: "Customer",
  },
}

// Example 2: Customer Support Tickets Configuration
export const supportTicketsConfig: StatsConfig = {
  title: "Support Ticket Analytics",
  dateLabel: "Created Date",

  fields: [
    { key: "id", label: "Ticket ID", type: "string", required: true },
    { key: "subject", label: "Subject", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "priority", label: "Priority", type: "string" },
    { key: "category", label: "Category", type: "string" },
    { key: "assignedTo", label: "Assigned Agent", type: "string" },
    { key: "customerId", label: "Customer ID", type: "string" },
    { key: "resolutionTime", label: "Resolution Time (hours)", type: "number" },
    { key: "date", label: "Created Date", type: "date", required: true },
  ],

  calculations: {
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Ticket status tracking
    statusField: "status",
    statusOptions: [
      "Open",
      "In Progress",
      "Waiting for Customer",
      "Resolved",
      "Closed",
    ],
    enableStatusBreakdown: true,

    // Issue category analysis
    categoryField: "category",
    enableCategoryBreakdown: true,
    categoryLabel: "Issue Category",

    // Agent performance
    assignedToField: "assignedTo",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Support Agent",

    // Resolution time tracking
    enableAmountCalculations: true,
    amountField: "resolutionTime",
    amountLabel: "Avg Resolution Time",
    amountPrefix: "",

    // Customer analysis
    enableClientCount: true,
    clientField: "customerId",
    clientLabel: "Customer",
  },
}

// Example 3: Project Management Tasks Configuration
export const projectTasksConfig: StatsConfig = {
  title: "Project Task Analytics",
  dateLabel: "Created Date",

  fields: [
    { key: "id", label: "Task ID", type: "string", required: true },
    { key: "title", label: "Task Title", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "priority", label: "Priority", type: "string" },
    { key: "assignedTo", label: "Assignee", type: "string" },
    { key: "projectId", label: "Project ID", type: "string" },
    { key: "estimatedHours", label: "Estimated Hours", type: "number" },
    { key: "actualHours", label: "Actual Hours", type: "number" },
    { key: "date", label: "Created Date", type: "date", required: true },
  ],

  calculations: {
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Task status tracking
    statusField: "status",
    statusOptions: [
      "To Do",
      "In Progress",
      "Code Review",
      "Testing",
      "Done",
      "Blocked",
    ],
    enableStatusBreakdown: true,

    // Priority analysis
    categoryField: "priority",
    enableCategoryBreakdown: true,
    categoryLabel: "Priority Level",

    // Team member performance
    assignedToField: "assignedTo",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Team Member",

    // Time tracking
    enableAmountCalculations: true,
    amountField: "actualHours",
    amountLabel: "Total Hours",
    amountPrefix: "",

    // Project analysis
    enableClientCount: true,
    clientField: "projectId",
    clientLabel: "Project",
  },
}

// Example 4: Marketing Campaigns Configuration
export const marketingCampaignsConfig: StatsConfig = {
  title: "Campaign Performance",
  dateLabel: "Launch Date",

  fields: [
    { key: "id", label: "Campaign ID", type: "string", required: true },
    { key: "name", label: "Campaign Name", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "channel", label: "Marketing Channel", type: "string" },
    { key: "budget", label: "Budget", type: "number" },
    { key: "spent", label: "Amount Spent", type: "number" },
    { key: "leads", label: "Leads Generated", type: "number" },
    { key: "manager", label: "Campaign Manager", type: "string" },
    { key: "date", label: "Launch Date", type: "date", required: true },
  ],

  calculations: {
    enableTotalCount: true,
    enableTodayCount: true,
    enableWeekCount: true,
    enableMonthCount: true,

    // Campaign status
    statusField: "status",
    statusOptions: ["Draft", "Active", "Paused", "Completed", "Cancelled"],
    enableStatusBreakdown: true,

    // Marketing channel analysis
    categoryField: "channel",
    enableCategoryBreakdown: true,
    categoryLabel: "Marketing Channel",

    // Campaign manager performance
    assignedToField: "manager",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Campaign Manager",

    // Budget tracking
    enableAmountCalculations: true,
    amountField: "spent",
    amountLabel: "Total Spent",
    amountPrefix: "$",

    // No client count for campaigns
    enableClientCount: false,
  },
}

// Example 5: Simple Blog Posts Configuration (minimal setup)
export const blogPostsConfig = createCustomStatsConfig(
  "Blog Analytics",
  [
    { key: "id", label: "Post ID", type: "string", required: true },
    { key: "title", label: "Title", type: "string", required: true },
    { key: "status", label: "Status", type: "string", required: true },
    { key: "category", label: "Category", type: "string" },
    { key: "author", label: "Author", type: "string" },
    { key: "views", label: "Views", type: "number" },
    { key: "date", label: "Published Date", type: "date", required: true },
  ],
  {
    // Only enable basic features for blog posts
    statusField: "status",
    statusOptions: ["Draft", "Published", "Archived"],
    enableStatusBreakdown: true,

    categoryField: "category",
    enableCategoryBreakdown: true,
    categoryLabel: "Post Category",

    assignedToField: "author",
    enableAssigneeBreakdown: true,
    assignedToLabel: "Author",

    enableAmountCalculations: true,
    amountField: "views",
    amountLabel: "Total Views",
    amountPrefix: "",
  },
)

// Export all example configurations
export const exampleConfigs = {
  ecommerce: ecommerceOrdersConfig,
  support: supportTicketsConfig,
  projects: projectTasksConfig,
  marketing: marketingCampaignsConfig,
  blog: blogPostsConfig,
}

export type ExampleConfigType = keyof typeof exampleConfigs
