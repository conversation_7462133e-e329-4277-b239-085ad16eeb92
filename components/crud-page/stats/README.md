# Generic Stats System

This document explains how to use the new generic stats system that works with any data type.

## Overview

The new stats system uses a generic `StatsResponse` interface that allows the backend to define exactly what statistics to display and how they should appear.

## StatsResponse Interface

```typescript
interface StatItem {
  id: string // Unique identifier
  title: string // Display title
  value: string | number // Main value to display
  label?: string // Subtitle/description
  description?: string // Additional description
  icon?: string // Icon identifier
  cardColor?: string // Background color class
  textColor?: string // Text color class
  trend?: {
    value: string | number
    direction: "up" | "down" | "neutral"
    label?: string
  }
  metadata?: Record<string, any>
}

interface StatBreakdown {
  id: string
  title: string
  items: Array<{
    label: string
    value: number
    percentage?: number
    color?: string
  }>
}

interface StatsResponse {
  stats: StatItem[] // Main stat cards
  breakdowns?: StatBreakdown[] // Optional breakdown sections
  summary?: {
    total: number
    period: string
    lastUpdated?: string
  }
  metadata?: Record<string, any>
}
```

## Example Backend Response

### Contacts Stats Example

```json
{
  "stats": [
    {
      "id": "total_contacts",
      "title": "Total Contacts",
      "value": 1250,
      "label": "125 this month",
      "description": "All contacts in the system",
      "icon": "users",
      "cardColor": "bg-blue-50",
      "textColor": "text-blue-900",
      "trend": {
        "value": "+12%",
        "direction": "up",
        "label": "vs last month"
      }
    },
    {
      "id": "active_contacts",
      "title": "Active Contacts",
      "value": 1180,
      "label": "94.4% of total",
      "cardColor": "bg-green-50",
      "textColor": "text-green-900"
    },
    {
      "id": "contacts_with_email",
      "title": "With Email",
      "value": 980,
      "label": "78.4% of total",
      "cardColor": "bg-purple-50",
      "textColor": "text-purple-900"
    },
    {
      "id": "recent_contacts",
      "title": "Added Today",
      "value": 15,
      "label": "New contacts",
      "cardColor": "bg-orange-50",
      "textColor": "text-orange-900"
    }
  ],
  "breakdowns": [
    {
      "id": "status_breakdown",
      "title": "Contact Status",
      "items": [
        {
          "label": "Active",
          "value": 1180,
          "percentage": 94.4,
          "color": "#10B981"
        },
        {
          "label": "Inactive",
          "value": 45,
          "percentage": 3.6,
          "color": "#F59E0B"
        },
        {
          "label": "Blocked",
          "value": 25,
          "percentage": 2.0,
          "color": "#EF4444"
        }
      ]
    },
    {
      "id": "tag_breakdown",
      "title": "Top Tags",
      "items": [
        {
          "label": "Customer",
          "value": 450,
          "percentage": 36.0,
          "color": "#3B82F6"
        },
        {
          "label": "Lead",
          "value": 320,
          "percentage": 25.6,
          "color": "#8B5CF6"
        },
        {
          "label": "Partner",
          "value": 180,
          "percentage": 14.4,
          "color": "#06B6D4"
        }
      ]
    }
  ],
  "summary": {
    "total": 1250,
    "period": "all time",
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

### Sales Stats Example

```json
{
  "stats": [
    {
      "id": "total_revenue",
      "title": "Total Revenue",
      "value": "$125,430",
      "label": "$15,200 this month",
      "icon": "dollar-sign",
      "cardColor": "bg-green-50",
      "textColor": "text-green-900",
      "trend": {
        "value": "+18%",
        "direction": "up",
        "label": "vs last month"
      }
    },
    {
      "id": "total_orders",
      "title": "Total Orders",
      "value": 342,
      "label": "45 this month",
      "cardColor": "bg-blue-50",
      "textColor": "text-blue-900"
    },
    {
      "id": "avg_order_value",
      "title": "Avg Order Value",
      "value": "$367",
      "label": "Per order",
      "cardColor": "bg-purple-50",
      "textColor": "text-purple-900"
    },
    {
      "id": "conversion_rate",
      "title": "Conversion Rate",
      "value": "12.5%",
      "label": "Leads to sales",
      "cardColor": "bg-orange-50",
      "textColor": "text-orange-900"
    }
  ],
  "breakdowns": [
    {
      "id": "order_status",
      "title": "Order Status",
      "items": [
        {
          "label": "Completed",
          "value": 280,
          "percentage": 81.9,
          "color": "#10B981"
        },
        {
          "label": "Processing",
          "value": 35,
          "percentage": 10.2,
          "color": "#F59E0B"
        },
        {
          "label": "Shipped",
          "value": 20,
          "percentage": 5.8,
          "color": "#3B82F6"
        },
        {
          "label": "Cancelled",
          "value": 7,
          "percentage": 2.1,
          "color": "#EF4444"
        }
      ]
    }
  ],
  "summary": {
    "total": 342,
    "period": "all time",
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

## Frontend Usage

### 1. Enable Generic Stats in DataPageEnhanced

```typescript
const config: DataPageEnhancedConfig<MyDataType> = {
  // ... other config

  // Enable the generic stats system
  useGenericStats: true,

  // Implement fetchStats to return StatsResponse
  fetchStats: async (params) => {
    const response = await MyAPI.Stats(params).request()
    return response // Should be StatsResponse format
  },
}
```

### 2. Backend Implementation

Your backend endpoint should return data in the `StatsResponse` format:

```typescript
// Example API endpoint: GET /api/v1/contacts/stats
app.get("/api/v1/contacts/stats", async (req, res) => {
  const stats = await calculateContactStats(req.query)

  const response: StatsResponse = {
    stats: [
      {
        id: "total_contacts",
        title: "Total Contacts",
        value: stats.total,
        label: `${stats.thisMonth} this month`,
        cardColor: "bg-blue-50",
        textColor: "text-blue-900",
      },
      // ... more stat items
    ],
    breakdowns: [
      {
        id: "status_breakdown",
        title: "Contact Status",
        items: stats.statusBreakdown.map((item) => ({
          label: item.status,
          value: item.count,
          percentage: item.percentage,
          color: getStatusColor(item.status),
        })),
      },
    ],
    summary: {
      total: stats.total,
      period: "all time",
      lastUpdated: new Date().toISOString(),
    },
  }

  res.json(response)
})
```

## Benefits

1. **Completely Generic** - Works with any data type
2. **Backend Controlled** - Backend decides what stats to show and how
3. **Flexible Styling** - Each stat card can have custom colors and styling
4. **Rich Data** - Supports trends, breakdowns, and metadata
5. **Type Safe** - Full TypeScript support
6. **Consistent UI** - Same component renders all stats regardless of data type

## Migration from Legacy Stats

If you have existing legacy stats configuration, you can migrate by:

1. Set `useGenericStats: true` in your config
2. Update your `fetchStats` function to return `StatsResponse`
3. Remove the old `statsConfig` object
4. Update your backend to return the new format

The system will automatically use the new generic stats when `useGenericStats` is enabled.
