import {
  BaseDataItem,
  CalculatedStats,
  StatCalculationConfig,
  TimePeriod,
} from "./types"

// Time period filtering utilities
export const getFilteredData = (
  data: BaseDataItem[],
  period: TimePeriod,
  startDate?: string,
  endDate?: string,
): BaseDataItem[] => {
  if (!data || data.length === 0) return []

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (period) {
    case "today":
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return (
          itemDate >= today &&
          itemDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
        )
      })

    case "yesterday":
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= yesterday && itemDate < today
      })

    case "this_week":
      const thisWeekStart = new Date(today)
      thisWeekStart.setDate(today.getDate() - today.getDay())
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= thisWeekStart
      })

    case "last_week":
      const lastWeekEnd = new Date(today)
      lastWeekEnd.setDate(today.getDate() - today.getDay())
      const lastWeekStart = new Date(lastWeekEnd)
      lastWeekStart.setDate(lastWeekEnd.getDate() - 7)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= lastWeekStart && itemDate < lastWeekEnd
      })

    case "this_month":
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= thisMonthStart
      })

    case "last_month":
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 1)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= lastMonthStart && itemDate < lastMonthEnd
      })

    case "this_year":
      const thisYearStart = new Date(now.getFullYear(), 0, 1)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= thisYearStart
      })

    case "last_year":
      const lastYearStart = new Date(now.getFullYear() - 1, 0, 1)
      const lastYearEnd = new Date(now.getFullYear(), 0, 1)
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= lastYearStart && itemDate < lastYearEnd
      })

    case "custom":
      if (!startDate || !endDate) return data
      const customStart = new Date(startDate)
      const customEnd = new Date(endDate)
      customEnd.setHours(23, 59, 59, 999) // Include the end date
      return data.filter((item) => {
        const itemDate = new Date(item.date)
        return itemDate >= customStart && itemDate <= customEnd
      })

    case "all":
    default:
      return data
  }
}

// Calculate stats based on configuration
export const calculateStats = (
  data: BaseDataItem[],
  config: StatCalculationConfig,
): CalculatedStats => {
  if (!data || data.length === 0) {
    return {
      total: 0,
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
      statusCounts: {},
      categoryCounts: {},
      assigneeCounts: {},
      uniqueClients: 0,
    }
  }

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const thisWeekStart = new Date(today)
  thisWeekStart.setDate(today.getDate() - today.getDay())
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)

  // Time-based filtering
  const todayData = data.filter((item) => {
    const itemDate = new Date(item.date)
    return (
      itemDate >= today &&
      itemDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
    )
  })

  const thisWeekData = data.filter((item) => {
    const itemDate = new Date(item.date)
    return itemDate >= thisWeekStart
  })

  const thisMonthData = data.filter((item) => {
    const itemDate = new Date(item.date)
    return itemDate >= thisMonthStart
  })

  // Status counts
  const statusCounts: Record<string, number> = {}
  if (config.enableStatusBreakdown && config.statusField) {
    data.forEach((item) => {
      const status = item[config.statusField!]
      if (status) {
        statusCounts[status] = (statusCounts[status] || 0) + 1
      }
    })
  }

  // Category counts
  const categoryCounts: Record<string, number> = {}
  let topCategory: { name: string; count: number } | undefined
  if (config.enableCategoryBreakdown && config.categoryField) {
    data.forEach((item) => {
      const category = item[config.categoryField!]
      if (category) {
        // Handle both string and array categories
        const categories = Array.isArray(category) ? category : [category]
        categories.forEach((cat) => {
          if (cat) {
            categoryCounts[cat] = (categoryCounts[cat] || 0) + 1
          }
        })
      }
    })

    const topCategoryEntry = Object.entries(categoryCounts).sort(
      ([, a], [, b]) => b - a,
    )[0]
    if (topCategoryEntry) {
      topCategory = { name: topCategoryEntry[0], count: topCategoryEntry[1] }
    }
  }

  // Assignee counts
  const assigneeCounts: Record<string, number> = {}
  let topAssignee: { name: string; count: number } | undefined
  if (config.enableAssigneeBreakdown && config.assignedToField) {
    data.forEach((item) => {
      const assignee = item[config.assignedToField!]
      if (assignee) {
        assigneeCounts[assignee] = (assigneeCounts[assignee] || 0) + 1
      }
    })

    const topAssigneeEntry = Object.entries(assigneeCounts).sort(
      ([, a], [, b]) => b - a,
    )[0]
    if (topAssigneeEntry) {
      topAssignee = { name: topAssigneeEntry[0], count: topAssigneeEntry[1] }
    }
  }

  // Amount calculations
  let totalAmount: number | undefined
  let thisWeekAmount: number | undefined
  let thisMonthAmount: number | undefined
  if (config.enableAmountCalculations && config.amountField) {
    totalAmount = data.reduce((sum, item) => {
      const amount = item[config.amountField!]
      if (amount) {
        const numericAmount =
          typeof amount === "number"
            ? amount
            : parseFloat(amount.toString().replace(/[^\d.-]/g, ""))
        return sum + (isNaN(numericAmount) ? 0 : numericAmount)
      }
      return sum
    }, 0)

    thisWeekAmount = thisWeekData.reduce((sum, item) => {
      const amount = item[config.amountField!]
      if (amount) {
        const numericAmount =
          typeof amount === "number"
            ? amount
            : parseFloat(amount.toString().replace(/[^\d.-]/g, ""))
        return sum + (isNaN(numericAmount) ? 0 : numericAmount)
      }
      return sum
    }, 0)

    thisMonthAmount = thisMonthData.reduce((sum, item) => {
      const amount = item[config.amountField!]
      if (amount) {
        const numericAmount =
          typeof amount === "number"
            ? amount
            : parseFloat(amount.toString().replace(/[^\d.-]/g, ""))
        return sum + (isNaN(numericAmount) ? 0 : numericAmount)
      }
      return sum
    }, 0)
  }

  // Unique clients count
  let uniqueClients: number | undefined
  if (config.enableClientCount && config.clientField) {
    const clientIds = new Set(
      data.map((item) => item[config.clientField!]).filter(Boolean),
    )
    uniqueClients = clientIds.size
  }

  return {
    total: data.length,
    today: todayData.length,
    thisWeek: thisWeekData.length,
    thisMonth: thisMonthData.length,
    statusCounts,
    topCategory,
    categoryCounts,
    topAssignee,
    assigneeCounts,
    totalAmount,
    thisWeekAmount,
    thisMonthAmount,
    uniqueClients,
  }
}
