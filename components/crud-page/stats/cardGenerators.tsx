import {
  Activity,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Users,
  DollarSign,
  TrendingUp,
  Tag,
  UserCheck,
} from "lucide-react"
import { StatCard, CalculatedStats, StatCalculationConfig } from "./types"

// Generate basic count cards
export const generateBasicCountCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  // Total count card
  if (config.enableTotalCount !== false) {
    cards.push({
      title: "Total Items",
      value: stats.total,
      subtitle: `${stats.thisMonth} this month`,
      icon: <Activity className="w-6 h-6" />,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      trend: {
        value: `+${stats.thisWeek} this week`,
        isPositive: true,
      },
    })
  }

  // Today count card
  if (config.enableTodayCount !== false) {
    cards.push({
      title: "Today",
      value: stats.today,
      subtitle: "Items for today",
      icon: <Calendar className="w-6 h-6" />,
      color: "text-green-600",
      bgColor: "bg-green-50",
    })
  }

  return cards
}

// Generate status-based cards
export const generateStatusCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  if (!config.enableStatusBreakdown || !config.statusOptions) {
    return cards
  }

  const statusIcons = [
    {
      icon: <CheckCircle className="w-6 h-6" />,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
    },
    {
      icon: <Clock className="w-6 h-6" />,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      icon: <AlertCircle className="w-6 h-6" />,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      icon: <XCircle className="w-6 h-6" />,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
    {
      icon: <Activity className="w-6 h-6" />,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
  ]

  config.statusOptions.forEach((status, index) => {
    const count = stats.statusCounts[status] || 0
    const iconConfig = statusIcons[index % statusIcons.length]
    const percentage =
      stats.total > 0 ? ((count / stats.total) * 100).toFixed(1) : "0"

    cards.push({
      title: status,
      value: count,
      subtitle: `${percentage}% of total`,
      icon: iconConfig.icon,
      color: iconConfig.color,
      bgColor: iconConfig.bgColor,
    })
  })

  return cards
}

// Generate amount/revenue cards
export const generateAmountCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  if (!config.enableAmountCalculations || stats.totalAmount === undefined) {
    return cards
  }

  const prefix = config.amountPrefix || "$"
  const label = config.amountLabel || "Revenue"

  // Total amount card
  cards.push({
    title: `Total ${label}`,
    value: `${prefix}${stats.totalAmount.toLocaleString()}`,
    subtitle: stats.thisMonthAmount
      ? `${prefix}${stats.thisMonthAmount.toLocaleString()} this month`
      : "",
    icon: <DollarSign className="w-6 h-6" />,
    color: "text-green-600",
    bgColor: "bg-green-50",
    trend: stats.thisWeekAmount
      ? {
          value: `${prefix}${stats.thisWeekAmount.toLocaleString()} this week`,
          isPositive: true,
        }
      : undefined,
  })

  return cards
}

// Generate client/customer cards
export const generateClientCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  if (!config.enableClientCount || stats.uniqueClients === undefined) {
    return cards
  }

  const label = config.clientLabel || "Clients"

  cards.push({
    title: `Unique ${label}`,
    value: stats.uniqueClients,
    subtitle: `${stats.total} total interactions`,
    icon: <Users className="w-6 h-6" />,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
  })

  return cards
}

// Generate category cards
export const generateCategoryCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  if (!config.enableCategoryBreakdown || !stats.topCategory) {
    return cards
  }

  const label = config.categoryLabel || "Category"

  cards.push({
    title: `Top ${label}`,
    value: stats.topCategory.name,
    subtitle: `${stats.topCategory.count} items`,
    icon: <Tag className="w-6 h-6" />,
    color: "text-indigo-600",
    bgColor: "bg-indigo-50",
  })

  return cards
}

// Generate assignee cards
export const generateAssigneeCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
): StatCard[] => {
  const cards: StatCard[] = []

  if (!config.enableAssigneeBreakdown || !stats.topAssignee) {
    return cards
  }

  const label = config.assignedToLabel || "Assignee"

  cards.push({
    title: `Most Active ${label}`,
    value: stats.topAssignee.name,
    subtitle: `${stats.topAssignee.count} items`,
    icon: <UserCheck className="w-6 h-6" />,
    color: "text-cyan-600",
    bgColor: "bg-cyan-50",
  })

  return cards
}

// Main function to generate all stat cards
export const generateStatCards = (
  stats: CalculatedStats,
  config: StatCalculationConfig,
  customCards?: StatCard[],
): StatCard[] => {
  const cards: StatCard[] = []

  // Add basic count cards
  cards.push(...generateBasicCountCards(stats, config))

  // Add status cards
  cards.push(...generateStatusCards(stats, config))

  // Add amount cards
  cards.push(...generateAmountCards(stats, config))

  // Add client cards
  cards.push(...generateClientCards(stats, config))

  // Add category cards
  cards.push(...generateCategoryCards(stats, config))

  // Add assignee cards
  cards.push(...generateAssigneeCards(stats, config))

  // Add custom cards
  if (customCards) {
    cards.push(...customCards)
  }

  // Filter out cards that are explicitly hidden
  return cards.filter((card) => card.visible !== false)
}
