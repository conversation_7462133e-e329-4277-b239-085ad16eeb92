// Types for DataBulkPage component

// Supported file formats
export type SupportedFormat = "csv" | "json" | "xlsx" | "xml"

// Bulk operation types
export type BulkOperationType = "import" | "update" | "delete"

// Validation result for each row
export interface ValidationResult {
  rowIndex: number
  isValid: boolean
  errors: string[]
  warnings: string[]
  data: Record<string, any>
}

// Bulk operation result
export interface BulkOperationResult {
  total: number
  successful: number
  failed: number
  errors: Array<{
    rowIndex: number
    error: string
    data?: Record<string, any>
  }>
}

// Field mapping for import
export interface FieldMapping {
  sourceField: string
  targetField: string
  required: boolean
  transform?: (value: any) => any
  validate?: (value: any) => string | null
}

// Configuration interface
export interface DataBulkConfig {
  title: string
  subtitle?: string

  // Supported operations
  supportedOperations: BulkOperationType[]
  supportedFormats: SupportedFormat[]

  // Field configuration
  fields: Array<{
    name: string
    label: string
    required: boolean
    type: "string" | "number" | "date" | "boolean" | "email" | "phone"
    example?: string
  }>

  // Data operations
  bulkImport?: (data: Record<string, any>[]) => Promise<BulkOperationResult>
  bulkUpdate?: (data: Record<string, any>[]) => Promise<BulkOperationResult>
  bulkDelete?: (ids: string[]) => Promise<BulkOperationResult>

  // Template and validation
  generateTemplate?: () => Record<string, any>[]
  validateData?: (data: Record<string, any>[]) => ValidationResult[]

  // Navigation
  backRoute: string

  // Customization
  maxFileSize?: number // in MB
  maxRecords?: number
  allowDuplicates?: boolean
  requireIdForUpdate?: boolean
  requireIdForDelete?: boolean
}
