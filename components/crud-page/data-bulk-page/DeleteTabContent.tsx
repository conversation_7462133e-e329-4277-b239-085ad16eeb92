"use client"

import { useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { TabsContent } from "@/components/ui/tabs"
import { Trash2, Upload, AlertTriangle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { DataBulkConfig } from "./types"

interface DeleteTabContentProps {
  config: DataBulkConfig
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void
  isProcessing: boolean
}

export default function DeleteTabContent({
  config,
  onFileUpload,
  isProcessing,
}: DeleteTabContentProps) {
  const { t } = useLocalization("crud-page", locales)
  const fileInputRef = useRef<HTMLInputElement>(null)

  return (
    <TabsContent value="delete" className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="w-5 h-5" />
            {t("data_bulk_delete_data")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-900">
                  {t("bulk_delete_warning")}
                </h4>
                <p className="text-sm text-red-700 mt-1">
                  {config.requireIdForDelete
                    ? t("data_bulk_delete_id_required")
                    : t("data_bulk_delete_warning_description")}
                </p>
              </div>
            </div>
          </div>

          {/* File Upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              ref={fileInputRef}
              type="file"
              accept={config.supportedFormats.map((f) => `.${f}`).join(",")}
              onChange={onFileUpload}
              className="hidden"
              disabled={isProcessing}
            />
            <div className="space-y-2">
              <Upload className="w-12 h-12 mx-auto text-gray-400" />
              <div>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                >
                  {t("bulk_import_choose_file")}
                </Button>
                <p className="text-sm text-gray-500 mt-2">
                  {t("bulk_import_supported_formats")}:{" "}
                  {config.supportedFormats.join(", ").toUpperCase()}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
}
