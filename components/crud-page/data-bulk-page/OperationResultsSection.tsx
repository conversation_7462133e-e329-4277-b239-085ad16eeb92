"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { BulkOperationResult } from "./types"

interface OperationResultsSectionProps {
  operationResult: BulkOperationResult | null
}

export default function OperationResultsSection({
  operationResult,
}: OperationResultsSectionProps) {
  const { t } = useLocalization("crud-page", locales)

  if (!operationResult) {
    return null
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {operationResult.failed === 0 ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <AlertCircle className="w-5 h-5 text-yellow-600" />
          )}
          {t("data_bulk_operation_results")}
        </<PERSON><PERSON><PERSON><PERSON>>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {operationResult.total}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_total")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {operationResult.successful}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_successful")}
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {operationResult.failed}
            </div>
            <div className="text-sm text-gray-600">
              {t("bulk_import_failed")}
            </div>
          </div>
        </div>

        {/* Error Details */}
        {operationResult.errors.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">{t("data_bulk_error_details")}</h4>
            <div className="max-h-48 overflow-auto border rounded-lg">
              {operationResult.errors.map((error, index) => (
                <div
                  key={index}
                  className="p-3 border-b last:border-b-0 bg-red-50"
                >
                  <div className="flex items-start gap-2">
                    <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-red-900">
                        {t("bulk_import_row")} {error.rowIndex + 1}
                      </div>
                      <div className="text-sm text-red-700">{error.error}</div>
                      {error.data && (
                        <div className="text-xs text-red-600 mt-1">
                          Data: {JSON.stringify(error.data)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
