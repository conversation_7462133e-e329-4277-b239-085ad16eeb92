"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useLocalization } from "@/localization/functions/client"
import { workflowLocales } from "./locales"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  CheckCircle,
  XCircle,
  Clock,
  MessageSquare,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  User,
  Bot,
  AlertTriangle,
} from "lucide-react"
import { AiWorkflowExecution } from "@/lib/repositories/aiWorkflowExecutions"

export interface WorkflowExecutionsProps {
  executions: AiWorkflowExecution[]
  searchTerm: string
  statusFilter: string
  onSearchTermChange?: (term: string) => void
  onStatusFilterChange?: (status: string) => void
}

export function WorkflowExecutions({
  executions,
  searchTerm,
  statusFilter,
  onSearchTermChange,
  onStatusFilterChange,
}: WorkflowExecutionsProps) {
  const [expandedExecution, setExpandedExecution] = useState<string | null>(
    null,
  )
  const { t } = useLocalization("workflow", workflowLocales)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "failure":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "in-progress":
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      resolved: "bg-green-100 text-green-800",
      escalated: "bg-yellow-100 text-yellow-800",
      failed: "bg-red-100 text-red-800",
    } as const

    const labels: Record<string, string> = {
      resolved: t("status_resolved"),
      escalated: t("status_escalated"),
      failed: t("status_failed"),
    }

    const cls =
      variants[status as keyof typeof variants] ?? "bg-gray-100 text-gray-800"
    const label = labels[status] ?? status
    return <Badge className={cls}>{label}</Badge>
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t("filters")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={t("search_placeholder")}
                  value={searchTerm}
                  onChange={(e) => onSearchTermChange?.(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={onStatusFilterChange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={t("filter_by_status")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("status_all")}</SelectItem>
                <SelectItem value="resolved">{t("status_resolved")}</SelectItem>
                <SelectItem value="escalated">
                  {t("status_escalated")}
                </SelectItem>
                <SelectItem value="failed">{t("status_failed")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Executions List */}
      <div className="space-y-4">
        {executions.map((execution) => (
          <Card key={execution.id} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      setExpandedExecution(
                        expandedExecution === execution.id
                          ? null
                          : execution.id,
                      )
                    }
                  >
                    {expandedExecution === execution.id ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">
                        {execution.customerName}
                      </h3>
                      <Badge variant="outline">{execution.id}</Badge>
                      {getStatusBadge(execution.finalStatus)}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {execution.originalMessage}
                    </p>
                  </div>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div>
                    {t("duration")}: {formatDuration(execution.totalDuration)}
                  </div>
                  <div>{formatTimestamp(execution.startTime)}</div>
                  {execution.feedbackScore && (
                    <div className="flex items-center gap-1 justify-end mt-1">
                      <span>⭐</span>
                      <span>
                        {t("feedback_score", {
                          score: execution.feedbackScore.toString(),
                        })}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>

            {expandedExecution === execution.id && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Workflow Steps */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Bot className="h-4 w-4" />
                      {t("workflow_steps")}
                    </h4>
                    <div className="space-y-3">
                      {execution.steps.map((step, index) => (
                        <div key={step.id} className="flex items-start gap-3">
                          <div className="flex flex-col items-center">
                            {getStatusIcon(step.status)}
                            {index < execution.steps.length - 1 && (
                              <div className="w-px h-8 bg-gray-300 mt-2" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h5 className="font-medium text-sm">
                                {step.name}
                              </h5>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <span>{formatTimestamp(step.timestamp)}</span>
                                <span>({formatDuration(step.duration)})</span>
                                {step.confidence && (
                                  <Badge variant="outline" className="text-xs">
                                    {Math.round(step.confidence * 100)}%{" "}
                                    {t("confidence")}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            {step.details && (
                              <p className="text-sm text-gray-600 mt-1">
                                {step.details}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Final Response */}
                  {execution.finalResponse && (
                    <div className="bg-green-50 rounded-lg p-4">
                      <h4 className="font-medium mb-2 flex items-center gap-2 text-green-800">
                        <MessageSquare className="h-4 w-4" />
                        {t("ai_response")}
                      </h4>
                      <p className="text-sm text-green-700">
                        {execution.finalResponse}
                      </p>
                    </div>
                  )}

                  {/* CS Agent Escalation */}
                  {execution.csAgentId && (
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h4 className="font-medium mb-2 flex items-center gap-2 text-yellow-800">
                        <User className="h-4 w-4" />
                        {t("escalated_to_cs")}
                      </h4>
                      <p className="text-sm text-yellow-700">
                        {" "}
                        {t("case_escalated", { agent: execution.csAgentId })}
                      </p>
                    </div>
                  )}

                  {/* Failed Cases */}
                  {execution.finalStatus === "failed" && (
                    <div className="bg-red-50 rounded-lg p-4">
                      <h4 className="font-medium mb-2 flex items-center gap-2 text-red-800">
                        <AlertTriangle className="h-4 w-4" />
                        {t("execution_failed")}
                      </h4>
                      <p className="text-sm text-red-700">{t("failed_desc")}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {executions.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">{t("no_executions")}</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
