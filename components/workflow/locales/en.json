{"filters": "Filters", "search_placeholder": "Search by customer name, message, or execution ID...", "filter_by_status": "Filter by status", "status_all": "All Statuses", "status_resolved": "Resolved", "status_escalated": "Escalated", "status_failed": "Failed", "duration": "Duration", "workflow_steps": "Workflow Steps", "confidence": "confidence", "ai_response": "AI Response", "escalated_to_cs": "Escalated to CS Agent", "case_escalated": "Case escalated to {agent} for manual handling", "execution_failed": "Execution Failed", "failed_desc": "The AI workflow was unable to provide a satisfactory response", "no_executions": "No executions found matching your criteria.", "feedback_score": "{score}/5"}