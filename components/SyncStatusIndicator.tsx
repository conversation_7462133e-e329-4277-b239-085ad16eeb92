"use client"

import { useState, useEffect } from "react"
import {
  Wifi,
  WifiOff,
  <PERSON>freshC<PERSON>,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
// import { offlineRepoManager } from '@/app/client_repositories/OfflineRepositoryManager'

export function SyncStatusIndicator() {
  const [syncStatus, setSyncStatus] = useState({
    isOnline: true,
    lastSyncAt: new Date(),
    syncInProgress: false,
    pendingChanges: 0,
    errors: [],
  })
  const [isManualSyncing, setIsManualSyncing] = useState(false)

  useEffect(() => {
    // const interval = setInterval(() => {
    //   setSyncStatus(offlineRepoManager.getSyncStatus())
    // }, 1000)
    // return () => clearInterval(interval)
  }, [])

  const handleManualSync = async () => {
    // setIsManualSyncing(true)
    // try {
    //   await offlineRepoManager.syncAll()
    // } catch (error) {
    //   console.error('Manual sync failed:', error)
    // } finally {
    //   setIsManualSyncing(false)
    // }
  }

  const getStatusIcon = () => {
    if (!syncStatus.isOnline) {
      return <WifiOff className="h-4 w-4 text-red-500" />
    }

    if (syncStatus.syncInProgress || isManualSyncing) {
      return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
    }

    if (syncStatus.errors.length > 0) {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }

    if (syncStatus.pendingChanges > 0) {
      return <Clock className="h-4 w-4 text-orange-500" />
    }

    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getStatusText = () => {
    if (!syncStatus.isOnline) {
      return "Offline"
    }

    if (syncStatus.syncInProgress || isManualSyncing) {
      return "Syncing..."
    }

    if (syncStatus.errors.length > 0) {
      return "Sync Error"
    }

    if (syncStatus.pendingChanges > 0) {
      return `${syncStatus.pendingChanges} pending`
    }

    return "Synced"
  }

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return "destructive"
    if (syncStatus.syncInProgress || isManualSyncing) return "default"
    if (syncStatus.errors.length > 0) return "secondary"
    if (syncStatus.pendingChanges > 0) return "outline"
    return "default"
  }

  const getTooltipContent = () => {
    const parts = []

    if (!syncStatus.isOnline) {
      parts.push("Device is offline")
    } else {
      parts.push("Device is online")
    }

    if (syncStatus.lastSyncAt) {
      parts.push(`Last sync: ${syncStatus.lastSyncAt.toLocaleTimeString()}`)
    }

    if (syncStatus.pendingChanges > 0) {
      parts.push(`${syncStatus.pendingChanges} changes waiting to sync`)
    }

    if (syncStatus.errors.length > 0) {
      parts.push(`Errors: ${syncStatus.errors.join(", ")}`)
    }

    return parts.join("\n")
  }

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant={getStatusColor()}
              className="flex items-center gap-1 cursor-help"
            >
              {getStatusIcon()}
              <span className="text-xs">{getStatusText()}</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <pre className="text-xs whitespace-pre-wrap">
              {getTooltipContent()}
            </pre>
          </TooltipContent>
        </Tooltip>

        {syncStatus.isOnline &&
          !syncStatus.syncInProgress &&
          !isManualSyncing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleManualSync}
              className="h-6 w-6 p-0"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
      </div>
    </TooltipProvider>
  )
}

export function OfflineIndicator() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== "undefined" ? navigator.onLine : true,
  )

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    if (typeof window !== "undefined") {
      window.addEventListener("online", handleOnline)
      window.addEventListener("offline", handleOffline)

      return () => {
        window.removeEventListener("online", handleOnline)
        window.removeEventListener("offline", handleOffline)
      }
    }
  }, [])

  if (isOnline) return null

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
      <div className="flex items-center">
        <WifiOff className="h-5 w-5 text-yellow-400 mr-2" />
        <div>
          <p className="text-sm font-medium text-yellow-800">
            You're currently offline
          </p>
          <p className="text-xs text-yellow-700">
            Your changes will be saved locally and synced when you're back
            online.
          </p>
        </div>
      </div>
    </div>
  )
}
