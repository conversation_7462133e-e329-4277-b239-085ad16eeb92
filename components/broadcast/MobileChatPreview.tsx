"use client"

import { Smartphone, Wifi, Battery, Signal } from "lucide-react"
import { Card, CardContent } from "../ui/card"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface MobileChatPreviewProps {
  message: string
  senderName?: string
  timestamp?: Date
}

export function MobileChatPreview({
  message,
  senderName = "Your Business",
  timestamp = new Date()
}: MobileChatPreviewProps) {
  const { t } = useLocalization("broadcast-form", locales)

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  return (
    <Card className="w-full max-w-sm mx-auto bg-gray-900 text-white overflow-hidden">
      <CardContent className="p-0">
        {/* Mobile Status Bar */}
        <div className="flex justify-between items-center px-4 py-2 bg-black text-white text-xs">
          <div className="flex items-center gap-1">
            <span className="font-medium">{formatTime(new Date())}</span>
          </div>
          <div className="flex items-center gap-1">
            <Signal className="h-3 w-3" />
            <Wifi className="h-3 w-3" />
            <Battery className="h-3 w-3" />
            <span className="text-xs">{t("form.mobile_chat.battery_percentage")}</span>
          </div>
        </div>

        {/* Chat Header */}
        <div className="flex items-center gap-3 px-4 py-3 bg-green-600 text-white">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-xs font-bold">
              {senderName.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-sm">{senderName}</h3>
            <p className="text-xs text-green-100">{t("form.mobile_chat.online")}</p>
          </div>
        </div>

        {/* Chat Messages Area */}
        <div className="bg-gray-100 min-h-[300px] p-4 space-y-3">
          {/* Previous message (placeholder) */}
          <div className="flex justify-end">
            <div className="bg-green-500 text-white px-3 py-2 rounded-lg max-w-[80%] text-sm">
              <p>{t("form.mobile_chat.sample_message")}</p>
              <div className="text-xs text-green-100 mt-1 text-right">
                {formatTime(new Date(Date.now() - 300000))}
              </div>
            </div>
          </div>

          {/* Broadcast Message */}
          {message && (
            <div className="flex justify-start">
              <div className="bg-white text-gray-800 px-3 py-2 rounded-lg max-w-[80%] text-sm shadow-sm border">
                <p className="whitespace-pre-wrap">{message}</p>
                <div className="text-xs text-gray-500 mt-1">
                  {formatTime(timestamp)}
                </div>
              </div>
            </div>
          )}

          {/* Typing indicator (if no message) */}
          {!message && (
            <div className="flex justify-start">
              <div className="bg-white text-gray-800 px-3 py-2 rounded-lg text-sm shadow-sm border">
                <div className="flex items-center gap-1">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-xs text-gray-500 ml-2">{t("form.mobile_chat.typing")}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chat Input Area */}
        <div className="bg-white border-t p-3">
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-100 rounded-full px-4 py-2">
              <span className="text-gray-500 text-sm">{t("form.mobile_chat.type_message_placeholder")}</span>
            </div>
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
              </svg>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
