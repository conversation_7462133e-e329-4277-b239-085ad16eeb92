"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, MessageSquare, Users, Send, Smartphone } from "lucide-react"
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../ui/card"
import { Input } from "../ui/input"
import {
  TableHead,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
  Table,
} from "../ui/table"
import { Textarea } from "../ui/textarea"
import { Label } from "../ui/label"

import { Button } from "../ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "../ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { ContactsAPI, DevicesAPI } from "@/lib/services"
import { Broadcast } from "@/lib/repositories/broadcast"
import { Device } from "@/lib/repositories/devices/interface"
import { MobileChatPreview } from "./MobileChatPreview"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

interface BroadcastContact {
  id: string
  name: string
  phone: string
  email?: string
}

interface BroadcastFormProps {
  initialBroadcast?: Broadcast | null
  onSave: (data: {
    title: string
    message: string
    targetContacts: string[]
    deviceId: string
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function BroadcastForm({
  initialBroadcast = null,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText = "Save Broadcast",
  title,
  description,
}: BroadcastFormProps) {
  const { t } = useLocalization("broadcast-form", locales)
  const [broadcastTitle, setBroadcastTitle] = useState(
    initialBroadcast?.title || "",
  )
  const [currentRecipients, setCurrentRecipients] = useState(
    initialBroadcast?.recipients || [],
  )
  const [message, setMessage] = useState(initialBroadcast?.message || "")
  const [selectedDeviceId, setSelectedDeviceId] = useState(
    initialBroadcast?.deviceId || "",
  )
  const [selectedContacts, setSelectedContacts] = useState<{ id: string }[]>(
    initialBroadcast?.targetContacts.map((id) => ({ id })) || [],
  )
  const [contactSearch, setContactSearch] = useState("")
  const [searchResults, setSearchResults] = useState<BroadcastContact[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [devices, setDevices] = useState<Device[]>([])
  const [loadingDevices, setLoadingDevices] = useState(false)

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (contactSearch.trim()) {
        searchContacts(contactSearch.trim())
      } else {
        setSearchResults([])
      }
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [contactSearch])

  useEffect(() => {
    searchContacts("")
    loadDevices()
  }, [])

  async function loadDevices() {
    try {
      setLoadingDevices(true)
      const response = await DevicesAPI.All({
        page: 1,
        per_page: 100,
        sort: [],
      }).request()

      setDevices(response.items.filter((device: Device) => device.isActive))
    } catch (error) {
      console.error(t("form.errors.loading_devices"), error)
      setDevices([])
    } finally {
      setLoadingDevices(false)
    }
  }

  async function searchContacts(query: string) {
    try {
      setIsSearching(true)
      const response = await ContactsAPI.All({
        search: query,
        per_page: 10,
        page: 1,
        sort: [],
      }).request()

      setSearchResults(response.items)
    } catch (error) {
      console.error(t("form.errors.searching_contacts"), error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const toggleContactSelection = (
    contact: BroadcastContact,
    checked: boolean,
  ) => {
    if (checked) {
      if (!selectedContacts.some((c) => c.id === contact.id)) {
        setSelectedContacts((prev) => [...prev, contact])
      }
    } else {
      setSelectedContacts((prev) => prev.filter((c) => c.id !== contact.id))
    }
  }

  const removeContact = (contactId: string) => {
    setSelectedContacts((prev) => prev.filter((c) => c.id !== contactId))
  }

  // Check if contact is selected
  const isContactSelected = (contactId: string) =>
    selectedContacts.some((c) => c.id === contactId)

  const messageLength = message.length
  const maxLength = 1000

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!broadcastTitle.trim()) {
      alert(t("form.validation.title_required"))
      return
    }

    if (!message.trim()) {
      alert(t("form.validation.message_required"))
      return
    }

    if (!selectedDeviceId) {
      alert(t("form.validation.device_required"))
      return
    }

    if (selectedContacts.length === 0) {
      alert(t("form.validation.contacts_required"))
      return
    }

    await onSave({
      title: broadcastTitle.trim(),
      message: message.trim(),
      targetContacts: selectedContacts.map((c) => c.id),
      deviceId: selectedDeviceId,
    })
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="information" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="information" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  {t("form.tabs.information")}
                </TabsTrigger>
                <TabsTrigger value="contacts" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {t("form.tabs.contacts")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="information" className="space-y-6 mt-6">
                {/* Broadcast Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("form.broadcast_info.title")}</CardTitle>
                    <CardDescription>{t("form.broadcast_info.description")}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">{t("form.fields.title.label")}</Label>
                      <Input
                        id="title"
                        value={broadcastTitle}
                        onChange={(e) => setBroadcastTitle(e.target.value)}
                        placeholder={t("form.fields.title.placeholder")}
                        maxLength={200}
                        required
                      />
                      <p className="text-sm text-gray-500">
                        {t("form.fields.title.character_count", { current: broadcastTitle.length, max: 200 })}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="device">{t("form.fields.device.label")}</Label>
                      <Select value={selectedDeviceId} onValueChange={setSelectedDeviceId} required>
                        <SelectTrigger>
                          <SelectValue placeholder={t("form.fields.device.placeholder")} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingDevices ? (
                            <SelectItem value="loading" disabled>
                              {t("form.fields.device.loading")}
                            </SelectItem>
                          ) : devices.length === 0 ? (
                            <SelectItem value="no-devices" disabled>
                              {t("form.fields.device.no_devices")}
                            </SelectItem>
                          ) : (
                            devices.map((device) => (
                              <SelectItem key={device.id} value={device.id}>
                                {device.name} ({device.sessionId})
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-gray-500">
                        {t("form.fields.device.description")}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">{t("form.fields.message.label")}</Label>
                      <Textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder={t("form.fields.message.placeholder")}
                        className="min-h-32"
                        maxLength={maxLength}
                        required
                      />
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>
                          {t("form.fields.message.character_count", { current: messageLength, max: maxLength })}
                        </span>
                        <span
                          className={
                            messageLength > maxLength * 0.9 ? "text-orange-500" : ""
                          }
                        >
                          {messageLength > maxLength * 0.9 && t("form.fields.message.approaching_limit")}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contacts" className="space-y-6 mt-6">

                {currentRecipients.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        {t("form.recipients.current_title", { count: currentRecipients.length })}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>{t("form.recipients.table_headers.name")}</TableHead>
                            <TableHead>{t("form.recipients.table_headers.phone")}</TableHead>
                            <TableHead>{t("form.recipients.table_headers.email")}</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {currentRecipients.map((recipient) => (
                            <TableRow key={recipient.contactId}>
                              <TableCell>{recipient.name || recipient.contactId}</TableCell>
                              <TableCell>{recipient.phone || "-"}</TableCell>
                              <TableCell>{recipient.email || "-"}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                )}

                {/* BroadcastContact Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      {t("form.recipients.select_title", { count: selectedContacts.length })}
                    </CardTitle>
                    <CardDescription>
                      {t("form.recipients.select_description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* BroadcastContact Search Input */}
                    <div className="space-y-2">
                      <Label>{t("form.recipients.search_label")}</Label>
                      <Input
                        placeholder={t("form.recipients.search_placeholder")}
                        value={contactSearch}
                        onChange={(e) => setContactSearch(e.target.value)}
                        autoFocus
                      />
                      <div className="max-h-80 overflow-auto border rounded-md mt-2">
                        {isSearching ? (
                          <p className="p-4 text-center text-gray-500">{t("form.recipients.searching")}</p>
                        ) : searchResults.length === 0 ? (
                          <p className="p-4 text-center text-gray-500">
                            {t("form.recipients.no_contacts")}
                          </p>
                        ) : (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="w-12">{t("form.recipients.table_headers.select")}</TableHead>
                                <TableHead>{t("form.recipients.table_headers.name")}</TableHead>
                                <TableHead>{t("form.recipients.table_headers.phone")}</TableHead>
                                <TableHead>{t("form.recipients.table_headers.email")}</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {searchResults.map((contact) => {
                                const checked = isContactSelected(contact.id)
                                return (
                                  <TableRow
                                    key={contact.id}
                                    className="hover:bg-gray-100"
                                  >
                                    <TableCell>
                                      <input
                                        type="checkbox"
                                        checked={checked}
                                        onChange={(e) =>
                                          toggleContactSelection(
                                            contact,
                                            e.target.checked,
                                          )
                                        }
                                        aria-label={`Select contact ${contact.name}`}
                                      />
                                    </TableCell>
                                    <TableCell>{contact.name}</TableCell>
                                    <TableCell>{contact.phone}</TableCell>
                                    <TableCell>{contact.email || "-"}</TableCell>
                                  </TableRow>
                                )
                              })}
                            </TableBody>
                          </Table>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Mobile Preview Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Smartphone className="h-5 w-5" />
                    {t("form.preview.title")}
                  </CardTitle>
                  <CardDescription>
                    {t("form.preview.description")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <MobileChatPreview
                    message={message}
                    senderName={broadcastTitle || t("form.preview.sender_fallback")}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            {t("form.buttons.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={
              isSubmitting ||
              !broadcastTitle.trim() ||
              !message.trim() ||
              !selectedDeviceId ||
              selectedContacts.length === 0
            }
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {t("form.buttons.saving")}
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                {submitButtonText}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
