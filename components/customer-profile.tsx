"use client"

import { useState, useEffect } from "react"
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  StickyNote,
  Trash2,
  Pencil,
  Save,
  PanelRightClose,
  PanelLeftClose,
  Loader2,
  Construction,
  Clock,
  Sparkles,
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { twMerge } from "tailwind-merge"
import { componentLocales } from "./locales"
import { useLocalization } from "@/localization/functions/client"
import { CustomerProfileAPI } from "@/lib/services/customerProfileApi"
import { CustomerProfile as CustomerProfileType } from "@/lib/repositories/customerProfiles/interface"

interface Note {
  id: string
  content: string
  isEditing?: boolean
}

interface CustomerProfileProps {
  conversationId: string
  customerProfileId?: string
  isCollapsed: boolean
  onToggle: () => void
}

export function CustomerProfile({
  conversationId,
  customerProfileId,
  isCollapsed,
  onToggle,
}: CustomerProfileProps) {
  const { t } = useLocalization("customerProfil", componentLocales)
  const [notes, setNotes] = useState<Note[]>([])
  const [newNote, setNewNote] = useState("")
  const [customerData, setCustomerData] = useState<CustomerProfileType | null>(
    null,
  )
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch customer profile data
  useEffect(() => {
    const fetchCustomerProfile = async () => {
      if (!customerProfileId) return

      setLoading(true)
      setError(null)

      try {
        const response =
          await CustomerProfileAPI.Detail(customerProfileId).request()
        setCustomerData(response)
      } catch (err) {
        console.error("Error fetching customer profile:", err)
        setError("Failed to load customer profile")
      } finally {
        setLoading(false)
      }
    }

    fetchCustomerProfile()
  }, [customerProfileId])

  const addNote = () => {
    if (!newNote.trim()) return
    const note: Note = {
      id: Date.now().toString(),
      content: newNote,
    }
    setNotes([note, ...notes])
    setNewNote("")
  }

  const deleteNote = (id: string) => {
    setNotes(notes.filter((n) => n.id !== id))
  }

  const startEditNote = (id: string) => {
    setNotes(
      notes.map((n) =>
        n.id === id ? { ...n, isEditing: true } : { ...n, isEditing: false },
      ),
    )
  }

  const saveEditedNote = (id: string, updatedContent: string) => {
    setNotes(
      notes.map((n) =>
        n.id === id ? { ...n, content: updatedContent, isEditing: false } : n,
      ),
    )
  }

  return (
    <Card
      className={twMerge(
        "rounded-none border-0 border-b bg-background h-full overflow-y-auto transition-all duration-300 ease-in-out",
        isCollapsed ? "w-[56px]" : "w-80",
      )}
    >
      <CardHeader className="pb-3 sticky top-0 bg-background z-10">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <CardTitle className="text-lg flex items-center gap-2">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center relative">
                <span className="font-semibold text-primary">
                  {t("iconProfil")}
                </span>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full flex items-center justify-center animate-pulse">
                  <Sparkles className="h-2 w-2 text-white" />
                </div>
              </div>
              <div className="flex flex-col">
                <span>{t("customerProfil")}</span>
                <Badge
                  variant="outline"
                  className="text-xs bg-orange-50 text-orange-700 border-orange-200 w-fit"
                >
                  <Construction className="h-3 w-3 mr-1" />
                  Coming Soon
                </Badge>
              </div>
            </CardTitle>
          )}
          <Button
            size="icon"
            variant="ghost"
            onClick={onToggle}
            title={isCollapsed ? "Expand profile" : "Collapse profile"}
            className={twMerge("hover:bg-muted/50", isCollapsed && "mx-auto")}
          >
            {isCollapsed ? (
              <PanelLeftClose className="h-4 w-4" />
            ) : (
              <PanelRightClose className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 pb-4 relative">
        {/* Coming Soon Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-50/90 to-blue-50/90 backdrop-blur-sm z-10 rounded-lg flex items-center justify-center">
          <div className="text-center p-6 bg-white/80 rounded-lg border border-orange-200 shadow-sm animate-pulse">
            <div className="flex items-center justify-center mb-3">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center animate-bounce">
                <Construction className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Customer Profile Coming Soon
            </h3>
            <p className="text-sm text-gray-600 mb-4 max-w-xs">
              We're building an amazing customer profile feature with detailed
              insights, interaction history, and smart analytics.
            </p>
            <div className="flex items-center justify-center gap-2 text-xs text-orange-600">
              <Sparkles className="h-3 w-3 animate-pulse" />
              <span>Expected in next release</span>
            </div>
          </div>
        </div>

        {!isCollapsed ? (
          <>
            {loading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2 text-sm text-muted-foreground">
                  Loading customer profile...
                </span>
              </div>
            )}
            {error && (
              <div className="text-center py-8">
                <p className="text-sm text-red-600">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </Button>
              </div>
            )}
            {!loading && !error && !customerData && (
              <div className="text-center py-8">
                <p className="text-sm text-muted-foreground">
                  No customer profile linked to this conversation
                </p>
              </div>
            )}

            {/* Konten utama yang hanya ditampilkan jika data ada */}
            {customerData && (
              <>
                {/* Basic Info */}
                <div className="space-y-3">
                  <div>
                    <h3 className="font-semibold text-base">
                      {customerData.firstName} {customerData.lastName}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge
                        variant="secondary"
                        className="bg-yellow-100 text-yellow-800"
                      >
                        {customerData.loyaltyLevel || "Standard"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {customerData.totalOrders || 0} orders
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    {customerData.email && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span>{customerData.email}</span>
                      </div>
                    )}
                    {customerData.phone && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>{customerData.phone}</span>
                      </div>
                    )}
                    {customerData.address && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>
                          {[
                            customerData.address.city,
                            customerData.address.state,
                            customerData.address.country,
                          ]
                            .filter(Boolean)
                            .join(", ")}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        Customer since{" "}
                        {new Date(customerData.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-lg font-semibold">
                      {customerData.totalOrders || 0}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Total Orders
                    </div>
                  </div>
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <div className="text-lg font-semibold">
                      ${customerData.totalSpent?.toLocaleString() || "0"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Total Spent
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Recent Orders */}
                {customerData &&
                  customerData.recentOrders &&
                  customerData.recentOrders.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <ShoppingBag className="h-4 w-4" />
                        {t("shopingBag")}
                      </h4>
                      <div className="space-y-2">
                        {customerData.recentOrders.map((order) => (
                          <div
                            key={order.id}
                            className="flex items-center justify-between p-2 bg-muted/30 rounded text-sm"
                          >
                            <div>
                              <div className="font-medium">{order.id}</div>
                              <div className="text-xs text-muted-foreground">
                                {order.date}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">{order.amount}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                <Separator />

                {/* Company Info */}
                {(customerData.company || customerData.jobTitle) && (
                  <>
                    <div>
                      <h4 className="font-medium mb-2">Company Information</h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        {customerData.company && (
                          <div>Company: {customerData.company}</div>
                        )}
                        {customerData.jobTitle && (
                          <div>Position: {customerData.jobTitle}</div>
                        )}
                      </div>
                    </div>
                    <Separator />
                  </>
                )}

                {/* Tags */}
                {customerData.tags && customerData.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Customer Tags</h4>
                    <div className="flex flex-wrap gap-1">
                      {customerData.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <Separator />

                {/* Quick Notes */}
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <StickyNote className="h-4 w-4" />
                    Quick Notes
                  </h4>

                  <Textarea
                    placeholder="Add a quick note about this customer..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={3}
                  />
                  <div className="flex justify-end mt-2">
                    <Button size="sm" onClick={addNote}>
                      Add Note
                    </Button>
                  </div>

                  <div className="space-y-2 mt-4">
                    {notes.map((note) => (
                      <Card key={note.id} className="bg-muted/30">
                        <CardContent className="p-3 space-y-2">
                          {note.isEditing ? (
                            <>
                              <Textarea
                                value={note.content}
                                onChange={(e) =>
                                  setNotes((prev) =>
                                    prev.map((n) =>
                                      n.id === note.id
                                        ? { ...n, content: e.target.value }
                                        : n,
                                    ),
                                  )
                                }
                                rows={3}
                              />
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  onClick={() =>
                                    saveEditedNote(note.id, note.content.trim())
                                  }
                                >
                                  <Save className="h-4 w-4 mr-1" />
                                  Save
                                </Button>
                              </div>
                            </>
                          ) : (
                            <div className="flex justify-between items-start">
                              <div className="text-sm">{note.content}</div>
                              <div className="flex gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => startEditNote(note.id)}
                                >
                                  <Pencil className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => deleteNote(note.id)}
                                >
                                  <Trash2 className="w-4 h-4 text-red-500" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {customerData.lastContactDate && (
                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    Last contact:{" "}
                    {new Date(
                      customerData.lastContactDate,
                    ).toLocaleDateString()}
                  </div>
                )}
              </>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center gap-4 pt-4 relative">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center relative">
              <span className="font-semibold text-primary">
                {customerData
                  ? `${customerData.firstName?.[0] || ""}${customerData.lastName?.[0] || ""}`
                  : "CP"}
              </span>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full flex items-center justify-center animate-pulse">
                <Construction className="h-2 w-2 text-white" />
              </div>
            </div>
            <div className="text-center">
              <Badge
                variant="outline"
                className="text-xs bg-orange-50 text-orange-700 border-orange-200 px-2 py-1"
              >
                Soon
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
