"use client"

import { useEffect, useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog"
import { Trash2 } from "lucide-react"
import { Device } from "@/lib/repositories/devices/interface"
import { DevicesAPI } from "@/lib/services"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function SessionsList({
  devices = [],
  loading = false,
  onDelete,
  onUpdate,
}: {
  devices: Device[]
  loading?: boolean
  onDelete?: (deviceId: string) => Promise<void>
  onUpdate?: (deviceId: string, data: any) => Promise<void>
}) {
  const { t } = useLocalization("layout", locales)

  const [search, setSearch] = useState("")
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const filteredDevices = devices.filter((device) =>
    device.name.toLowerCase().includes(search.toLowerCase()),
  )

  async function logoutDevice(id: string) {
    setDeletingId(id)
    try {
      await DevicesAPI.RemoveDevice(id).request()
      window.location.reload() // or refetch your data
    } catch (err) {
      console.error("Error logout:", err)
    } finally {
      setDeletingId(null)
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform?.toLowerCase()) {
      case "whatsapp":
        return "🟢"
      case "whatsapp-web":
        return "💻"
      default:
        return "📱"
    }
  }

  return (
    <>
      <Input
        placeholder="Search devices..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="max-w-sm"
      />

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          <p>{t("sessions_list.loading_devices")}</p>
        ) : filteredDevices.length === 0 ? (
          <p>{t("sessions_list.no_devices")}</p>
        ) : (
          filteredDevices.map((device, idx) => (
            <Card key={idx}>
              <CardHeader className="flex flex-row items-center py-3 justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  {getPlatformIcon(device.platform)}
                  <span>{device?.me?.pushName || device.name}</span>
                </CardTitle>
                <div className="flex flex-row gap-2 items-center">
                  <Switch
                    id={`switch-${device.id}`}
                    checked={device.id === activeSessionId}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        document.cookie = `waha_session_id=${device.id}; path=/; samesite=lax;`
                        setActiveSessionId(device.id)
                      } else {
                        document.cookie = `waha_session_id=; path=/; max-age=0;`
                        setActiveSessionId(null)
                      }
                    }}
                  />

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="icon"
                        variant="destructive"
                        title="Remove device"
                      >
                        <Trash2 />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          {t("sessions_list.remove_device")}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          {t("sessions_list.remove_device_confirm", {
                            deviceName: device?.me?.pushName || device.name,
                          })}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>
                          {t("sessions_list.cancel")}
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => logoutDevice(device.id)}
                          disabled={deletingId === device.id}
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          {deletingId === device.id ? "Removing..." : "Remove"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardHeader>
              <CardContent className="flex flex-col justify-between text-sm text-muted-foreground space-y-2">
                <div className="flex items-center gap-1">
                  <span>{t("sessions_list.platform")}:</span>
                  <Badge variant="outline" className="capitalize text-xs">
                    {device.platform}
                  </Badge>
                </div>
                <div>
                  {t("sessions_list.status")}:{" "}
                  <Badge
                    variant="outline"
                    className={`text-xs ${device.status === "WORKING"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                      }`}
                  >
                    {device.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {!loading && (
        <p className="text-sm text-muted-foreground">
          {t("sessions_list.showing_results", {
            filtered: filteredDevices.length,
            total: devices.length,
          })}
        </p>
      )}
    </>
  )
}
