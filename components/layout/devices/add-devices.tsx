"use client"

import { Plus, QrCode } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import Image from "next/image"
import { useQrSession } from "@/hooks/useQrSession"
import { useEffect, useState } from "react"
import { getCookie } from "@/lib/utils"
import { toast } from "sonner"
import { Device } from "@/lib/repositories/devices/interface"
import { SyncResult } from "@/lib/services/devicesApi"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function AddDevices({
  devices,
  loading,
  fetchDevices,
  syncDevices,
  syncResult,
}: {
  devices: Device[]
  loading?: boolean
  fetchDevices: () => void
  syncDevices?: () => Promise<void>
  syncResult?: SyncResult | null
}) {
  const { t } = useLocalization("layout", locales)

  const [open, setOpen] = useState<boolean>(false)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [timeLeft, setTimeLeft] = useState<number>(0)

  // const { startSession, qrCode, timeLeft } = useQrSession(
  //   devices,
  //   () => {
  //     fetchDevices();
  //   },
  //   () => {
  //     setOpen(false)
  //   });

  function startSession() {
    fetch("/api/v1/devices/link", {
      method: "POST",
    })
      .then((res) => res.json())
      .then((body) => {
        setQrCode(body.data.qr)
        setOpen(true)
      })
      .catch((err) => {
        toast.error(err.message)
      })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button onClick={startSession}>
          <QrCode className="h-4 w-4" />
          {t("add_devices.link_device")}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("add_devices.link_new_device")}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {t("add_devices.instructions", {
              settings: t("add_devices.settings_text"),
              linked_devices: t("add_devices.linked_devices_text"),
            })}
          </p>
          <div className="flex justify-center flex-col items-center gap-2">
            <div className="p-6 border rounded bg-muted/50">
              {!qrCode ? (
                <QrCode className="w-32 h-32 text-muted-foreground" />
              ) : (
                <Image src={qrCode} alt="qr-code" width={240} height={240} />
              )}
            </div>
            {qrCode && (
              <p className="text-xs text-gray-300 text-center">
                {t("add_devices.available_time", { time: timeLeft })}
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
