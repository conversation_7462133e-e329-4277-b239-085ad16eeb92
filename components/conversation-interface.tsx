"use client"

import { useRef, useEffect, useState } from "react"
import { ConversationHeader } from "@/components/conversation-header"
import { ConversationMessageUI } from "@/components/conversation-message"
import { ConversationInput } from "@/components/conversation-input"
import { EditConversationDialog } from "@/components/edit-conversation-dialog"
import { OfflineIndicator } from "@/components/SyncStatusIndicator"
import { realtime } from "@/lib/realtime"
import { useSendMessage } from "@/hooks/useSendMessage"
import { ConversationsAPI } from "@/lib/services/conversationApi"
import { componentLocales } from "./locales"
import { useLocalization } from "@/localization/functions/client"
import { Conversation } from "@/lib/repositories/conversations"
import { useConversationMessage } from "@/hooks/useConversationMessage"
import { toast } from "sonner"

interface ConversationInterfaceProps {
  conversation: Conversation
  onToggleTemplates: () => void
  showTemplates: boolean
  isProfileCollapsed: boolean
  currentUserId: string
  messageInput: string
  setMessageInput: (message: string) => void
  onConversationUpdated?: (updatedConversation: Conversation) => void
}

export function ConversationInterface({
  conversation,
  messageInput,
  setMessageInput,
  onToggleTemplates,
  showTemplates,
  isProfileCollapsed,
  currentUserId,
  onConversationUpdated,
}: ConversationInterfaceProps) {
  const { t } = useLocalization("chatInterface", componentLocales)
  const {
    sendMessage,
    loading: loadingSendMSG,
    error: errorSendMSG,
  } = useSendMessage()

  const {
    messages,
    loading,
    error,
    isLastConversations,
    page,
    setPage,
    refetch,
  } = useConversationMessage({
    conversationId: conversation.id,
  })

  const containerRef = useRef<HTMLDivElement | null>(null)
  const listRef = useRef<HTMLDivElement | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [prevScrollHeight, setPrevScrollHeight] = useState(0)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [currentConversation, setCurrentConversation] = useState(conversation)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView()
  }

  // Update current conversation when prop changes
  useEffect(() => {
    setCurrentConversation(conversation)
  }, [conversation])

  // Handle edit conversation
  const handleEditConversation = () => {
    setShowEditDialog(true)
  }

  // Handle conversation update
  const handleConversationUpdated = (updatedConversation: Conversation) => {
    setCurrentConversation(updatedConversation)
    if (onConversationUpdated) {
      onConversationUpdated(updatedConversation)
    }
  }

  // Handle close conversation
  const handleCloseConversation = async () => {
    try {
      const response = await ConversationsAPI.Close(conversation.id, {
        reason: "Closed by user",
        notes: "Conversation closed from interface"
      }).request()

      if (response) {
        toast.success("Conversation closed successfully")
        handleConversationUpdated(response)
      }
    } catch (error: any) {
      console.error("Failed to close conversation:", error)
      const message = error.response?.data?.error || error.message || "Failed to close conversation"
      toast.error(message)
    }
  }

  useEffect(() => {
    const channel = realtime.client.subscribe("conversation-room-channel")

    const handleNewMessage = (data: any) => {
      if (conversation.id !== data.from) return
      refetch()
    }

    channel.bind("new-message", handleNewMessage)

    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [conversation.id, refetch])

  useEffect(() => {
    setPrevScrollHeight(0)
    setPage(0)
  }, [conversation.id])

  useEffect(() => {
    const listEl = listRef.current
    if (!listEl || isLastConversations || loading) return

    const handleScroll = () => {
      if (listEl.scrollTop <= 20 && !loading && !isLastConversations) {
        setPrevScrollHeight(listEl.scrollHeight)
        setPage((prev) => prev + 1)
      }
    }

    listEl.addEventListener("scroll", handleScroll)
    return () => listEl.removeEventListener("scroll", handleScroll)
  }, [loading, isLastConversations, setPage])

  useEffect(() => {
    const listEl = listRef.current
    if (!listEl || prevScrollHeight === 0) return

    const newScrollHeight = listEl.scrollHeight
    const scrollDiff = newScrollHeight - prevScrollHeight
    listEl.scrollTop = scrollDiff
    setPrevScrollHeight(0)
  }, [messages])

  useEffect(() => {
    if (!messages || messages.length === 0) return
    if (page === 0) scrollToBottom()
  }, [messages, page])

  const handleSendMessage = async (message: string) => {
    const result = await sendMessage({
      conversationId: conversation.id,
      text: message,
    })

    // Update conversation with new last message immediately for better UX
    if (result.success && onConversationUpdated) {
      const updatedConversation: Conversation = {
        ...currentConversation,
        lastMessage: {
          body: message,
          fromMe: true,
          _data: {
            messageTimestamp: new Date(),
          },
          ack: 1, // Sent but not delivered yet
        },
        lastMessageAt: new Date(),
        updatedAt: new Date(),
      }
      onConversationUpdated(updatedConversation)
    }

    refetch()
    scrollToBottom()
  }

  const participantsList = (conversation.participantsFull || []).map(
    (p: { id: string; name: string }) => ({
      id: p.id,
      name: p.name,
    }),
  )

  const participantsSet = new Set(participantsList)

  return (
    <div ref={containerRef} className="flex flex-col h-full">
      <ConversationHeader
        customerName={currentConversation.name || t("customerSupport")}
        customerEmail={currentConversation.id}
        orderNumber="~.~"
        status={(currentConversation as any).status_presence || t("status")}
        tags={currentConversation.tags || []}
        description={currentConversation.description}
        conversation={currentConversation}
        onEditConversation={handleEditConversation}
        onCloseConversation={handleCloseConversation}
      />

      <OfflineIndicator />

      <div className="flex-1 overflow-y-auto p-4 space-y-4" ref={listRef}>
        {loading && (
          <div className="text-xs text-muted-foreground">
            Loading messages...
          </div>
        )}
        {error && <div className="text-xs text-red-600">Error: {error}</div>}

        {(messages || []).map((message, idx) => (
          <div key={idx} className="relative">
            <ConversationMessageUI
              message={message}
              currentUser={currentUserId}
              participants={participantsSet}
            />
          </div>
        ))}

        {(conversation as any).status_presence === "typing" && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className="italic">{t("sedangMengetik")}</span>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <ConversationInput
        onSendMessage={handleSendMessage}
        messageInput={messageInput}
        setMessageInput={setMessageInput}
        onToggleTemplates={onToggleTemplates}
        showTemplates={showTemplates}
        conversation={conversation}
      />

      <EditConversationDialog
        conversation={currentConversation}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onConversationUpdated={handleConversationUpdated}
      />
    </div>
  )
}
