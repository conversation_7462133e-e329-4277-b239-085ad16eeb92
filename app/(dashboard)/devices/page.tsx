"use client"

import SessionsList from "@/components/layout/devices/SessionsList"
import AddDevices from "@/components/layout/devices/add-devices"
import { useDevices } from "@/hooks/useDevices"
import { devicesLocales } from "./locales"
import { useLocalization } from "@/localization/functions/client"

export default function LinkedDevicesPage() {
  const { t } = useLocalization("devices", devicesLocales)
  const {
    devices,
    loading,
    error,
    syncResult,
    fetchDevices,
    syncDevices,
    deleteDevice,
    updateDevice
  } = useDevices()

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t("page_title")}</h2>

        {/* Form to set provider */}
        <div className="flex gap-2 items-center">
          <AddDevices
            devices={devices}
            loading={loading}
            fetchDevices={fetchDevices}
            syncDevices={syncDevices}
            syncResult={syncResult}
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {syncResult && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
          <p>Sync completed: {syncResult.synced} devices synced, {syncResult.created} created, {syncResult.updated} updated</p>
          {syncResult.errors.length > 0 && (
            <ul className="mt-2 list-disc list-inside">
              {syncResult.errors.map((error, index) => (
                <li key={index} className="text-red-600">{error}</li>
              ))}
            </ul>
          )}
        </div>
      )}

      <SessionsList
        devices={devices?.map((device) => ({
          ...device,
          id: device.id,
          name: device.me?.pushName || device.name,
          platform: device.platform || "phone",
        }))}
        loading={loading}
        onDelete={deleteDevice}
        onUpdate={updateDevice}
      />
    </div>
  )
}
