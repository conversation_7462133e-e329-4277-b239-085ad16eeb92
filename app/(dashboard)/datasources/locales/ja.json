{"page_title": "データソース", "page_subtitle": "データソースとコンテンツを管理", "new_datasource": "新しいデータソース", "search_placeholder": "データソースを検索...", "refresh": "更新", "buttons": {"add_source": "ソースを追加", "save_source": "ソースを保存", "update_source": "ソースを更新", "cancel": "キャンセル", "delete": "削除", "deleting": "削除中..."}, "dialog": {"add_title": "新しいデータソースを追加", "edit_title": "データソースを編集", "delete_title": "データソースを削除", "delete_description": "データソース \"{{name}}\" を削除してもよろしいですか？"}, "form": {"labels": {"name": "名前", "type": "タイプ", "url": "URL / 識別子", "access_key": "アクセスキー（オプション）"}, "placeholders": {"name": "データソース名を入力", "type": "タイプを選択", "url": "https://sheet.google.com/...", "access_key": "アクセスキーを入力", "search": "AIデータソースを検索..."}}, "table": {"title": "データソース ({{count}})", "description": "データソースとコンテンツを管理", "headers": {"name": "名前", "type": "タイプ", "content_url": "コンテンツ/URL", "status": "ステータス", "created": "作成日", "actions": "アクション"}, "content": {"no_content": "コンテンツなし", "no_url": "URLなし"}, "status": {"active": "アクティブ", "inactive": "非アクティブ"}}, "types": {"google_sheet": "Googleシート", "rest_api": "REST API", "csv": "CSV", "json": "JSON", "firebase": "Firebase"}, "states": {"loading": "データソースを読み込み中...", "no_datasources": "データソースが見つかりません", "create_first": "最初のデータソースを作成して開始"}, "validation": {"name_required": "名前は必須です", "type_required": "タイプは必須です", "url_required": "URLは必須です"}, "confirmations": {"delete": "このデータソースを削除してもよろしいですか？"}, "errors": {"fetch_failed": "データソースの取得エラー", "delete_failed": "データソースの削除エラー", "delete_alert": "データソースの削除に失敗しました。もう一度お試しください。"}, "new_page": {"title": "新しいデータソースを作成", "description": "コレクションに新しいデータソースを追加", "submit": "データソースを作成"}, "detail_page": {"loading": "データソースを読み込み中...", "not_found": "データソースが見つかりません", "back_to_list": "データソースに戻る", "edit": {"title": "データソースを編集", "description": "データソースの詳細を更新", "submit": "変更を保存"}, "delete_confirmation": "このデータソースを削除してもよろしいですか？この操作は元に戻せません。", "delete_error": "データソースの削除に失敗しました。もう一度お試しください。"}}