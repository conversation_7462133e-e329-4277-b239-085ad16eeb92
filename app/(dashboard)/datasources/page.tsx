"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Eye,
  Trash2,
  Database,
  FileText,
  Globe,
  RefreshCw
} from "lucide-react"
import { DatasourcesAPI } from "@/lib/services"
import { Datasource } from "@/lib/repositories/datasources/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

export default function DatasourcesPage() {
  const { t } = useLocalization("datasources", locales)
  const router = useRouter()
  const [datasources, setDatasources] = useState<Datasource[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  const fetchDatasources = async (search = "") => {
    try {
      setLoading(true)
      const response = await DatasourcesAPI.All({
        page: 1,
        per_page: 100,
        search: search || undefined,
        sort: [{ field: "createdAt", direction: "DESC" }],
      }).request()

      setDatasources(response.items)
    } catch (error) {
      console.error(t("errors.fetch_failed"), error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDatasources()
  }, [])

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchDatasources(searchTerm)
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [searchTerm])



  const handleDelete = async (id: string) => {
    if (!confirm(t("confirmations.delete"))) return

    try {
      await DatasourcesAPI.Delete(id).request()
      fetchDatasources(searchTerm)
    } catch (error) {
      console.error(t("errors.delete_failed"), error)
      alert(t("errors.delete_alert"))
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "TEXT":
        return <FileText className="h-4 w-4" />
      case "API":
        return <Globe className="h-4 w-4" />
      default:
        return <Database className="h-4 w-4" />
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t("page_title")}</h1>
          <p className="text-gray-600">{t("page_subtitle")}</p>
        </div>
        <Button onClick={() => router.push("/datasources/new")}>
          <Plus className="h-4 w-4 mr-2" />
          {t("new_datasource")}
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={t("search_placeholder")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" onClick={() => fetchDatasources(searchTerm)}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Datasources Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("table.title", { count: datasources.length })}</CardTitle>
          <CardDescription>{t("table.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : datasources.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>{t("states.no_datasources")}</p>
              <p className="text-sm">{t("states.create_first")}</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("table.headers.name")}</TableHead>
                  <TableHead>{t("table.headers.type")}</TableHead>
                  <TableHead>{t("table.headers.content_url")}</TableHead>
                  <TableHead>{t("table.headers.status")}</TableHead>
                  <TableHead>{t("table.headers.created")}</TableHead>
                  <TableHead>{t("table.headers.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {datasources.map((datasource) => (
                  <TableRow key={datasource.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(datasource.type)}
                        {datasource.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{datasource.type}</Badge>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      {datasource.type === "TEXT" ? (
                        <div className="truncate text-sm text-gray-600">
                          {datasource.content || t("table.content.no_content")}
                        </div>
                      ) : (
                        <div className="truncate text-sm text-blue-600">
                          {datasource.url || t("table.content.no_url")}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={datasource.isActive ? "default" : "secondary"}>
                        {datasource.isActive ? t("table.status.active") : t("table.status.inactive")}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {formatDate(datasource.createdAt)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/datasources/${datasource.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(datasource.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
