"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { DatasourcesAPI } from "@/lib/services"
import DatasourceForm from "@/components/datasource/DatasourceForm"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

export default function NewDatasourcePage() {
  const { t } = useLocalization("datasources", locales)
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSave = async (data: {
    name: string
    type: string
    content?: string
    url?: string
    accessKey?: string
    isActive: boolean
  }) => {
    setIsSubmitting(true)
    try {
      const datasource = await DatasourcesAPI.Create({
        name: data.name,
        type: data.type,
        content: data.content,
        url: data.url,
        accessKey: data.accessKey,
        isActive: data.isActive,
      }).request()

      router.push(`/datasources/${datasource.id}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <DatasourceForm
      onSave={handleSave}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
      submitButtonText={t("new_page.submit")}
      title={t("new_page.title")}
      description={t("new_page.description")}
    />
  )
}
