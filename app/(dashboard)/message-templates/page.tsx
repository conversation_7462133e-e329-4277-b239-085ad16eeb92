import { getMessageTemplatesSearchConfig } from "@/lib/utils/searchConfigBuilder"
import MessageTemplatesClient from "./client"

export default async function MessageTemplatesPage() {
  // ✨ Build search config directly using business logic (SSR)
  const searchConfig = getMessageTemplatesSearchConfig()

  if (!searchConfig) {
    return <div>Error: Could not load search configuration</div>
  }

  return <MessageTemplatesClient searchConfig={searchConfig} />
}
