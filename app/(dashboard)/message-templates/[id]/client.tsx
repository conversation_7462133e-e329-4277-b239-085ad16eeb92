"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"
import { MessageTemplate } from "@/lib/repositories/messageTemplates/interface"

interface ClientPageProps {
  id: string
}

const messageTemplateEditorConfig: DataEditorConfig = {
  title: "Edit Message Template",
  subtitle: "Update your saved message template for reuse",

  fields: [
    {
      name: "title",
      label: "Template Name",
      type: "text",
      placeholder: "e.g., Order Confirmation, Reminder",
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "query",
      label: "Query",
      type: "text",
      placeholder: "e.g., Order, Notification, Support",
      validation: {
        required: true,
        maxLength: 100,
      },
      group: "content",
    },
    {
      name: "template",
      label: "Message Template Body",
      type: "textarea",
      placeholder:
        "e.g., Hello {{title}}, your order {{order_id}} has been shipped.",
      rows: 6,
      validation: {
        required: true,
        minLength: 5,
      },
      description: "Use {{variables}} to insert dynamic content",
      group: "content",
    },
    {
      name: "variables",
      label: "Variables",
      type: "text",
      placeholder: "title, order_id",
      description: "Comma-separated variable titles used in the message",
      group: "content",
    },
    {
      name: "tags",
      label: "Tags",
      type: "text",
      placeholder: "follow-up, shipping, invoice",
      description: "Optional labels to organize templates (comma-separated)",
      group: "meta",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Internal notes or usage description",
      rows: 3,
      validation: {
        maxLength: 500,
      },
      group: "meta",
    },
    {
      name: "createdAt",
      label: "Created Date",
      type: "text",
      disabled: true,
      group: "system",
    },
    {
      name: "updatedAt",
      label: "Last Updated",
      type: "text",
      disabled: true,
      group: "system",
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      disabled: true,
      group: "system",
    },
  ],

  sections: [
    {
      title: "Basic Information",
      description: "Identify and categorize your template",
      fields: ["title", "category"],
    },
    {
      title: "Template Content",
      description: "Message content and variables",
      fields: ["query", "template", "variables"],
    },
    {
      title: "Metadata",
      description: "Organize with tags and provide description",
      fields: ["tags", "description"],
    },
    {
      title: "System Information",
      description: "Audit trail and metadata",
      fields: ["createdAt", "updatedAt", "createdBy"],
    },
  ],

  fetchData: async (id: string) => {
    try {
      const template = await MessageTemplatesAPI.Detail(id).request()

      if (!template) throw new Error("Message Template not found")

      return {
        id: template.id,
        title: template.title,
        query: template.query,
        template: template.template,
        variables: template.variables?.join(", ") || "",
        tags: template.tags?.join(", ") || "",
        description: "",
        createdAt: template.createdAt
          ? new Date(template.createdAt).toLocaleString("id-ID")
          : "",
        updatedAt: template.updatedAt
          ? new Date(template.updatedAt).toLocaleString("id-ID")
          : "",
        createdBy: template.createdBy || "System",
      }
    } catch (error: any) {
      console.error("Error fetching message template:", error)
      throw new Error(error?.message || "Failed to fetch message template")
    }
  },

  saveData: async (data: Record<string, any>) => {
    try {
      const payload: Partial<MessageTemplate> = {
        title: data.title,
        query: data.query,
        template: data.template,
        variables: data.variables
          ? data.variables
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        tags: data.tags
          ? data.tags
              .split(",")
              .map((t: string) => t.trim())
              .filter(Boolean)
          : [],
        isActive: true,
      }

      await MessageTemplatesAPI.Update(data.id, payload).request()
    } catch (error: any) {
      console.error("Error updating message template:", error)

      let message = "Failed to update message template"
      if (error?.response?.data?.messages?.length > 0) {
        message = error.response.data.messages[0]
      } else if (error?.message) {
        message = error.message
      }

      throw new Error(message)
    }
  },

  backRoute: "/message-templates",
  successRoute: "/message-templates",

  submitButtonText: "Update Template",
  cancelButtonText: "Cancel",
  showImagePreview: false,
}

export default function ClientPage({ id }: ClientPageProps) {
  return <DataEditorPage config={messageTemplateEditorConfig} id={id} />
}
