"use client"

import { TableRowData } from "@/components/crud-page"
import type { DataPageEnhancedConfig } from "@/components/crud-page/DataPageEnhanced"
import DataPageEnhanced from "@/components/crud-page/DataPageEnhanced"
import { DEFAULT_PER_PAGE } from "@/components/crud-page/default_per_page"
import {
  StatBreakdown,
  StatItem,
  StatsResponse,
} from "@/components/crud-page/stats"
import { useLocalization } from "@/localization/functions/client"
import { MessageTemplate } from "@/lib/repositories/messageTemplates"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"
import { SearchConfig } from "@/lib/types/searchConfig"
import { deepMerge } from "@/lib/utils/deepMerge"
import { apiLocales } from "@/app/api/locales"
import { messageTemplatesLocales } from "./locales"

// ✨ Map backend stats to frontend format
const mapMessageTemplateStatsToGeneric = (
  backendData: any,
  t: (key: string, template?: Record<string, string | number>) => string,
): StatsResponse => {
  const data = backendData.data || backendData

  const stats: StatItem[] = [
    {
      id: "total_templates",
      title: t("stats.total_templates"),
      value: data.total || 0,
      label: `${data.recent || 0} ${t("stats.recent_added")}`,
      description: t("stats.total_description"),
      icon: "template",
      cardColor: "bg-blue-50",
      textColor: "text-blue-900",
    },
    {
      id: "active_templates",
      title: t("stats.active_templates"),
      value: data.active || 0,
      label: `${((data.active / (data.total || 1)) * 100).toFixed(1)}${t("stats.percentage")}`,
      cardColor: "bg-green-50",
      textColor: "text-green-900",
    },
    {
      id: "templates_with_variables",
      title: t("stats.with_variables"),
      value: data.withVariables || 0,
      label: `${((data.withVariables / (data.total || 1)) * 100).toFixed(1)}${t("stats.percentage")}`,
      cardColor: "bg-purple-50",
      textColor: "text-purple-900",
    },
    {
      id: "unique_categories",
      title: t("stats.unique_categories"),
      value: data.uniqueCategories || 0,
      label: t("stats.categories_label"),
      cardColor: "bg-orange-50",
      textColor: "text-orange-900",
    },
  ]

  const breakdowns: StatBreakdown[] = []

  // Category breakdown
  if (data.categoryBreakdown && data.categoryBreakdown.length > 0) {
    breakdowns.push({
      id: "category_breakdown",
      title: t("stats.category_breakdown"),
      items: data.categoryBreakdown.map((item: any, index: number) => ({
        label: item.category,
        value: item.count,
        percentage: item.percentage,
        color: [
          "#3B82F6",
          "#8B5CF6",
          "#06B6D4",
          "#10B981",
          "#F59E0B",
          "#EF4444",
        ][index % 6],
      })),
    })
  }

  return {
    stats,
    breakdowns,
    summary: {
      total: data.total || 0,
      period: t("stats.all_time"),
      lastUpdated: new Date().toISOString(),
    },
  }
}

interface MessageTemplatesClientProps {
  searchConfig: SearchConfig
}

export default function MessageTemplatesClient({ searchConfig }: MessageTemplatesClientProps) {
  const { t } = useLocalization(
    "message-templates",
    deepMerge(messageTemplatesLocales, apiLocales),
  )

  const config: DataPageEnhancedConfig<MessageTemplate> = {
    title: t("page_title"),
    subtitle: t("page_subtitle"),
    headers: [
      t("headers.title"),
      t("headers.category"),
      t("headers.variables"),
      t("headers.is_active"),
      t("headers.created_date"),
      t("headers.updated_date"),
      t("headers.created_by"),
    ],
    searchConfig: searchConfig,

    deleteItem: async (id: string): Promise<void> => {
      await MessageTemplatesAPI.Delete(id).request()
    },
    addRoute: "/message-templates/new",
    editRoute: (id: string) => `/message-templates/${id}`,
    bulkRoute: "/message-templates/bulk",

    fetchData: async (params: {
      search?: string
      includeDeleted?: boolean
      page?: number
      limit?: number
      sort?: Array<{ field: string; direction: "ASC" | "DESC" }>
      filters?: Array<{ field: string; operator?: string; value: any }>
    }) => {
      let apiParams = {
        page: params?.page || 1,
        per_page: params?.limit || DEFAULT_PER_PAGE,
        search: params?.search,
        sort: params?.sort || [],
        filters: params?.filters || [],
      }

      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || []
        apiParams.filters.push({ field: "includeDeleted", value: true })
      }

      const response = await MessageTemplatesAPI.All(apiParams).request()

      return {
        items: response.items || [],
        total: response.total || 0,
        totalPages: Math.ceil(response.total / DEFAULT_PER_PAGE),
      }
    },
    transformToTableRow: (item: MessageTemplate): TableRowData => {
      return {
        id: item.id,
        columns: [
          item.title,
          item.category || "-",
          item.variables ? item.variables.join(", ") : "-",
          item.isActive ? t("common.yes") : t("common.no"),
          new Date(item.createdAt).toLocaleDateString("id-ID"),
          new Date(item.updatedAt).toLocaleDateString("id-ID"),
          item.createdBy || "-",
        ],
      }
    },
  }

  return <DataPageEnhanced config={config} />
}
