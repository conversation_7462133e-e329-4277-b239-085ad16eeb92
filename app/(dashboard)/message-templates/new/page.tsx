"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { MessageTemplatesAPI } from "@/lib/services/messageTemplatesApi"

const messageTemplateEditorConfig: DataEditorConfig = {
  title: "Message Template",
  subtitle: "Create a new message template for customer communication",

  fields: [
    {
      name: "title",
      label: "Template Name",
      type: "text",
      placeholder: "e.g., Order Confirmation, Shipping Update",
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "query",
      label: "Query",
      type: "text",
      placeholder: "e.g., Order, Support, Notification",
      validation: {
        required: true,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "template",
      label: "Message Template Body",
      type: "textarea",
      placeholder: "Hello {{title}}, your order {{order_id}} has been shipped!",
      rows: 6,
      validation: {
        required: true,
        minLength: 5,
      },
      description: "Use double curly braces {{...}} for variables",
      group: "content",
    },
    {
      name: "variables",
      label: "Variables",
      type: "text",
      placeholder: "title, order_id, date",
      description: "Comma-separated list of expected variables in the template",
      group: "content",
    },
    {
      name: "tags",
      label: "Tags",
      type: "text",
      placeholder: "e.g., shipping, invoice, follow-up",
      description: "Optional tags to help organize templates",
      group: "meta",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Brief explanation of this template",
      rows: 3,
      group: "meta",
      validation: {
        maxLength: 500,
      },
    },
  ],

  sections: [
    {
      title: "Basic Information",
      description: "Identify this template",
      fields: ["title", "category"],
    },
    {
      title: "Content",
      description: "Write the message and define its variables",
      fields: ["query", "template", "variables"],
    },
    {
      title: "Metadata",
      description: "Tags and descriptions for easier search and management",
      fields: ["tags", "description"],
    },
  ],

  saveData: async (data: Record<string, any>, _isEdit: boolean) => {
    try {
      const transformedData = {
        title: data.title,
        query: data.query,
        template: data.template,
        variables: data.variables
          ? data.variables
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        tags: data.tags
          ? data.tags
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        description: data.description || "",
      }

      await MessageTemplatesAPI.Create(transformedData).request()
    } catch (error: any) {
      console.error("Error creating messageTemplate:", error)

      let errorMessage = "Failed to create message template"
      if (error?.response?.data?.messages?.length > 0) {
        errorMessage = error.response.data.messages[0]
      } else if (error?.message) {
        errorMessage = error.message
      }

      throw new Error(errorMessage)
    }
  },

  backRoute: "/message-templates",
  successRoute: "/message-templates",

  submitButtonText: "Create Template",
  cancelButtonText: "Cancel",
  showImagePreview: false,
}

export default function NewMessageTemplatePage() {
  return <DataEditorPage config={messageTemplateEditorConfig} />
}
