import { redirect } from "next/navigation"
import { buildSessionContextFromCookie } from "../api/sharedFunction"
import DashboardClientLayout from "@/components/layout/DashboardClientLayout"
import { accountBusinessLogic } from "@/lib/repositories/businessLogics"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const context = await buildSessionContextFromCookie()
  if (!context) {
    redirect("/auth/login")
  }

  const account = await accountBusinessLogic.getAccount(context)

  if (!account) {
    redirect("/auth/register")
  }


  return (
    <DashboardClientLayout account={account}>
      {children}
    </DashboardClientLayout>
  )
}
