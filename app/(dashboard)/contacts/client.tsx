"use client"

import { apiLocales } from "@/app/api/locales"
import { TableRowData } from "@/components/crud-page"
import type { DataPageEnhancedConfig } from "@/components/crud-page/DataPageEnhanced"
import DataPageEnhanced from "@/components/crud-page/DataPageEnhanced"
import { DEFAULT_PER_PAGE } from "@/components/crud-page/default_per_page"
import {
  StatBreakdown,
  StatItem,
  StatsResponse,
} from "@/components/crud-page/stats"
import { useLocalization } from "@/localization/functions/client"
import { Contact } from "@/lib/repositories/contacts"
import { ContactsAPI } from "@/lib/services/contactsApi"
import { SearchConfig } from "@/lib/types/searchConfig"
import { deepMerge } from "@/lib/utils/deepMerge"
import { contactsLocales } from "./locales"

// ✨ Mapper function to convert backend response to generic StatsResponse
const mapContactStatsToGeneric = (
  backendData: any,
  t: (key: string, template?: Record<string, string | number>) => string,
): StatsResponse => {
  const data = backendData.data || backendData

  // Create main stat cards
  const stats: StatItem[] = [
    {
      id: "total_contacts",
      title: t("stats.total_contacts"),
      value: data.totalContacts || 0,
      label: `${data.recentContacts || 0} ${t("stats.added_recently")}`,
      description: t("stats.all_contacts_description"),
      icon: "users",
      cardColor: "bg-blue-50",
      textColor: "text-blue-900",
    },
    {
      id: "active_contacts",
      title: t("stats.active_contacts"),
      value: data.activeContacts || 0,
      label:
        data.totalContacts > 0
          ? `${((data.activeContacts / data.totalContacts) * 100).toFixed(1)}${t("stats.percentage_of_total")}`
          : `0${t("stats.percentage_of_total")}`,
      cardColor: "bg-green-50",
      textColor: "text-green-900",
    },
    {
      id: "contacts_with_email",
      title: t("stats.contacts_with_email"),
      value: data.contactsWithEmail || 0,
      label:
        data.totalContacts > 0
          ? `${((data.contactsWithEmail / data.totalContacts) * 100).toFixed(1)}${t("stats.percentage_of_total")}`
          : `0${t("stats.percentage_of_total")}`,
      cardColor: "bg-purple-50",
      textColor: "text-purple-900",
    },
    {
      id: "contacts_with_tags",
      title: t("stats.contacts_with_tags"),
      value: data.contactsWithTags || 0,
      label:
        data.totalContacts > 0
          ? `${((data.contactsWithTags / data.totalContacts) * 100).toFixed(1)}${t("stats.percentage_of_total")}`
          : `0${t("stats.percentage_of_total")}`,
      cardColor: "bg-orange-50",
      textColor: "text-orange-900",
    },
  ]

  // Create breakdown sections
  const breakdowns: StatBreakdown[] = []

  // Status breakdown
  if (data.statusBreakdown && data.statusBreakdown.length > 0) {
    breakdowns.push({
      id: "status_breakdown",
      title: t("stats.contact_status"),
      items: data.statusBreakdown.map((item: any) => ({
        label: item.status,
        value: item.count,
        percentage: item.percentage,
        color:
          item.status === "Active"
            ? "#10B981"
            : item.status === "Deleted"
              ? "#EF4444"
              : item.status === "Pending"
                ? "#F59E0B"
                : "#6B7280",
      })),
    })
  }

  // Tag breakdown
  if (data.tagBreakdown && data.tagBreakdown.length > 0) {
    breakdowns.push({
      id: "tag_breakdown",
      title: t("stats.top_tags"),
      items: data.tagBreakdown.map((item: any, index: number) => ({
        label: item.tag,
        value: item.count,
        percentage: item.percentage,
        color: [
          "#3B82F6",
          "#8B5CF6",
          "#06B6D4",
          "#10B981",
          "#F59E0B",
          "#EF4444",
        ][index % 6],
      })),
    })
  }

  // Created by breakdown
  if (data.createdByBreakdown && data.createdByBreakdown.length > 0) {
    breakdowns.push({
      id: "created_by_breakdown",
      title: t("stats.created_by"),
      items: data.createdByBreakdown.map((item: any, index: number) => ({
        label: item.createdBy,
        value: item.count,
        percentage: item.percentage,
        color: ["#6366F1", "#EC4899", "#14B8A6", "#F97316", "#84CC16"][
          index % 5
        ],
      })),
    })
  }

  return {
    stats,
    breakdowns,
    summary: {
      total: data.totalContacts || 0,
      period: t("stats.all_time"),
      lastUpdated: new Date().toISOString(),
    },
  }
}

interface ContactsClientProps {
  searchConfig: SearchConfig
}

export default function ContactsClient({ searchConfig }: ContactsClientProps) {
  const { t } = useLocalization(
    "contacts",
    deepMerge(contactsLocales, apiLocales),
  )

  const config: DataPageEnhancedConfig<Contact> = {
    title: t("page_title"),
    subtitle: t("page_subtitle"),
    headers: [
      t("headers.name"),
      t("headers.phone"),
      t("headers.email"),
      t("headers.tags"),
      t("headers.notes_count"),
      t("headers.created_date"),
      t("headers.updated_date"),
      t("headers.created_by"),
    ],
    searchConfig: searchConfig,

    deleteItem: async (id: string): Promise<void> => {
      await ContactsAPI.Delete(id).request()
    },
    addRoute: "/contacts/new",
    editRoute: (id: string) => `/contacts/${id}`,
    bulkRoute: "/contacts/bulk",

    fetchData: async (params: {
      search?: string
      includeDeleted?: boolean
      page?: number
      limit?: number
      sort?: Array<{ field: string; direction: "ASC" | "DESC" }>
      filters?: Array<{ field: string; operator?: string; value: any }>
    }) => {
      let apiParams = {
        page: params?.page || 1,
        per_page: params?.limit || DEFAULT_PER_PAGE,
        search: params?.search,
        sort: params?.sort || [],
        filters: params?.filters || [],
      }

      // Add includeDeleted as a filter if specified
      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || []
        apiParams.filters.push({ field: "includeDeleted", value: true })
      }

      const response = await ContactsAPI.All(apiParams).request()

      return {
        items: response.items || [],
        total: response.total || 0,
        totalPages: Math.ceil(response.total / DEFAULT_PER_PAGE),
      }
    },
    fetchStats: async (params: {
      search?: string
      includeDeleted?: boolean
      filters?: Array<{ field: string; value: any }>
      dateFrom?: string
      dateTo?: string
    }) => {
      const result = await ContactsAPI.Stats(params).request()
      // ✨ Transform backend response to generic StatsResponse format with localization
      return mapContactStatsToGeneric(result, t)
    },
    transformToTableRow: (item: Contact): TableRowData => {
      return {
        id: item.id,
        columns: [
          item.name,
          item.phone,
          item.email || "-",
          item.tags ? item.tags.join(", ") : "-",
          item.notes ? item.notes.length.toString() : "0",
          new Date(item.createdAt).toLocaleDateString("id-ID"),
          new Date(item.updatedAt).toLocaleDateString("id-ID"),
          item.createdBy || "-",
        ],
      }
    },
  }

  return <DataPageEnhanced config={config} />
}
