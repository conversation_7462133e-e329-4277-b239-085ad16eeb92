"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { ContactsAPI } from "@/lib/services/contactsApi"

// Contact editor configuration for creating new contacts
const contactEditorConfig: DataEditorConfig = {
  title: "Contact",
  subtitle: "Create new customer contact",

  fields: [
    // Basic Information
    {
      name: "name",
      label: "Full Name",
      type: "text",
      placeholder: "Enter full name",
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "phone",
      label: "Phone Number",
      type: "phone",
      placeholder: "+628123456789",
      validation: {
        required: true,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
      },
      description: "WhatsApp number for customer communication",
      group: "basic",
    },
    {
      name: "email",
      label: "Email Address",
      type: "email",
      placeholder: "<EMAIL>",
      validation: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      },
      group: "basic",
    },

    // Classification
    {
      name: "tags",
      label: "Tags",
      type: "text",
      placeholder: "VIP, Regular Customer, New Lead",
      description: "Separate multiple tags with commas",
      group: "classification",
    },

    // Additional Information
    {
      name: "company",
      label: "Company",
      type: "text",
      placeholder: "Company name",
      validation: {
        maxLength: 100,
      },
      group: "additional",
    },
    {
      name: "position",
      label: "Position",
      type: "text",
      placeholder: "Job title",
      validation: {
        maxLength: 100,
      },
      group: "additional",
    },
    {
      name: "address",
      label: "Address",
      type: "textarea",
      placeholder: "Enter full address",
      rows: 3,
      validation: {
        maxLength: 500,
      },
      group: "additional",
    },
    {
      name: "birthday",
      label: "Birthday",
      type: "date",
      group: "additional",
    },

    // Notes
    {
      name: "notes",
      label: "Notes",
      type: "textarea",
      placeholder: "Important customer, Potential high-value client",
      rows: 4,
      validation: {
        maxLength: 1000,
      },
      group: "notes",
      description: "Separate multiple tags with commas",
    },
  ],

  sections: [
    {
      title: "Basic Information",
      description: "Essential contact details",
      fields: ["name", "phone", "email"],
    },
    {
      title: "Classification",
      description: "Tags and categories",
      fields: ["tags"],
    },
    {
      title: "Additional Information",
      description: "Optional details",
      fields: ["company", "position", "address", "birthday"],
    },
    {
      title: "Notes",
      description: "Additional information and remarks",
      fields: ["notes"],
    },
  ],

  // Data operations
  saveData: async (data: Record<string, any>, _isEdit: boolean) => {
    try {
      // Transform tags from comma-separated string to array
      // Note: Some fields (company, position, address, birthday) are not in the Contact interface
      // but we'll include them in the payload for the API to handle
      const transformedData = {
        name: data.name,
        phone: data.phone,
        email: data.email || undefined,
        tags: data.tags
          ? data.tags
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
          : [],
        notes: data.notes
          ? data.notes
              .split(",")
              .map((tag: string) => tag.trim())
              .filter(Boolean)
              .map((note: string) => {
                return {
                  text: note,
                  createdAt: new Date().toISOString(),
                }
              })
          : [],
        // Extended fields (not in Contact interface but used in form)
        ...(data.company && { company: data.company }),
        ...(data.position && { position: data.position }),
        ...(data.address && { address: data.address }),
        ...(data.birthday && { birthday: data.birthday }),
      }

      await ContactsAPI.Create(transformedData).request()
    } catch (error: any) {
      console.error("Error creating contact:", error)

      // Extract error message from the response
      let errorMessage = "Failed to create contact"
      if (
        error?.response?.data?.messages &&
        error.response.data.messages.length > 0
      ) {
        errorMessage = error.response.data.messages[0]
      } else if (error?.message) {
        errorMessage = error.message
      }

      throw new Error(errorMessage)
    }
  },

  // Navigation
  backRoute: "/contacts",
  successRoute: "/contacts",

  // Customization
  submitButtonText: "Create Contact",
  cancelButtonText: "Cancel",
  showImagePreview: false,
  maxFileSize: 5, // 5MB
  allowedFileTypes: ["image/jpeg", "image/png", "image/gif"],
}

export default function NewContactPage() {
  return <DataEditorPage config={contactEditorConfig} />
}
