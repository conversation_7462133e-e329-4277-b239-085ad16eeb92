import { getContactsSearchConfig } from "@/lib/utils/searchConfigBuilder"
import ContactsClient from "./client"

export default async function ContactsPage() {
  // ✨ Build search config directly using business logic (SSR)
  const searchConfig = getContactsSearchConfig()

  if (!searchConfig) {
    return <div>Error: Could not load search configuration</div>
  }

  return <ContactsClient searchConfig={searchConfig} />
}
