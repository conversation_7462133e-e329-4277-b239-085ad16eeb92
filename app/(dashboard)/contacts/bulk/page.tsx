"use client"

import DataBulkPage from "@/components/crud-page/DataBulkPageSimple"
import type { DataBulkConfig } from "@/components/crud-page/DataBulkPageSimple"
import type {
  ImportResult,
  ValidationError,
} from "@/components/crud-page/BulkImportComponent"
import type { DeleteResult } from "@/components/crud-page/BulkDeleteComponent"
import { ContactsAPI } from "@/lib/services/contactsApi"
import { toast } from "sonner"

// Contacts bulk operations configuration
let myNotes: { text: string; createdAt: string }[] = []
const contactsBulkConfig: DataBulkConfig = {
  title: "Contacts",
  subtitle: "Bulk import, update, and delete contact records",
  backRoute: "/contacts",

  // Enable all operations
  enableImport: true,
  enableUpdate: true,
  enableDelete: true,

  // Import configuration
  importConfig: {
    fields: [
      {
        name: "name",
        label: "Full Name",
        required: true,
        type: "string",
        example: "<PERSON>",
      },
      {
        name: "phone",
        label: "Phone Number",
        required: true,
        type: "phone",
        example: "+1234567890",
      },
      {
        name: "email",
        label: "Email Address",
        required: false,
        type: "email",
        example: "<EMAIL>",
      },
      {
        name: "tags",
        label: "Tags",
        required: false,
        type: "string",
        example: "VIP,Customer",
      },
      {
        name: "company",
        label: "Company",
        required: false,
        type: "string",
        example: "Acme Corp",
      },
      {
        name: "position",
        label: "Position",
        required: false,
        type: "string",
        example: "Manager",
      },
      {
        name: "address",
        label: "Address",
        required: false,
        type: "string",
        example: "123 Main St",
      },
      {
        name: "birthday",
        label: "Birthday",
        required: false,
        type: "date",
        example: "1990-01-15",
      },
      {
        name: "notes",
        label: "Notes",
        required: false,
        type: "string",
        example: "Important customer",
      },
    ],
    maxFileSize: 10,
    maxRecords: 1000,
    supportedFormats: ["csv", "json"],

    generateTemplate: () => [
      {
        id: Math.floor(Math.random() * 10 ** 10), // Unique id for template
        name: "John Doe" + new Date().getTime(), // Unique name for template
        phone: `+${Math.floor(Math.random() * 10 ** 10)}`,
        email: "<EMAIL>",
        tags: "VIP,Customer",
        company: "Acme Corp",
        position: "Manager",
        address: "123 Main St, City, State",
        birthday: "1990-01-15",
        notes: "Important customer",
      },
      {
        id: Math.floor(Math.random() * 10 ** 10), // Unique id for template
        name: "Jane Smith" + new Date().getTime(),
        phone: `+${Math.floor(Math.random() * 10 ** 10)}`,
        email: "<EMAIL>",
        tags: "Lead,Prospect",
        company: "Tech Solutions",
        position: "Director",
        address: "456 Oak Ave, City, State",
        birthday: "1985-03-22",
        notes: "Potential high-value client",
      },
    ],

    validateData: (data: Record<string, any>[]): ValidationError[] => {
      const errors: ValidationError[] = []

      data.forEach((item, index) => {
        // Required field validation
        if (!item.name || item.name.trim() === "") {
          errors.push({
            row: index,
            field: "name",
            message: "Name is required",
          })
        }
        if (!item.phone || item.phone.trim() === "") {
          errors.push({
            row: index,
            field: "phone",
            message: "Phone number is required",
          })
        }

        // Format validation
        if (item.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(item.email)) {
          errors.push({
            row: index,
            field: "email",
            message: "Invalid email format",
          })
        }
        if (
          item.phone &&
          !/^[\+]?[1-9][\d]{0,15}$/.test(item.phone.replace(/\s/g, ""))
        ) {
          errors.push({
            row: index,
            field: "phone",
            message: "Invalid phone format",
          })
        }
        if (item.birthday && !/^\d{4}-\d{2}-\d{2}$/.test(item.birthday)) {
          errors.push({
            row: index,
            field: "birthday",
            message: "Birthday should be in YYYY-MM-DD format",
          })
        }

        // Length validation
        if (item.name && item.name.length > 100) {
          errors.push({
            row: index,
            field: "name",
            message: "Name must not exceed 100 characters",
          })
        }
      })

      return errors
    },

    importData: async (data: Record<string, any>[]): Promise<ImportResult> => {
      try {
        const transformedData = data.map((item) => ({
          name: item.name,
          phone: item.phone,
          email: item.email || undefined,
          tags: item.tags
            ? item.tags
                .split(",")
                .map((tag: string) => tag.trim())
                .filter(Boolean)
            : [],
          company: item.company || undefined,
          position: item.position || undefined,
          address: item.address || undefined,
          birthday: item.birthday || undefined,
          notes: item.notes
            ? [{ text: item.notes, createdAt: new Date().toISOString() }]
            : [],
        }))

        const result = await ContactsAPI.BulkCreate(transformedData).request()

        return {
          total: result.total,
          successful: result.successful,
          failed: result.failed,
          errors: (result.errors || []).map((error) => ({
            row: error.row || 0,
            field: error.field || "general",
            message: error.message,
          })),
        }
      } catch (error: any) {
        console.error("Bulk import error:", error)
        return {
          total: data.length,
          successful: 0,
          failed: data.length,
          errors: data.map((_, index) => ({
            row: index,
            field: "general",
            message: error?.message || "Import failed",
          })),
        }
      }
    },

    headers: ["Name", "Phone", "Email", "Tags"],
    transformToTableRow: (item: Record<string, any>) => ({
      id: item.id,
      columns: [
        item.name || "",
        item.phone || "",
        item.email || "",
        item.tags ? item.tags : "",
      ],
    }),
  },

  // Update configuration
  updateConfig: {
    fetchData: async () => {
      try {
        const result = await ContactsAPI.All({
          page: 1,
          per_page: 1000, // Get all contacts for bulk operations
          sort: [],
          filters: [],
        }).request()
        myNotes = result.items.map((item) => item.notes).flat() as [
          { text: string; createdAt: string },
        ]

        return (
          result.items.map((a) => {
            a.notes = a.notes
              ? a.notes.map((note) => note.text).join(", ")
              : ("" as any)
            return a
          }) || []
        )
      } catch (error) {
        console.error("Error fetching contacts for bulk update:", error)
        toast.error("Failed to fetch contacts for bulk update")
        return []
      }
    },

    updateData: async (data: Record<string, any>[]) => {
      try {
        data.map((item) => {
          if (item.birthday && /^\d{2}\/\d{2}\/\d{4}$/.test(item.birthday)) {
            const [day, month, year] = item.birthday.split("/")
            item.birthday = `${year}-${month}-${day}`
          }
          item.tags = item.tags
            ? item.tags
                .split(",")
                .map((tag: string) => tag.trim())
                .filter(Boolean)
            : []
          item.notes = item.notes
            ? item.notes
                .split(",")
                .map((tag: string) => tag.trim())
                .filter(Boolean)
                .map((note: string) => {
                  const existingNote = myNotes.find((a) => a.text === note)
                  return {
                    text: note,
                    createdAt:
                      existingNote?.createdAt || new Date().toISOString(),
                  }
                })
            : []

          return item
        })
        const result = await ContactsAPI.BulkUpdate(
          data as Array<{ id: string; [key: string]: any }>,
        ).request()

        return {
          total: result.total,
          successful: result.successful,
          failed: result.failed,
          errors: (result.errors || []).map((error) => ({
            row: error.row || 0,
            field: error.field || "general",
            message: error.message,
          })),
        }
      } catch (error: any) {
        console.error("Bulk update error:", error)
        return {
          total: data.length,
          successful: 0,
          failed: data.length,
          errors: data.map((_, index) => ({
            row: index,
            field: "general",
            message: error?.message || "Update failed",
          })),
        }
      }
    },

    headers: ["Name", "Phone", "Email", "Tags", "Created Date"],
    transformToTableRow: (item: Record<string, any>) => ({
      id: item.id,
      columns: [
        item.name || "",
        item.phone || "",
        item.email || "",
        item.tags ? item.tags.join(", ") : "",
        item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "",
      ],
    }),

    importConfig: {
      fields: [
        {
          name: "id",
          label: "ID",
          required: true,
          type: "string",
          example: "123",
        },
        {
          name: "name",
          label: "Full Name",
          required: true,
          type: "string",
          example: "John Doe",
        },
        {
          name: "phone",
          label: "Phone Number",
          required: true,
          type: "phone",
          example: "+1234567890",
        },
        {
          name: "email",
          label: "Email Address",
          required: false,
          type: "email",
          example: "<EMAIL>",
        },
        {
          name: "company",
          label: "Company",
          required: false,
          type: "string",
          example: "Acme Corp",
        },
        {
          name: "tags",
          label: "Tags",
          required: false,
          type: "string",
          example: "VIP,Customer",
        },
      ],
      maxFileSize: 10,
      maxRecords: 1000,
      supportedFormats: ["csv", "json"],
      generateTemplate: () => [],
      validateData: () => [],
      importData: async () => ({
        total: 0,
        successful: 0,
        failed: 0,
        errors: [],
      }),
    },
  },

  // Delete configuration
  deleteConfig: {
    fetchData: async () => {
      try {
        const result = await ContactsAPI.All({
          page: 1,
          per_page: 1000, // Get all contacts for bulk operations
          sort: [],
          filters: [],
        }).request()

        return result.items || []
      } catch (error) {
        console.error("Error fetching contacts for bulk delete:", error)
        toast.error("Failed to fetch contacts")
        return []
      }
    },

    deleteData: async (ids: string[]): Promise<DeleteResult> => {
      try {
        const result = await ContactsAPI.BulkDelete(ids).request()

        return {
          total: result.total,
          successful: result.successful,
          failed: result.failed,
          errors: (result.errors || []).map((error) => ({
            id: error.id || "",
            message: error.message,
          })),
        }
      } catch (error: any) {
        console.error("Bulk delete error:", error)
        return {
          total: ids.length,
          successful: 0,
          failed: ids.length,
          errors: ids.map((id) => ({
            id,
            message: error?.message || "Delete failed",
          })),
        }
      }
    },

    headers: ["Name", "Phone", "Email", "Company", "Created Date"],
    transformToTableRow: (item: Record<string, any>) => ({
      id: item.id,
      columns: [
        item.name || "",
        item.phone || "",
        item.email || "",
        item.company || "",
        item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "",
      ],
    }),

    itemName: "contact",
    itemNamePlural: "contacts",
  },
}

export default function ContactsBulkPage() {
  return <DataBulkPage config={contactsBulkConfig} />
}
