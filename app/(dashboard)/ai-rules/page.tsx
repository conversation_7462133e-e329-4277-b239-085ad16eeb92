import { getAiRulesSearchConfig } from "@/lib/utils/searchConfigBuilder"
import AiRulesClient from "./client"

export default async function AiRulesPage() {
  // ✨ Build search config directly using business logic (SSR)
  const searchConfig = getAiRulesSearchConfig()

  if (!searchConfig) {
    return <div>Error: Could not load search configuration</div>
  }

  return <AiRulesClient searchConfig={searchConfig} />
}
