"use client"

import { TableRowData } from "@/components/crud-page"
import type { DataPageEnhancedConfig } from "@/components/crud-page/DataPageEnhanced"
import DataPageEnhanced from "@/components/crud-page/DataPageEnhanced"
import { DEFAULT_PER_PAGE } from "@/components/crud-page/default_per_page"
import {
  StatBreakdown,
  StatItem,
  StatsResponse,
} from "@/components/crud-page/stats"
import { useLocalization } from "@/localization/functions/client"
import { AiRule } from "@/lib/repositories/aiRules"
import { AiRulesAPI } from "@/lib/services/aiRulesApi"
import { SearchConfig } from "@/lib/types/searchConfig"
import { deepMerge } from "@/lib/utils/deepMerge"
import { apiLocales } from "@/app/api/locales"
import { aiRulesLocales } from "./locales"

// ✨ Convert API stats to generic frontend format
const mapAiRuleStatsToGeneric = (
  backendData: any,
  t: (key: string, template?: Record<string, string | number>) => string,
): StatsResponse => {
  const data = backendData.data || backendData

  const stats: StatItem[] = [
    {
      id: "total_rules",
      title: t("stats.total_rules"),
      value: data.total || 0,
      label: `${data.recent || 0} ${t("stats.recent_added")}`,
      description: t("stats.total_description"),
      icon: "robot",
      cardColor: "bg-blue-50",
      textColor: "text-blue-900",
    },
    {
      id: "rules_with_tags",
      title: t("stats.with_tags"),
      value: data.withTags || 0,
      label: `${((data.withTags / (data.total || 1)) * 100).toFixed(1)}${t("stats.percentage")}`,
      cardColor: "bg-green-50",
      textColor: "text-green-900",
    },
    {
      id: "unique_descriptions",
      title: t("stats.unique_descriptions"),
      value: data.uniqueCategories || 0,
      label: t("stats.categories_label"),
      cardColor: "bg-purple-50",
      textColor: "text-purple-900",
    },
    {
      id: "active_rules",
      title: t("stats.active_rules"),
      value: data.active || 0,
      label: `${((data.active / (data.total || 1)) * 100).toFixed(1)}${t("stats.percentage")}`,
      cardColor: "bg-orange-50",
      textColor: "text-orange-900",
    },
  ]

  const breakdowns: StatBreakdown[] = []

  // Status breakdown
  if (data.statusBreakdown && data.statusBreakdown.length > 0) {
    breakdowns.push({
      id: "status_breakdown",
      title: t("stats.status_breakdown"),
      items: data.statusBreakdown.map((item: any) => ({
        label: item.status,
        value: item.count,
        percentage: item.percentage,
        color: item.status === "Active" ? "#10B981" : "#EF4444",
      })),
    })
  }

  // Tag breakdown
  if (data.tagBreakdown && data.tagBreakdown.length > 0) {
    breakdowns.push({
      id: "tag_breakdown",
      title: t("stats.tag_breakdown"),
      items: data.tagBreakdown.map((item: any, index: number) => ({
        label: item.tag,
        value: item.count,
        percentage: item.percentage,
        color: [
          "#3B82F6",
          "#8B5CF6",
          "#06B6D4",
          "#10B981",
          "#F59E0B",
          "#EF4444",
        ][index % 6],
      })),
    })
  }

  return {
    stats,
    breakdowns,
    summary: {
      total: data.total || 0,
      period: t("stats.all_time"),
      lastUpdated: new Date().toISOString(),
    },
  }
}

interface AiRulesClientProps {
  searchConfig: SearchConfig
}

export default function AiRulesClient({ searchConfig }: AiRulesClientProps) {
  const { t } = useLocalization(
    "ai-rules",
    deepMerge(aiRulesLocales, apiLocales),
  )

  const config: DataPageEnhancedConfig<AiRule> = {
    title: t("page_title"),
    subtitle: t("page_subtitle"),
    headers: [
      t("headers.name"),
      t("headers.description"),
      t("headers.tags"),
      t("headers.is_active"),
      t("headers.created_date"),
      t("headers.updated_date"),
      t("headers.created_by"),
    ],
    searchConfig: searchConfig,

    deleteItem: async (id: string): Promise<void> => {
      await AiRulesAPI.Delete(id).request()
    },
    addRoute: "/ai-rules/new",
    editRoute: (id: string) => `/ai-rules/${id}`,
    bulkRoute: "/ai-rules/bulk",

    fetchData: async (params: {
      search?: string
      includeDeleted?: boolean
      page?: number
      limit?: number
      sort?: Array<{ field: string; direction: "ASC" | "DESC" }>
      filters?: Array<{ field: string; operator?: string; value: any }>
    }) => {
      let apiParams = {
        page: params?.page || 1,
        per_page: params?.limit || DEFAULT_PER_PAGE,
        search: params?.search,
        sort: params?.sort || [],
        filters: params?.filters || [],
      }

      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || []
        apiParams.filters.push({ field: "includeDeleted", value: true })
      }

      const response = await AiRulesAPI.All(apiParams).request()

      return {
        items: response.items || [],
        total: response.total || 0,
        totalPages: Math.ceil(response.total / DEFAULT_PER_PAGE),
      }
    },
    transformToTableRow: (item: AiRule): TableRowData => {
      return {
        id: item.id,
        columns: [
          item.name,
          item.description || "-",
          item.tags ? item.tags.join(", ") : "-",
          item.isActive ? t("common.yes") : t("common.no"),
          new Date(item.createdAt).toLocaleDateString("id-ID"),
          new Date(item.updatedAt).toLocaleDateString("id-ID"),
          item.createdBy || "-",
        ],
      }
    },
  }

  return <DataPageEnhanced config={config} />
}
