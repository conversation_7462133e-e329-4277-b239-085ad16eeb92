{"page_title": "Customer AiRules", "page_subtitle": "Manage and monitor customer aiRules for CS operations", "headers": {"name": "Name", "phone": "Phone", "email": "Email", "tags": "Tags", "notes_count": "Notes Count", "created_date": "Created Date", "updated_date": "Updated Date", "created_by": "Created By"}, "sort_options": {"name": "Name", "phone": "Phone", "email": "Email", "created_at": "Created Date", "updated_at": "Updated Date"}, "date_filter_options": {"today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "all": "All Time"}, "filters": {"has_phone": "Has Phone", "has_email": "<PERSON>", "has_tags": "Has Tags"}, "stats": {"name": "AiRule Statistics", "total_aiRules": "Total AiRules", "active_aiRules": "Active AiRules", "aiRules_with_email": "With Email", "aiRules_with_tags": "With Tags", "added_recently": "added recently", "all_aiRules_description": "All aiRules in the system", "percentage_of_total": "% of total", "aiRule_status": "AiRule Status", "top_tags": "Top Tags", "created_by": "Created By", "all_time": "all time", "status_options": {"active": "Active", "deleted": "Deleted", "pending": "Pending", "archived": "Archived"}}, "routes": {"add": "/aiRules/new", "edit": "/aiRules/{{id}}", "bulk": "/aiRules/bulk"}, "labels": {"tag": "Tag", "created_by": "Created By", "aiRule": "AiRule", "interactions": "Interactions"}}