"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { AiRulesAPI } from "@/lib/services/aiRulesApi"
import { AiRule } from "@/lib/repositories/aiRules/interface"

interface ClientPageProps {
  id: string
}

const aiRuleEditorConfig: DataEditorConfig = {
  name: "Edit AI Rule",
  subtitle: "Modify rule conditions, actions, and metadata",

  fields: [
    {
      name: "name",
      label: "Rule Name",
      type: "text",
      placeholder: "e.g., Abandoned Cart Reminder",
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Describe what this rule does...",
      rows: 4,
      validation: {
        maxLength: 500,
      },
      group: "basic",
    },
    {
      name: "conditions",
      label: "Conditions",
      type: "text",
      placeholder: "e.g., cart_abandoned, user_logged_in",
      description: "Comma-separated list of trigger conditions",
      validation: {
        required: true,
      },
      group: "logic",
    },
    {
      name: "actions",
      label: "Actions",
      type: "text",
      placeholder: "e.g., send_email, notify_admin",
      description: "Comma-separated list of actions to perform",
      validation: {
        required: true,
      },
      group: "logic",
    },
    {
      name: "tags",
      label: "Tags",
      type: "text",
      placeholder: "marketing, retention, automation",
      description: "Optional comma-separated labels for grouping",
      group: "meta",
    },
    {
      name: "isActive",
      label: "Active",
      type: "checkbox",
      description: "Enable or disable this rule",
      group: "meta",
    },
    {
      name: "createdAt",
      label: "Created Date",
      type: "text",
      disabled: true,
      group: "system",
    },
    {
      name: "updatedAt",
      label: "Last Updated",
      type: "text",
      disabled: true,
      group: "system",
    },
    {
      name: "createdBy",
      label: "Created By",
      type: "text",
      disabled: true,
      group: "system",
    },
  ],

  sections: [
    {
      name: "Basic Info",
      description: "Name and summary of the rule",
      fields: ["name", "description"],
    },
    {
      name: "Rule Logic",
      description: "Define the conditions and actions",
      fields: ["conditions", "actions"],
    },
    {
      name: "Metadata",
      description: "Tags and status",
      fields: ["tags", "isActive"],
    },
    {
      name: "System Info",
      description: "Tracking information",
      fields: ["createdAt", "updatedAt", "createdBy"],
    },
  ],

  fetchData: async (id: string) => {
    try {
      const rule: AiRule = await AiRulesAPI.Detail(id).request()

      if (!rule) throw new Error("AI Rule not found")

      return {
        id: rule.id,
        name: rule.name,
        description: rule.description || "",
        conditions: rule.conditions?.join(", ") || "",
        actions: rule.actions?.join(", ") || "",
        tags: rule.tags?.join(", ") || "",
        isActive: rule.isActive || false,
        createdAt: rule.createdAt
          ? new Date(rule.createdAt).toLocaleString("id-ID")
          : "",
        updatedAt: rule.updatedAt
          ? new Date(rule.updatedAt).toLocaleString("id-ID")
          : "",
        createdBy: rule.createdBy || "System",
      }
    } catch (error: any) {
      console.error("Error fetching AI Rule:", error)
      throw new Error(error?.message || "Failed to fetch AI Rule")
    }
  },

  saveData: async (data: Record<string, any>) => {
    try {
      const payload: Partial<AiRule> = {
        name: data.name,
        description: data.description,
        conditions: data.conditions
          ? data.conditions
              .split(",")
              .map((c: string) => c.trim())
              .filter(Boolean)
          : [],
        actions: data.actions
          ? data.actions
              .split(",")
              .map((a: string) => a.trim())
              .filter(Boolean)
          : [],
        tags: data.tags
          ? data.tags
              .split(",")
              .map((t: string) => t.trim())
              .filter(Boolean)
          : [],
        isActive: data.isActive ?? true,
      }

      await AiRulesAPI.Update(data.id, payload).request()
    } catch (error: any) {
      console.error("Error updating AI Rule:", error)

      let message = "Failed to update AI Rule"
      if (error?.response?.data?.messages?.length > 0) {
        message = error.response.data.messages[0]
      } else if (error?.message) {
        message = error.message
      }

      throw new Error(message)
    }
  },

  backRoute: "/ai-rules",
  successRoute: "/ai-rules",

  submitButtonText: "Update Rule",
  cancelButtonText: "Cancel",
  showImagePreview: false,
}

export default function ClientPage({ id }: ClientPageProps) {
  return <DataEditorPage config={aiRuleEditorConfig} id={id} />
}
