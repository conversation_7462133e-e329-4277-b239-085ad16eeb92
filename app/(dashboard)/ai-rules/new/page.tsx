"use client"

import { DataEditorPage, DataEditorConfig } from "@/components/crud-page"
import { AiRulesAPI } from "@/lib/services/aiRulesApi"
import { AiRule } from "@/lib/repositories/aiRules/interface"

const aiRuleEditorConfig: DataEditorConfig = {
  name: "New AI Rule",
  subtitle: "Create a new AI rule for automation or alerts",

  fields: [
    {
      name: "name",
      label: "Rule Name",
      type: "text",
      placeholder: "e.g., Abandoned Cart Trigger",
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100,
      },
      group: "basic",
    },
    {
      name: "description",
      label: "Description",
      type: "textarea",
      placeholder: "Describe what this rule does...",
      rows: 4,
      validation: {
        maxLength: 500,
      },
      group: "basic",
    },
    {
      name: "conditions",
      label: "Conditions",
      type: "text",
      placeholder: "e.g., cart_abandoned, user_logged_in",
      description: "Comma-separated list of triggering conditions",
      validation: {
        required: true,
      },
      group: "logic",
    },
    {
      name: "actions",
      label: "Actions",
      type: "text",
      placeholder: "e.g., send_email, notify_admin",
      description: "Comma-separated list of actions this rule should take",
      validation: {
        required: true,
      },
      group: "logic",
    },
    {
      name: "tags",
      label: "Tags",
      type: "text",
      placeholder: "e.g., automation, reminder, marketing",
      description: "Optional tags to help organize rules",
      group: "meta",
    },
    {
      name: "isActive",
      label: "Active",
      type: "checkbox",
      description: "Enable this rule upon creation",
      group: "meta",
    },
  ],

  sections: [
    {
      name: "Basic Info",
      description: "Identify the rule with a name and description",
      fields: ["name", "description"],
    },
    {
      name: "Rule Logic",
      description: "Define when the rule runs and what it does",
      fields: ["conditions", "actions"],
    },
    {
      name: "Metadata",
      description: "Tags and settings for organization",
      fields: ["tags", "isActive"],
    },
  ],

  saveData: async (data: Record<string, any>, _isEdit: boolean) => {
    try {
      const payload: Partial<AiRule> = {
        name: data.name,
        description: data.description,
        conditions: data.conditions
          ? data.conditions
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        actions: data.actions
          ? data.actions
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        tags: data.tags
          ? data.tags
              .split(",")
              .map((v: string) => v.trim())
              .filter(Boolean)
          : [],
        isActive: !!data.isActive,
      }

      await AiRulesAPI.Create(payload).request()
    } catch (error: any) {
      console.error("Error creating AI rule:", error)

      let errorMessage = "Failed to create AI rule"
      if (error?.response?.data?.messages?.length > 0) {
        errorMessage = error.response.data.messages[0]
      } else if (error?.message) {
        errorMessage = error.message
      }

      throw new Error(errorMessage)
    }
  },

  backRoute: "/ai-rules",
  successRoute: "/ai-rules",

  submitButtonText: "Create Rule",
  cancelButtonText: "Cancel",
  showImagePreview: false,
}

export default function NewAiRulePage() {
  return <DataEditorPage config={aiRuleEditorConfig} />
}
