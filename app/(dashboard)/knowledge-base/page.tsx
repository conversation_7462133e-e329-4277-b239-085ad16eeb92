"use client"

import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function KnowledgeBaseLandingPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  return (
    <div className="max-w-4xl mx-auto mt-20 p-6 bg-white rounded-md shadow-md space-y-8">
      <h1 className="text-3xl font-bold">{t("title")}</h1>

      <p className="text-lg text-gray-700">{t("description")}</p>

      <section className="space-y-4">
        <h2 className="text-xl font-semibold">{t("landing.why_use_title")}</h2>
        <ul className="list-disc pl-5 space-y-2 text-gray-700">
          <li>{t("landing.why_use_item_1")}</li>
          <li>{t("landing.why_use_item_2")}</li>
          <li>{t("landing.why_use_item_3")}</li>
          <li>{t("landing.why_use_item_4")}</li>
          <li>{t("landing.why_use_item_5")}</li>
        </ul>
      </section>

      <section className="space-y-4">
        <h2 className="text-xl font-semibold">
          {t("landing.get_started_title")}
        </h2>
        <p className="text-gray-700">{t("landing.get_started_desc")}</p>
        <div className="flex gap-4 mt-4">
          <Link href="/knowledge-base/text">
            <Button>{t("start_editing")}</Button>
          </Link>
          <Link href="/knowledge-base/documents">
            <Button variant="outline">{t("manage_documents")}</Button>
          </Link>
        </div>
      </section>
    </div>
  )
}
