"use client"

import { useState, useEffect, useRef } from "react"
import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import { KnowledgeBaseAPI } from "@/lib/services/knowledgeBaseApi"

export default function DocumentUploadPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  const [files, setFiles] = useState<
    { id: string; name: string; size: number }[]
  >([])
  const [uploading, setUploading] = useState(false)
  const [message, setMessage] = useState("")
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  // Fetch document list on mount
  const fetchDocuments = async () => {
    try {
      const res = await KnowledgeBaseAPI.ListDocuments().request()
      setFiles(res.documents || [])
    } catch (error) {
      console.error("Failed to fetch documents", error)
      setMessage(t("knowledgeBase.fetch_documents_error"))
    }
  }

  useEffect(() => {
    fetchDocuments()
  }, [])

  const handleUpload = async () => {
    if (!fileInputRef.current?.files?.length) return

    const file = fileInputRef.current.files[0]
    setUploading(true)
    setMessage("")

    try {
      const res = await KnowledgeBaseAPI.UploadDocument({ file }).request()
      if (res.success) {
        setMessage(t("knowledgeBase.upload_success"))
        fileInputRef.current.value = "" // reset input
        await fetchDocuments()
      } else {
        setMessage(t("knowledgeBase.upload_error"))
      }
    } catch (error: any) {
      setMessage(error.message || t("knowledgeBase.upload_error"))
    } finally {
      setUploading(false)
    }
  }

  const handleRemove = async (id: string) => {
    setMessage("")
    try {
      const res = await KnowledgeBaseAPI.RemoveDocument({ id }).request()
      if (res.success) {
        setMessage(t("knowledgeBase.remove_success"))
        await fetchDocuments()
      } else {
        setMessage(t("knowledgeBase.remove_error"))
      }
    } catch (error: any) {
      setMessage(error.message || t("knowledgeBase.remove_error"))
    }
  }

  return (
    <div className="max-w-4xl mx-auto mt-20 p-6 bg-white rounded-md shadow-md space-y-6">
      <h1 className="text-2xl font-bold">
        {t("knowledgeBase.documents_title")}
      </h1>
      <p className="text-gray-600">
        {t("knowledgeBase.documents_description")}
      </p>

      <div>
        <input
          type="file"
          accept=".pdf,.doc,.docx,.txt"
          ref={fileInputRef}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          disabled={uploading}
          onChange={() => setMessage("")}
        />
        <Button
          onClick={handleUpload}
          disabled={uploading || !fileInputRef.current?.files?.length}
          className="mt-3"
        >
          {uploading
            ? t("knowledgeBase.uploading")
            : t("knowledgeBase.upload_button")}
        </Button>
      </div>

      {message && <p className="text-sm text-green-600">{message}</p>}

      <div>
        <h2 className="font-semibold text-lg mb-2">
          {t("knowledgeBase.uploaded_files")}
        </h2>
        {files.length === 0 && (
          <p className="text-gray-600">
            {t("knowledgeBase.no_files")}
          </p>
        )}

        <ul className="space-y-2">
          {files.map(({ id, name, size }) => (
            <li
              key={id}
              className="flex justify-between items-center border rounded p-2"
            >
              <div>
                <p className="font-medium">{name}</p>
                <p className="text-xs text-gray-500">
                  {(size / 1024).toFixed(1)} KB
                </p>
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleRemove(id)}
              >
                {t("knowledgeBase.remove_button")}
              </Button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
