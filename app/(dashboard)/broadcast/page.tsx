"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Plus,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Calendar,
} from "lucide-react"
import { BroadcastAPI } from "@/lib/services"
import {
  Broadcast,
  BroadcastStatus,
} from "@/lib/repositories/broadcast/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"

const statusColors = {
  [BroadcastStatus.DRAFT]: "bg-gray-100 text-gray-800",
  [BroadcastStatus.SCHEDULED]: "bg-blue-100 text-blue-800",
  [BroadcastStatus.SENDING]: "bg-yellow-100 text-yellow-800",
  [BroadcastStatus.COMPLETED]: "bg-green-100 text-green-800",
  [BroadcastStatus.FAILED]: "bg-red-100 text-red-800",
  [BroadcastStatus.CANCELLED]: "bg-gray-100 text-gray-800",
}

export default function BroadcastPage() {
  const { t } = useLocalization("broadcast", locales)
  const router = useRouter()
  const [broadcasts, setBroadcasts] = useState<Broadcast[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)

  const fetchBroadcasts = async (page = 1, search = "") => {
    try {
      setLoading(true)
      const response = await BroadcastAPI.All({
        page,
        per_page: 10,
        search: search || undefined,
        sort: [{ field: "createdAt", direction: "DESC" }],
      }).request()

      setBroadcasts(response.items)
      setTotalPages(1)
      setTotalItems(response.total)
      setCurrentPage(page)
    } catch (error) {
      console.error(t("page.errors.fetch_failed"), error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBroadcasts()
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchBroadcasts(1, searchTerm)
  }

  const handleDelete = async (id: string) => {
    if (!confirm(t("page.confirmations.delete"))) return

    try {
      await BroadcastAPI.Delete(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.delete_failed"), error)
    }
  }

  const handleStartBroadcast = async (id: string) => {
    try {
      await BroadcastAPI.Start(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.start_failed"), error)
    }
  }

  const handleCancelBroadcast = async (id: string) => {
    if (!confirm(t("page.confirmations.cancel"))) return

    try {
      await BroadcastAPI.Cancel(id).request()
      fetchBroadcasts(currentPage, searchTerm)
    } catch (error) {
      console.error(t("page.errors.cancel_failed"), error)
    }
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getSuccessRate = (broadcast: Broadcast) => {
    if (broadcast.totalTargets === 0) return 0
    return Math.round((broadcast.sentCount / broadcast.totalTargets) * 100)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{t("page.title")}</h1>
          <p className="text-gray-600">
            {t("page.subtitle")}
          </p>
        </div>
        <Button
          onClick={() => router.push("/broadcast/new")}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          {t("page.new_broadcast")}
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>{t("page.search.title")}</CardTitle>
          <CardDescription>
            {t("page.search.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-2">
            <Input
              placeholder={t("page.search.placeholder")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Broadcasts Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("page.table.title", { count: totalItems })}</CardTitle>
          <CardDescription>{t("page.table.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : broadcasts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">{t("page.states.no_broadcasts")}</p>
              <Button
                onClick={() => router.push("/broadcast/new")}
                className="mt-4"
                variant="outline"
              >
                {t("page.states.create_first")}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("page.table.headers.title")}</TableHead>
                    <TableHead>{t("page.table.headers.status")}</TableHead>
                    <TableHead>{t("page.table.headers.targets")}</TableHead>
                    <TableHead>{t("page.table.headers.progress")}</TableHead>
                    <TableHead>{t("page.table.headers.success_rate")}</TableHead>
                    <TableHead>{t("page.table.headers.created")}</TableHead>
                    <TableHead className="text-right">{t("page.table.headers.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {broadcasts.map((broadcast) => (
                    <TableRow key={broadcast.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{broadcast.title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {broadcast.message}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={statusColors[broadcast.status]}>
                          {t(`status.${broadcast.status}`)}
                        </Badge>
                      </TableCell>
                      <TableCell>{broadcast.totalTargets}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{t("page.table.progress.sent", { count: broadcast.sentCount })}</div>
                          <div>{t("page.table.progress.failed", { count: broadcast.failedCount })}</div>
                          <div>{t("page.table.progress.pending", { count: broadcast.pendingCount })}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getSuccessRate(broadcast)}%</TableCell>
                      <TableCell>{formatDate(broadcast.createdAt)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() =>
                                router.push(`/broadcast/${broadcast.id}`)
                              }
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              {t("page.actions.view_details")}
                            </DropdownMenuItem>
                            {broadcast.status === BroadcastStatus.DRAFT && (
                              <>
                                <DropdownMenuItem
                                  onClick={() =>
                                    router.push(
                                      `/broadcast/${broadcast.id}/edit`,
                                    )
                                  }
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  {t("page.actions.edit")}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleStartBroadcast(broadcast.id)
                                  }
                                >
                                  <Play className="mr-2 h-4 w-4" />
                                  {t("page.actions.start_broadcast")}
                                </DropdownMenuItem>
                              </>
                            )}
                            {broadcast.status === BroadcastStatus.SENDING && (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleCancelBroadcast(broadcast.id)
                                }
                              >
                                <Pause className="mr-2 h-4 w-4" />
                                {t("page.actions.cancel_broadcast")}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDelete(broadcast.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("page.actions.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fetchBroadcasts(currentPage - 1, searchTerm)}
                    disabled={currentPage === 1}
                  >
                    {t("page.pagination.previous")}
                  </Button>
                  <span className="flex items-center px-4">
                    {t("page.pagination.page_info", { current: currentPage, total: totalPages })}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => fetchBroadcasts(currentPage + 1, searchTerm)}
                    disabled={currentPage === totalPages}
                  >
                    {t("page.pagination.next")}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
