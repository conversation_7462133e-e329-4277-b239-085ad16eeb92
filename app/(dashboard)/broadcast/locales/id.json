{"page": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> dan kirim pesan siaran ke kontak Anda", "new_broadcast": "<PERSON><PERSON>", "search": {"title": "<PERSON><PERSON>", "description": "Te<PERSON><PERSON> siaran berdasarkan judul atau konten pesan", "placeholder": "<PERSON>i siaran...", "button_label": "<PERSON><PERSON>"}, "table": {"title": "<PERSON><PERSON> ({{count}})", "description": "<PERSON><PERSON><PERSON> ka<PERSON>", "headers": {"title": "<PERSON><PERSON><PERSON>", "status": "Status", "targets": "Target", "progress": "Progres", "success_rate": "Tingkat <PERSON>has<PERSON>", "created": "Dibuat", "actions": "<PERSON><PERSON><PERSON>"}, "progress": {"sent": "Terkirim: {{count}}", "failed": "Gagal: {{count}}", "pending": "Menunggu: {{count}}"}}, "states": {"loading": "<PERSON><PERSON><PERSON> siaran...", "no_broadcasts": "Tidak ada siaran di<PERSON>ukan", "create_first": "<PERSON>uat siaran pertama Anda"}, "pagination": {"previous": "Sebelumnya", "next": "Selanjutnya", "page_info": "Halaman {{current}} dari {{total}}"}, "actions": {"view_details": "<PERSON><PERSON>", "edit": "Edit", "start_broadcast": "<PERSON><PERSON>", "cancel_broadcast": "Batalkan <PERSON>", "delete": "Hapus"}, "confirmations": {"delete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus siaran ini?", "cancel": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membatalkan siaran ini?"}, "errors": {"fetch_failed": "<PERSON><PERSON> siaran", "delete_failed": "<PERSON><PERSON>", "start_failed": "<PERSON><PERSON> memulai siaran", "cancel_failed": "Gagal membatalkan siaran"}}, "status": {"DRAFT": "Draf", "SCHEDULED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SENDING": "Mengirim", "COMPLETED": "Se<PERSON><PERSON>", "FAILED": "Gaga<PERSON>", "CANCELLED": "Di<PERSON><PERSON><PERSON>"}, "form": {"new": {"title": "B<PERSON>t <PERSON>", "description": "<PERSON><PERSON> pesan ke beberapa kontak sekaligus", "submit": "<PERSON><PERSON><PERSON>"}, "edit": {"title": "<PERSON>", "description": "Perbarui detail siaran <PERSON>a", "submit": "<PERSON><PERSON><PERSON>"}, "tabs": {"information": "Informasi", "contacts": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "mis., Newsletter <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "character_count": "{{current}}/{{max}} karakter"}, "device": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>h perangkat untuk mengirim", "description": "<PERSON><PERSON><PERSON> perangkat yang akan digunakan untuk mengirim pesan siaran", "loading": "Memuat perangkat...", "no_devices": "Tidak ada perangkat aktif tersedia"}, "message": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> pesan siaran Anda di sini...", "character_count": "{{current}}/{{max}} karakter", "approaching_limit": "Mendekati batas"}}, "recipients": {"current_title": "<PERSON><PERSON><PERSON> ({{count}})", "select_title": "<PERSON><PERSON><PERSON> ({{count}})", "select_description": "<PERSON><PERSON><PERSON> kontak mana yang akan menerima siaran ini", "search_label": "<PERSON>i dan <PERSON>", "search_placeholder": "<PERSON>i kontak berda<PERSON>kan nama, telepon, atau email...", "searching": "<PERSON><PERSON><PERSON>...", "no_contacts": "Tidak ada kontak ditemukan", "table_headers": {"select": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "phone": "Telepon", "email": "Email"}, "select_contact_aria": "<PERSON><PERSON><PERSON> k<PERSON> {{name}}"}, "preview": {"title": "Pratinjau Mobile", "description": "Lihat bagaimana pesan Anda akan muncul di perangkat mobile", "sender_fallback": "<PERSON><PERSON><PERSON>"}, "buttons": {"cancel": "<PERSON><PERSON>", "saving": "Menyimpan...", "back": "Kembali"}, "validation": {"title_required": "<PERSON><PERSON><PERSON> masukkan judul", "message_required": "<PERSON><PERSON><PERSON> masukkan pesan", "device_required": "<PERSON><PERSON>an pilih perang<PERSON>", "contacts_required": "<PERSON><PERSON>an pilih setidaknya satu kontak"}, "mobile_chat": {"online": "online", "battery_percentage": "100%", "sample_message": "Hai! Saya ingin tahu lebih lanjut tentang layanan Anda.", "typing": "mengetik...", "type_message_placeholder": "<PERSON><PERSON><PERSON> pesan..."}}, "detail": {"title": "<PERSON><PERSON>", "loading": "<PERSON><PERSON><PERSON> siaran...", "not_found": "Siaran tidak ditemukan", "back_to_broadcasts": "Ke<PERSON><PERSON> ke Siaran", "refresh": "Segarkan", "edit": "Edit", "start_broadcast": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "delete": "Hapus", "sections": {"message": "<PERSON><PERSON>", "recipients": "<PERSON><PERSON><PERSON>", "status": "Status", "mobile_preview": "Pratinjau Mobile", "mobile_preview_description": "Bagaimana pesan ini muncul di perangkat mobile"}, "stats": {"total_targets": "Total Target:", "sent": "Terkirim:", "failed": "Gagal:", "pending": "Menunggu:", "progress": "Progres", "created": "Dibuat:", "started": "<PERSON><PERSON><PERSON>:", "completed": "Selesai:", "device": "Perangkat:"}, "recipients_table": {"title": "Penerima ({{count}})", "description": "Status detail untuk setiap penerima", "headers": {"name": "<PERSON><PERSON>", "contact": "Kontak", "status": "Status", "sent_at": "<PERSON><PERSON><PERSON>", "error": "Error"}, "no_recipients": "Tidak ada penerima di<PERSON>n", "unknown_contact": "Tidak Dikenal"}, "confirmations": {"cancel": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membatalkan siaran ini?", "delete": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus siaran ini?"}, "errors": {"fetch_data": "Gagal mengambil data siaran", "fetch_device": "Gagal mengambil per<PERSON>kat", "refresh_progress": "Gagal menyegarkan progres", "start_failed": "<PERSON><PERSON> memulai siaran", "cancel_failed": "Gagal membatalkan siaran", "delete_failed": "<PERSON><PERSON>"}}, "edit": {"loading": "<PERSON><PERSON><PERSON> siaran...", "not_found": "Siaran tidak ditemukan", "back_to_broadcasts": "Ke<PERSON><PERSON> ke Siaran", "cannot_edit": "<PERSON>aran ini tidak dapat diedit karena sudah dimulai.", "view_broadcast": "<PERSON><PERSON>", "errors": {"fetch_failed": "<PERSON><PERSON> siaran"}}}