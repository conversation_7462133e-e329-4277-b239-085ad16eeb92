{"contact": {"email": "<EMAIL>", "whatsapp": "+62 851-9005-2577", "whatsappLink": "https://wa.me/*************", "phone": "+62 851-9005-2577", "website": "https://cspintar.com", "address": "Jakarta, Indonesia"}, "businessHours": {"timezone": "Asia/Jakarta", "weekdays": {"start": "09:00", "end": "18:00"}, "weekends": {"start": "10:00", "end": "16:00"}}, "supportChannels": [{"id": "email", "name": "Email Support", "description": "Get detailed help via email", "icon": "Mail", "color": "blue", "responseTime": "24 hours", "availability": "24/7", "recommended": false, "action": {"type": "email", "target": "<EMAIL>"}}, {"id": "whatsapp", "name": "WhatsApp Support", "description": "Chat with us instantly", "icon": "MessageCircle", "color": "green", "responseTime": "5 minutes", "availability": "Business hours", "recommended": true, "action": {"type": "link", "target": "https://wa.me/*************"}}, {"id": "phone", "name": "Phone Support", "description": "Speak directly with our team", "icon": "Phone", "color": "purple", "responseTime": "Immediate", "availability": "Business hours", "recommended": false, "action": {"type": "phone", "target": "+62 851-9005-2577"}}, {"id": "documentation", "name": "Documentation", "description": "Browse our help articles", "icon": "BookOpen", "color": "orange", "responseTime": "Self-service", "availability": "24/7", "recommended": false, "action": {"type": "link", "target": "/docs"}}], "faq": [{"id": "getting-started", "question": "How do I get started with CS AI?", "answer": "Follow our quick setup guide: 1) Connect your WhatsApp device, 2) Build your knowledge base, 3) Start handling conversations with AI assistance.", "category": "Getting Started"}, {"id": "pricing", "question": "What are your pricing plans?", "answer": "We offer flexible pricing based on your needs. Contact our sales team for detailed pricing information.", "category": "Billing"}, {"id": "integrations", "question": "What integrations do you support?", "answer": "We support WhatsApp Business API, various CRM systems, and custom integrations via our API.", "category": "Integrations"}, {"id": "data-security", "question": "How secure is my data?", "answer": "We use enterprise-grade security with end-to-end encryption, SOC 2 compliance, and regular security audits.", "category": "Security"}], "quickActions": [{"id": "report-bug", "title": "Report a Bug", "description": "Found an issue? Let us know", "icon": "Bug", "action": {"type": "email", "target": "<EMAIL>", "subject": "Bug Report"}}, {"id": "feature-request", "title": "Request Feature", "description": "Suggest new features", "icon": "Lightbulb", "action": {"type": "email", "target": "<EMAIL>", "subject": "Feature Request"}}, {"id": "billing-help", "title": "Billing Help", "description": "Questions about your bill", "icon": "CreditCard", "action": {"type": "email", "target": "<EMAIL>", "subject": "Billing Inquiry"}}, {"id": "technical-support", "title": "Technical Support", "description": "Get technical assistance", "icon": "Settings", "action": {"type": "link", "target": "https://wa.me/*************"}}], "tips": ["Include your account email when contacting support", "Describe the issue with specific steps to reproduce", "Attach screenshots or error messages if possible", "Mention what you were trying to accomplish", "Check our documentation first for quick answers"], "emergencyContact": {"available": true, "description": "For critical issues affecting your business operations", "contact": {"whatsapp": "https://wa.me/*************", "email": "<EMAIL>"}}}