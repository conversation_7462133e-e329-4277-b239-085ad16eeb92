"use client"

import Link from "next/link"
import { useLocalization } from "@/localization/functions/client"
import { supportLocales } from "./locales"

const SUPPORT_CONTACT = {
  email: "<EMAIL>",
  whatsapp: "+62 851-9005-2577",
  whatsappLink: "https://wa.me/6285190052577",
}

export default function SupportPage() {
  const { t } = useLocalization("support", supportLocales)

  return (
    <div className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md">
      <h1 className="text-2xl font-semibold mb-4">{t("title")}</h1>
      <p className="text-gray-700 mb-6">{t("subtitle")}</p>

      {/* Email Support */}
      <div className="mb-6">
        <h2 className="text-lg font-medium text-gray-800 mb-1">
          {t("email.title")}
        </h2>
        <p className="text-sm text-gray-600 mb-1">{t("email.description")}</p>
        <p className="text-sm text-gray-700 font-mono mb-3">
          {t("email.label")}:{" "}
          <a
            href={`mailto:${SUPPORT_CONTACT.email}`}
            className="text-blue-600 underline"
          >
            {SUPPORT_CONTACT.email}
          </a>
        </p>
        <Link
          href={`mailto:${SUPPORT_CONTACT.email}`}
          className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm"
        >
          {t("email.button")}
        </Link>
      </div>

      {/* WhatsApp Support */}
      <div className="mb-6">
        <h2 className="text-lg font-medium text-gray-800 mb-1">
          {t("whatsapp.title")}
        </h2>
        <p className="text-sm text-gray-600 mb-1">
          {t("whatsapp.description")}
        </p>
        <p className="text-sm text-gray-700 font-mono mb-3">
          {t("whatsapp.label")}:{" "}
          <a
            href={SUPPORT_CONTACT.whatsappLink}
            target="_blank"
            className="text-blue-600 underline"
          >
            {SUPPORT_CONTACT.whatsapp}
          </a>
        </p>
        <Link
          href={SUPPORT_CONTACT.whatsappLink}
          target="_blank"
          className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm"
        >
          {t("whatsapp.button")}
        </Link>
      </div>

      {/* Tips */}
      <div className="mt-8">
        <h3 className="text-md font-semibold text-gray-800 mb-2">
          {t("tips.title")}
        </h3>
        <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
          {/* {t("tips.items", { returnObjects: true }).map((item: string, idx: number) => (
            <li key={idx}>{item}</li>
          ))} */}
        </ul>
      </div>
    </div>
  )
}
