"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { AuthAPI } from "@/lib/services/authApi"
import { useLocalization } from "@/localization/functions/client"
import { accountLocales } from "./locales"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function SettingsPage() {
  const { t } = useLocalization("settings-account", accountLocales)
  const router = useRouter()

  // State: Change password
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [changeError, setChangeError] = useState("")
  const [changeSuccess, setChangeSuccess] = useState("")

  // State: Delete account
  const [deletePassword, setDeletePassword] = useState("")
  const [deleteError, setDeleteError] = useState("")
  const [deleteSuccess, setDeleteSuccess] = useState("")

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setChangeError("")
    setChangeSuccess("")
    try {
      const res = await AuthAPI.ChangePassword({
        currentPassword,
        newPassword,
      }).request()
      if (res.success) {
        setChangeSuccess(t("auth.password_changed"))
        setCurrentPassword("")
        setNewPassword("")
      }
    } catch (err: any) {
      setChangeError(err?.message || t("auth.change_password_error"))
    }
  }

  const handleDeleteAccount = async (e: React.FormEvent) => {
    e.preventDefault()
    setDeleteError("")
    setDeleteSuccess("")
    try {
      const res = await AuthAPI.DeleteAccount({
        password: deletePassword,
      }).request()
      if (res.success) {
        setDeleteSuccess(t("auth.account_deleted"))
        localStorage.clear()
        router.push("/login")
      }
    } catch (err: any) {
      setDeleteError(err?.message || t("auth.delete_account_error"))
    }
  }

  return (
    <div className="max-w-2xl mx-auto py-12 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t("auth.change_password")}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleChangePassword} className="space-y-4">
            <Input
              type="password"
              placeholder={t("auth.old_password")}
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
            />
            <Input
              type="password"
              placeholder={t("auth.new_password")}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
            />
            <Button type="submit" className="w-full">
              {t("auth.update_password")}
            </Button>

            {changeSuccess && (
              <Alert variant="default">
                <AlertTitle>{t("auth.success")}</AlertTitle>
                <AlertDescription>{changeSuccess}</AlertDescription>
              </Alert>
            )}

            {changeError && (
              <Alert variant="destructive">
                <AlertTitle>{t("auth.error")}</AlertTitle>
                <AlertDescription>{changeError}</AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">
            {t("auth.delete_account")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleDeleteAccount} className="space-y-4">
            <Input
              type="password"
              placeholder={t("auth.password")}
              value={deletePassword}
              onChange={(e) => setDeletePassword(e.target.value)}
            />
            <Button type="submit" variant="destructive" className="w-full">
              {t("auth.confirm_delete")}
            </Button>

            {deleteSuccess && (
              <Alert variant="default">
                <AlertTitle>{t("auth.success")}</AlertTitle>
                <AlertDescription>{deleteSuccess}</AlertDescription>
              </Alert>
            )}

            {deleteError && (
              <Alert variant="destructive">
                <AlertTitle>{t("auth.error")}</AlertTitle>
                <AlertDescription>{deleteError}</AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
