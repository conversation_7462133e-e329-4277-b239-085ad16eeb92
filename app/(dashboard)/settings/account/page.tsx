"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import { useLocalization } from "@/localization/functions/client"
import { AccountAPI } from "@/lib/services/accountApi"

export default function AccountPage() {
  const { t } = useLocalization("account", {}) // load keys under "account"


  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [loadingAccount, setLoadingAccount] = useState(true)
  const [success, setSuccess] = useState("")
  const [error, setError] = useState("")

  // Load account data on component mount
  useEffect(() => {
    const loadAccount = async () => {
      try {
        setLoadingAccount(true)
        const accountData = await AccountAPI.GetAccount().request()
        setName(accountData.name)
        setEmail(accountData.email)
      } catch (err: any) {
        console.error("Error loading account:", err)
        setError("Failed to load account information")
      } finally {
        setLoadingAccount(false)
      }
    }

    loadAccount()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")
    setLoading(true)

    try {
      const res = await AccountAPI.UpdateAccountName(name).request()
      if (res.success) {
        setSuccess(t("success_message"))
      } else {
        setError(t("error_message"))
      }
    } catch (err: any) {
      setError(err?.message || t("error_message"))
    } finally {
      setLoading(false)
    }
  }

  if (loadingAccount) {
    return (
      <div className="max-w-md mx-auto py-12">
        <Card>
          <CardHeader>
            <CardTitle>Loading...</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-4">Loading account information...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto py-12">
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                disabled
                className="bg-gray-50"
              />
              <p className="text-sm text-gray-500">Email cannot be changed</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">{t("name_label")}</Label>
              <Input
                id="name"
                type="text"
                placeholder={t("name_placeholder")}
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                disabled={loading}
              />
            </div>

            <Button type="submit" disabled={loading} className="w-full">
              {loading ? t("saving") : t("save_button")}
            </Button>

            {success && (
              <Alert variant="default" className="mt-4">
                <AlertTitle>{t("success_title")}</AlertTitle>
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertTitle>{t("error_title")}</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
