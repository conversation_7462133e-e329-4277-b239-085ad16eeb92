"use client"

import { useEffect, useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useLocalization } from "@/localization/functions/client"
import { CollaborationAPI, Collaborator } from "@/lib/services/collaborationApi"
import Link from "next/link"

export default function CollaboratorsPage() {
  const { t } = useLocalization("collaborators", {})

  const [collaborators, setCollaborators] = useState<Collaborator[]>([])
  const [inviteEmail, setInviteEmail] = useState("")
  const [inviteError, setInviteError] = useState("")
  const [inviteSuccess, setInviteSuccess] = useState("")
  const [loading, setLoading] = useState(false)

  const fetchCollaborators = async () => {
    try {
      const res = await CollaborationAPI.All().request()
      setCollaborators(res.items || [])
    } catch (err) {
      console.error("Failed to fetch collaborators:", err)
    }
  }

  useEffect(() => {
    fetchCollaborators()
  }, [])

  const handleInvite = async (e: React.FormEvent) => {
    e.preventDefault()
    setInviteError("")
    setInviteSuccess("")
    setLoading(true)
    try {
      const res = await CollaborationAPI.InviteCollaborator({
        email: inviteEmail,
      }).request()
      if (res.success) {
        setInviteSuccess(t("invite.success"))
        setInviteEmail("")
        await fetchCollaborators()
      } else {
        setInviteError(t("invite.error"))
      }
    } catch (err: any) {
      setInviteError(err?.message || t("invite.error"))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto py-12 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t("collaborators.title")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <ul className="space-y-2">
            {collaborators.length === 0 ? (
              <li>{t("collaborators.none")}</li>
            ) : (
              collaborators.map((collab) => (
                <li
                  key={collab.id}
                  className="border p-2 rounded-md hover:bg-gray-100 cursor-pointer"
                >
                  <Link
                    href={`/settings/collaborators/${collab.id}`}
                    className="block w-full"
                  >
                    <strong>{collab.email}</strong> – {collab.role} (
                    {collab.status})
                  </Link>
                </li>
              ))
            )}
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t("invite.title")}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleInvite} className="space-y-4">
            <Input
              type="email"
              placeholder={t("invite.email_placeholder")}
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              required
              disabled={loading}
            />
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? t("invite.sending") : t("invite.send_invite")}
            </Button>

            {inviteSuccess && (
              <Alert variant="default" className="mt-4">
                <AlertTitle>{t("invite.success_title")}</AlertTitle>
                <AlertDescription>{inviteSuccess}</AlertDescription>
              </Alert>
            )}

            {inviteError && (
              <Alert variant="destructive" className="mt-4">
                <AlertTitle>{t("invite.error_title")}</AlertTitle>
                <AlertDescription>{inviteError}</AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
