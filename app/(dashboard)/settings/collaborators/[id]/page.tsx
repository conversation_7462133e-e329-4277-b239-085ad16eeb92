"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchPara<PERSON> } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  CollaborationAPI,
  Collaborator,
  UpdateRolePayload,
} from "@/lib/services/collaborationApi"
import { useLocalization } from "@/localization/functions/client"

const locales = {
  error_no_id: "No collaborator ID provided.",
  error_fetch: "Failed to fetch collaborator details.",
  error_update: "Failed to update collaborator role.",
  error_remove: "Failed to remove collaborator.",
  error_title: "Error",
  loading: "Loading collaborator details...",
  no_data: "No collaborator data found.",
  detail_title: "Collaborator Details",
  email: "Email",
  status: "Status",
  invited_at: "Invited At",
  accepted_at: "Accepted At",
  role: "Role",
  update_role: "Update Role",
  remove_collaborator: "Remove Collaborator",
  success_title: "Success",
  success_update: "Role updated successfully.",
}

export default function CollaboratorDetail() {
  const { t } = useLocalization("collaboratorDetail", { en: locales })
  const router = useRouter()

  const [collaborator, setCollaborator] = useState<Collaborator | null>(null)
  const [role, setRole] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [successMsg, setSuccessMsg] = useState("")

  const collaboratorId = "1"

  useEffect(() => {
    async function fetchCollaborator() {
      setLoading(true)
      setError("")
      try {
        const res =
          await CollaborationAPI.GetCollaboratorById(collaboratorId).request()
        setCollaborator(res)
        setRole(res.role)
      } catch (err: any) {
        setError(err?.message || t("error_fetch"))
      } finally {
        setLoading(false)
      }
    }

    fetchCollaborator()
  }, [collaboratorId])

  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRole(e.target.value)
  }

  const handleUpdateRole = async () => {
    if (!collaboratorId) return
    setLoading(true)
    setError("")
    setSuccessMsg("")
    try {
      const payload: UpdateRolePayload = {
        collaboratorId,
        role,
      }
      const res =
        await CollaborationAPI.UpdateCollaboratorRole(payload).request()
      if (res.success) {
        setSuccessMsg(t("success_update"))
      } else {
        setError(t("error_update"))
      }
    } catch (err: any) {
      setError(err?.message || t("error_update"))
    } finally {
      setLoading(false)
    }
  }

  const handleRemove = async () => {
    if (!collaboratorId) return
    setLoading(true)
    setError("")
    try {
      const res =
        await CollaborationAPI.RemoveCollaborator(collaboratorId).request()
      if (res.success) {
        router.push("/collaborators")
      } else {
        setError(t("error_remove"))
      }
    } catch (err: any) {
      setError(err?.message || t("error_remove"))
    } finally {
      setLoading(false)
    }
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>{t("error_title")}</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (loading && !collaborator) {
    return <p>{t("loading")}</p>
  }

  if (!collaborator) {
    return <p>{t("no_data")}</p>
  }

  return (
    <div className="max-w-lg mx-auto mt-24 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{t("detail_title")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            <strong>{t("email")}:</strong> {collaborator.email}
          </p>
          <p>
            <strong>{t("status")}:</strong> {collaborator.status}
          </p>
          <p>
            <strong>{t("invited_at")}:</strong>{" "}
            {collaborator.invitedAt
              ? new Date(collaborator.invitedAt).toLocaleString()
              : "-"}
          </p>
          <p>
            <strong>{t("accepted_at")}:</strong>{" "}
            {collaborator.acceptedAt
              ? new Date(collaborator.acceptedAt).toLocaleString()
              : "-"}
          </p>

          <div>
            <label htmlFor="role" className="block font-semibold mb-1">
              {t("role")}
            </label>
            <Input
              id="role"
              value={role}
              onChange={handleRoleChange}
              disabled={loading}
            />
          </div>

          <div className="flex gap-4">
            <Button onClick={handleUpdateRole} disabled={loading}>
              {t("update_role")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemove}
              disabled={loading}
            >
              {t("remove_collaborator")}
            </Button>
          </div>

          {successMsg && (
            <Alert variant="default" className="mt-4">
              <AlertTitle>{t("success_title")}</AlertTitle>
              <AlertDescription>{successMsg}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
