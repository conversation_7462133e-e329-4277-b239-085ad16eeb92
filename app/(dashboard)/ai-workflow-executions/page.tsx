"use client"

import { useState, useEffect, useCallback } from "react"
import {
  AiWorkflowExecution,
  AiWorkflowStep,
} from "@/lib/repositories/aiWorkflowExecutions"
import { AiWorkflowExecutionsAPI } from "@/lib/services/workflowExecution"
import { WorkflowExecutions } from "@/components/workflow/workflow-executions"
import { realtime } from "@/lib/realtime"
import { useLocalization } from "@/localization/functions/client"
import { aiWorkflowExecutionsLocales } from "./locales"

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay)
    return () => clearTimeout(handler)
  }, [value, delay])

  return debouncedValue
}

export default function Page() {
  const { t } = useLocalization(
    "aiWorkflowExecutions",
    aiWorkflowExecutionsLocales,
  )
  const [executions, setExecutions] = useState<AiWorkflowExecution[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const [notification, setNotification] = useState<string | null>(null)

  const debouncedSearchTerm = useDebounce(searchTerm, 500)

  const fetchExecutions = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const filters =
        statusFilter !== "all"
          ? [{ field: "finalStatus", value: statusFilter }]
          : []

      const response = await AiWorkflowExecutionsAPI.All({
        page: 1,
        per_page: 100,
        sort: [],
        filters,
        search: debouncedSearchTerm || undefined,
      }).request()

      setExecutions(response.items)
    } catch (err: any) {
      setError(err.message || "Failed to load workflow executions")
    } finally {
      setLoading(false)
    }
  }, [debouncedSearchTerm, statusFilter])

  const showNotification = useCallback((message: string) => {
    setNotification(message)
    setTimeout(() => {
      setNotification(null)
    }, 3000)
  }, [])

  const handleNewWorkflowStep = useCallback(
    (workflowId: string, newStep: AiWorkflowStep): void => {
      setExecutions((prev) => {
        const idx = prev.findIndex((e) => e.id === workflowId)
        if (idx === -1) return prev

        const updated = [...prev]
        const executionToUpdate = { ...updated[idx] }
        executionToUpdate.steps = [...executionToUpdate.steps, newStep]
        updated[idx] = executionToUpdate

        return updated
      })
      showNotification(t("notif_step_added", { workflowId }))
    },
    [showNotification],
  )

  const handleNewWorkflowExecution = useCallback(
    (workflowData: AiWorkflowExecution): void => {
      setExecutions((prev) => [workflowData, ...prev])
      showNotification(t("notif_execution_added", { id: workflowData.id }))
    },
    [showNotification],
  )

  useEffect(() => {
    const channel = realtime.client.subscribe("workflow-channel")

    channel.bind(
      "new-workflow-step",
      (updatedData: { workflowId: string; newStep: AiWorkflowStep }) => {
        handleNewWorkflowStep(updatedData.workflowId, updatedData.newStep)
      },
    )

    channel.bind("new-workflow", (workflowData: AiWorkflowExecution) => {
      handleNewWorkflowExecution(workflowData)
    })

    return () => {
      channel.unbind_all()
      channel.unsubscribe()
    }
  }, [handleNewWorkflowStep, handleNewWorkflowExecution])

  useEffect(() => {
    fetchExecutions()
  }, [fetchExecutions])

  if (loading) {
    return <div className="p-8 text-center text-gray-500">{t("loading")}</div>
  }

  if (error) {
    return (
      <div className="p-8 text-center text-red-500">
        {t("error_prefix")}
        {error}
      </div>
    )
  }

  return (
    <div className="min-h-full bg-gray-50 relative overflow-y-auto">
      {/* Notification Banner */}
      {notification && (
        <div className="absolute top-0 left-0 right-0 bg-blue-500 text-white text-center py-2 z-50 w-full">
          {notification}
        </div>
      )}

      <div className="container px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">{t("title")}</h1>
          <p className="text-gray-600 mt-2">{t("subtitle")}</p>
        </div>

        <WorkflowExecutions
          executions={executions}
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          onSearchTermChange={setSearchTerm}
          onStatusFilterChange={setStatusFilter}
        />
      </div>
    </div>
  )
}
