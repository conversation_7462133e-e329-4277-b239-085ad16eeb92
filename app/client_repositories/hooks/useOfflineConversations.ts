"use client"

import { useState, useEffect, useCallback } from "react"
import { offlineRepoManager } from "../OfflineRepositoryManager"
import { OfflineConversation } from "../ConversationOfflineRepository"
import { OfflineConversationMessage } from "../ConversationMessageOfflineRepository"

export interface UseOfflineConversationsResult {
  conversations: OfflineConversation[]
  loading: boolean
  error: string | null
  syncStatus: any

  // Actions
  createConversation: (
    conversationData: Omit<
      OfflineConversation,
      "_id" | "_rev" | "id" | "createdAt" | "updatedAt"
    >,
  ) => Promise<OfflineConversation>
  updateConversation: (
    id: string,
    data: Partial<OfflineConversation>,
  ) => Promise<OfflineConversation | null>
  deleteConversation: (id: string, hardDelete?: boolean) => Promise<boolean>
  searchConversations: (query: string) => Promise<OfflineConversation[]>
  syncConversations: () => Promise<void>
  refreshConversations: () => Promise<void>
}

export function useOfflineConversations(
  participantId?: string,
): UseOfflineConversationsResult {
  const [conversations, setConversations] = useState<OfflineConversation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [syncStatus, setSyncStatus] = useState(
    offlineRepoManager.getSyncStatus(),
  )

  const loadConversations = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      let loadedConversations: OfflineConversation[]

      if (participantId) {
        loadedConversations =
          await offlineRepoManager.conversations.getConversationsByParticipant(
            participantId,
          )
      } else {
        const result =
          await offlineRepoManager.conversations.getRecentConversations(100)
        loadedConversations = result
      }

      setConversations(loadedConversations)
    } catch (err: any) {
      setError(err.message || "Failed to load conversations")
      console.error("Error loading conversations:", err)
    } finally {
      setLoading(false)
    }
  }, [participantId])

  const createConversation = useCallback(
    async (
      conversationData: Omit<
        OfflineConversation,
        "_id" | "_rev" | "id" | "createdAt" | "updatedAt"
      >,
    ): Promise<OfflineConversation> => {
      try {
        const newConversation =
          await offlineRepoManager.conversations.create(conversationData)
        await loadConversations() // Refresh the list
        return newConversation
      } catch (err: any) {
        setError(err.message || "Failed to create conversation")
        throw err
      }
    },
    [loadConversations],
  )

  const updateConversation = useCallback(
    async (
      id: string,
      data: Partial<OfflineConversation>,
    ): Promise<OfflineConversation | null> => {
      try {
        const updatedConversation =
          await offlineRepoManager.conversations.update(id, data)
        if (updatedConversation) {
          await loadConversations() // Refresh the list
        }
        return updatedConversation
      } catch (err: any) {
        setError(err.message || "Failed to update conversation")
        throw err
      }
    },
    [loadConversations],
  )

  const deleteConversation = useCallback(
    async (id: string, hardDelete = false): Promise<boolean> => {
      try {
        const success = await offlineRepoManager.conversations.delete(
          id,
          hardDelete,
        )
        if (success) {
          await loadConversations() // Refresh the list
        }
        return success
      } catch (err: any) {
        setError(err.message || "Failed to delete conversation")
        throw err
      }
    },
    [loadConversations],
  )

  const searchConversations = useCallback(
    async (query: string): Promise<OfflineConversation[]> => {
      try {
        return await offlineRepoManager.conversations.searchConversations(query)
      } catch (err: any) {
        setError(err.message || "Failed to search conversations")
        throw err
      }
    },
    [],
  )

  const syncConversations = useCallback(async (): Promise<void> => {
    try {
      await offlineRepoManager.syncAll()
      setSyncStatus(offlineRepoManager.getSyncStatus())
      await loadConversations() // Refresh after sync
    } catch (err: any) {
      setError(err.message || "Failed to sync conversations")
      throw err
    }
  }, [loadConversations])

  const refreshConversations = useCallback(async (): Promise<void> => {
    await loadConversations()
  }, [loadConversations])

  // Load conversations on mount and when participantId changes
  useEffect(() => {
    loadConversations()
  }, [loadConversations])

  // Update sync status periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setSyncStatus(offlineRepoManager.getSyncStatus())
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  return {
    conversations,
    loading,
    error,
    syncStatus,
    createConversation,
    updateConversation,
    deleteConversation,
    searchConversations,
    syncConversations,
    refreshConversations,
  }
}

export interface UseOfflineConversationResult {
  conversation: OfflineConversation | null
  messages: OfflineConversationMessage[]
  loading: boolean
  error: string | null

  // Actions
  sendMessage: (
    content: string,
    senderId: string,
    recipientId: string,
  ) => Promise<OfflineConversationMessage>
  markAsRead: (userId: string) => Promise<number>
  loadMoreMessages: (limit?: number) => Promise<void>
  searchMessages: (query: string) => Promise<OfflineConversationMessage[]>
  refreshConversation: () => Promise<void>
}

export function useOfflineConversation(
  conversationId: string,
): UseOfflineConversationResult {
  const [conversation, setConversation] = useState<OfflineConversation | null>(
    null,
  )
  const [messages, setMessages] = useState<OfflineConversationMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [messageOffset, setMessageOffset] = useState(0)

  const loadConversationData = useCallback(
    async (reset = false) => {
      try {
        setLoading(true)
        setError(null)

        const offset = reset ? 0 : messageOffset
        const { conversation: loadedConversation, messages: loadedMessages } =
          await offlineRepoManager.getConversationWithMessages(
            conversationId,
            50, // Load 50 messages at a time
          )

        setConversation(loadedConversation)

        if (reset) {
          setMessages(loadedMessages)
          setMessageOffset(loadedMessages.length)
        } else {
          setMessages((prev) => [...prev, ...loadedMessages])
          setMessageOffset((prev) => prev + loadedMessages.length)
        }
      } catch (err: any) {
        setError(err.message || "Failed to load conversation")
        console.error("Error loading conversation:", err)
      } finally {
        setLoading(false)
      }
    },
    [conversationId, messageOffset],
  )

  const sendMessage = useCallback(
    async (
      content: string,
      senderId: string,
      recipientId: string,
    ): Promise<OfflineConversationMessage> => {
      try {
        const { message } = await offlineRepoManager.sendMessageToConversation(
          conversationId,
          content,
          senderId,
          recipientId,
        )

        // Add message to local state immediately (optimistic update)
        setMessages((prev) => [message, ...prev])

        return message
      } catch (err: any) {
        setError(err.message || "Failed to send message")
        throw err
      }
    },
    [conversationId],
  )

  const markAsRead = useCallback(
    async (userId: string): Promise<number> => {
      try {
        const count = await offlineRepoManager.markConversationAsRead(
          conversationId,
          userId,
        )

        // Update local state
        setMessages((prev) =>
          prev.map((msg) =>
            msg.recipientId === userId ? { ...msg, isRead: true } : msg,
          ),
        )

        return count
      } catch (err: any) {
        setError(err.message || "Failed to mark as read")
        throw err
      }
    },
    [conversationId],
  )

  const loadMoreMessages = useCallback(
    async (limit = 50): Promise<void> => {
      try {
        const moreMessages =
          await offlineRepoManager.messages.getMessagesByConversationId(
            conversationId,
            limit,
            messageOffset,
          )

        setMessages((prev) => [...prev, ...moreMessages])
        setMessageOffset((prev) => prev + moreMessages.length)
      } catch (err: any) {
        setError(err.message || "Failed to load more messages")
        throw err
      }
    },
    [conversationId, messageOffset],
  )

  const searchMessages = useCallback(
    async (query: string): Promise<OfflineConversationMessage[]> => {
      try {
        return await offlineRepoManager.messages.searchMessages(
          conversationId,
          query,
        )
      } catch (err: any) {
        setError(err.message || "Failed to search messages")
        throw err
      }
    },
    [conversationId],
  )

  const refreshConversation = useCallback(async (): Promise<void> => {
    setMessageOffset(0)
    await loadConversationData(true)
  }, [loadConversationData])

  // Load conversation data on mount and when conversationId changes
  useEffect(() => {
    if (conversationId) {
      setMessageOffset(0)
      loadConversationData(true)
    }
  }, [conversationId])

  return {
    conversation,
    messages,
    loading,
    error,
    sendMessage,
    markAsRead,
    loadMoreMessages,
    searchMessages,
    refreshConversation,
  }
}
