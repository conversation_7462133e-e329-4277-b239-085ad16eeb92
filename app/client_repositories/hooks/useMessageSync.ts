"use client"

import { useCallback } from "react"
import { offlineRepoManager } from "../OfflineRepositoryManager"
import { ConversationMessage } from "@/lib/repositories/conversationMessages/interface"

export interface MessageSyncHook {
  // Store messages in PouchDB
  storeWAHAMessage: (wahaData: any) => Promise<void>
  storeSentMessage: (
    conversationId: string,
    content: string,
    senderId: string,
    recipientId: string,
    metadata?: any,
  ) => Promise<void>
  storeAIResponse: (
    conversationId: string,
    content: string,
    recipientId: string,
    metadata?: any,
  ) => Promise<void>

  // Sync with server
  syncMessages: () => Promise<void>

  // Utility
  generateConversationId: (session: string, participant: string) => string
}

export function useMessageSync(): MessageSyncHook {
  const storeWAHAMessage = useCallback(async (wahaData: any) => {
    try {
      const conversationId = ConversationrateConversationId(
        wahaData.session,
        wahaData.payload.from,
      )
      const payload = wahaData.payload

      // Store in offline repository
      await offlineRepoManager.sendMessageToConversation(
        conversationId,
        payload.body,
        payload.fromMe ? wahaData.me.id : payload.from,
        payload.fromMe ? payload.from : wahaData.me.id,
      )

      console.log(`✅ Stored WAHA message in PouchDB: ${conversationId}`)
    } catch (error) {
      console.error("❌ Failed to store WAHA message in PouchDB:", error)
    }
  }, [])

  const storeSentMessage = useCallback(
    async (
      conversationId: string,
      content: string,
      senderId: string,
      recipientId: string,
      metadata?: any,
    ) => {
      try {
        await offlineRepoManager.sendMessageToConversation(
          conversationId,
          content,
          senderId,
          recipientId,
        )
        console.log(`✅ Stored sent message in PouchDB: ${conversationId}`)
      } catch (error) {
        console.error("❌ Failed to store sent message in PouchDB:", error)
      }
    },
    [],
  )

  const storeAIResponse = useCallback(
    async (
      conversationId: string,
      content: string,
      recipientId: string,
      metadata?: any,
    ) => {
      try {
        await offlineRepoManager.sendMessageToConversation(
          conversationId,
          content,
          "ai-assistant",
          recipientId,
        )
        console.log(`✅ Stored AI response in PouchDB: ${conversationId}`)
      } catch (error) {
        console.error("❌ Failed to store AI response in PouchDB:", error)
      }
    },
    [],
  )

  const syncMessages = useCallback(async () => {
    try {
      await offlineRepoManager.syncAll()
      console.log("✅ Messages synced with server")
    } catch (error) {
      console.error("❌ Failed to sync messages:", error)
    }
  }, [])

  const generateConversationId = useCallback(
    (session: string, participant: string): string => {
      // Use the same logic as the server
      const crypto = require("crypto")
      return (
        "room-" +
        crypto
          .createHash("sha256")
          .update(participant + ":" + session)
          .digest("hex")
      )
    },
    [],
  )

  return {
    storeWAHAMessage,
    storeSentMessage,
    storeAIResponse,
    syncMessages,
    generateConversationId,
  }
}

// Hook for real-time message listening (to be used in components)
export function useMessageListener(conversationId?: string) {
  const { storeWAHAMessage, storeSentMessage, storeAIResponse } =
    useMessageSync()

  // Listen for real-time messages (you can integrate with your realtime system)
  const handleRealtimeMessage = useCallback(
    async (messageData: any) => {
      if (!conversationId) return

      try {
        switch (messageData.type) {
          case "waha_webhook":
            await storeWAHAMessage(messageData.data)
            break
          case "sent_message":
            await storeSentMessage(
              conversationId,
              messageData.content,
              messageData.senderId,
              messageData.recipientId,
              messageData.metadata,
            )
            break
          case "ai_response":
            await storeAIResponse(
              conversationId,
              messageData.content,
              messageData.recipientId,
              messageData.metadata,
            )
            break
        }
      } catch (error) {
        console.error("Failed to handle realtime message:", error)
      }
    },
    [conversationId, storeWAHAMessage, storeSentMessage, storeAIResponse],
  )

  return {
    handleRealtimeMessage,
  }
}
