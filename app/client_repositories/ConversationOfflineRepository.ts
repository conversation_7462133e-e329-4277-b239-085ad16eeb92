import { BasePouchRepository } from "./BasePouchRepository"
import { OfflineEntity } from "./types"
import { Conversation } from "@/lib/repositories/conversations/interface"

// Extend Conversation interface for offline storage
export interface OfflineConversation extends Conversation, OfflineEntity {}

export class ConversationOfflineRepository extends BasePouchRepository<OfflineConversation> {
  constructor() {
    super("conversations", process.env.NEXT_PUBLIC_API_URL)
  }

  protected async setupIndexes(): Promise<void> {
    try {
      // Create indexes for common queries
      await this.db.createIndex({
        index: {
          fields: ["createdAt"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["updatedAt"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["name"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["participants"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["customerProfileId"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["_needsSync"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["deletedAt"],
        },
      })
    } catch (error) {
      console.error("Error setting up conversation indexes:", error)
    }
  }

  protected getApiEndpoint(): string {
    return "/api/v1/conversations"
  }

  protected mapToApiFormat(doc: OfflineConversation): any {
    // Remove PouchDB specific fields when sending to API
    const { _id, _rev, _syncStatus, _lastSyncAt, _needsSync, ...apiData } = doc
    return apiData
  }

  protected mapFromApiFormat(
    apiData: any,
  ): Omit<OfflineConversation, "_id" | "_rev"> {
    // Map API response to local format
    return {
      id: apiData.id,
      name: apiData.name,
      description: apiData.description,
      participants: apiData.participants || [],
      lastMessage: apiData.lastMessage,
      lastMessageAt: apiData.lastMessageAt
        ? new Date(apiData.lastMessageAt)
        : undefined,
      tags: apiData.tags || [],
      customerProfileId: apiData.customerProfileId,
      isActive: apiData.isActive !== false,
      createdAt: apiData.createdAt,
      updatedAt: apiData.updatedAt,
      deletedAt: apiData.deletedAt,
      createdBy: apiData.createdBy,
      updatedBy: apiData.updatedBy,
      organizationId: apiData.organizationId,
    }
  }

  // Conversation-specific methods
  async getConversationsByParticipant(
    participantId: string,
  ): Promise<OfflineConversation[]> {
    const result = await this.db.find({
      selector: {
        participants: {
          $elemMatch: { $eq: participantId },
        },
        deletedAt: { $exists: false },
      },
      sort: [{ updatedAt: "DESC" }],
    })
    return result.docs as OfflineConversation[]
  }

  async getConversationsByCustomerProfile(
    customerProfileId: string,
  ): Promise<OfflineConversation[]> {
    const result = await this.db.find({
      selector: {
        customerProfileId,
        deletedAt: { $exists: false },
      },
      sort: [{ updatedAt: "DESC" }],
    })
    return result.docs as OfflineConversation[]
  }

  async searchConversations(query: string): Promise<OfflineConversation[]> {
    const result = await this.db.find({
      selector: {
        $or: [
          { name: { $regex: new RegExp(query, "i") } },
          { description: { $regex: new RegExp(query, "i") } },
        ],
        deletedAt: { $exists: false },
      },
      sort: [{ updatedAt: "DESC" }],
    })
    return result.docs as OfflineConversation[]
  }

  async updateLastMessage(
    conversationId: string,
    lastMessage: any,
    lastMessageAt: Date,
  ): Promise<OfflineConversation | null> {
    return this.update(conversationId, {
      lastMessage,
      lastMessageAt: lastMessageAt,
    } as Partial<OfflineConversation>)
  }

  async getRecentConversations(limit = 20): Promise<OfflineConversation[]> {
    const result = await this.db.find({
      selector: {
        deletedAt: { $exists: false },
      },
      sort: [{ updatedAt: "DESC" }],
      limit,
    })
    return result.docs as OfflineConversation[]
  }

  async getConversationCount(): Promise<number> {
    const result = await this.db.find({
      selector: {
        deletedAt: { $exists: false },
      },
      fields: ["_id"],
    })
    return result.docs.length
  }

  // Sync with server and handle conflicts
  async syncWithConflictResolution(): Promise<void> {
    try {
      const syncResult = await this.sync()

      if (syncResult.conflicts && syncResult.conflicts.length > 0) {
        console.log(
          "Resolving conversation conflicts:",
          syncResult.conflicts.length,
        )

        for (const conflict of syncResult.conflicts) {
          // Simple conflict resolution: server wins
          await this.resolveConflict(conflict, "server-wins")
        }
      }
    } catch (error) {
      console.error("Error syncing conversations:", error)
    }
  }

  private async resolveConflict(
    conflict: any,
    strategy: "server-wins" | "client-wins" | "merge",
  ): Promise<void> {
    switch (strategy) {
      case "server-wins":
        // Accept server version
        await this.db.put(conflict.server)
        break
      case "client-wins":
        // Keep client version, mark for sync
        await this.markForSync(conflict.client._id)
        break
      case "merge":
        // Merge both versions (implement custom logic)
        const merged = this.mergeConflictedDocs(
          conflict.client,
          conflict.server,
        )
        await this.db.put(merged)
        break
    }
  }

  private mergeConflictedDocs(
    client: OfflineConversation,
    server: OfflineConversation,
  ): OfflineConversation {
    // Simple merge strategy: use latest updatedAt
    const clientTime = new Date(client.updatedAt).getTime()
    const serverTime = new Date(server.updatedAt).getTime()

    return clientTime > serverTime ? client : server
  }
}
