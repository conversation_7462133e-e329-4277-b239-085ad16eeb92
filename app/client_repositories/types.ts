// Base types for offline repositories
export interface OfflineEntity {
  _id: string
  _rev?: string
  id: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  // Sync metadata
  _syncStatus?: "pending" | "synced" | "conflict" | "error"
  _lastSyncAt?: Date
  _needsSync?: boolean
}

export interface SyncResult {
  success: boolean
  conflicts?: any[]
  errors?: any[]
  docs_read?: number
  docs_written?: number
  doc_write_failures?: number
  start_time?: Date
  end_time?: Date
}

export interface OfflineQueryParams {
  limit?: number
  skip?: number
  sort?: { [key: string]: "asc" | "DESC" }[]
  selector?: any
  fields?: string[]
  includeDeleted?: boolean
}

export interface OfflineRepository<T extends OfflineEntity> {
  // Basic CRUD
  getById(id: string): Promise<T | null>
  getAll(params: OfflineQueryParams): Promise<{ items: T[]; total: number }>
  create(
    data: Omit<T, "_id" | "_rev" | "id" | "createdAt" | "updatedAt">,
  ): Promise<T>
  update(id: string, data: Partial<T>): Promise<T | null>
  delete(id: string, hardDelete?: boolean): Promise<boolean>

  // Offline-specific methods
  sync(): Promise<SyncResult>
  getUnsyncedItems(): Promise<T[]>
  markForSync(id: string): Promise<void>
  clearCache(): Promise<void>

  // Bulk operations
  bulkCreate(
    items: Omit<T, "_id" | "_rev" | "id" | "createdAt" | "updatedAt">[],
  ): Promise<T[]>
  bulkUpdate(updates: { id: string; data: Partial<T> }[]): Promise<number>
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>
}
