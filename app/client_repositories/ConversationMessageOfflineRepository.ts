import { BasePouchRepository } from "./BasePouchRepository"
import { OfflineEntity } from "./types"
import { ConversationMessage } from "@/lib/repositories/conversationMessages/interface"

// Extend ConversationMessage interface for offline storage
export interface OfflineConversationMessage
  extends ConversationMessage,
    OfflineEntity {}

export class ConversationMessageOfflineRepository extends BasePouchRepository<OfflineConversationMessage> {
  constructor() {
    super("conversation-messages", process.env.NEXT_PUBLIC_API_URL)
  }

  protected async setupIndexes(): Promise<void> {
    try {
      // Create indexes for common queries
      await this.db.createIndex({
        index: {
          fields: ["conversationId", "createdAt"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["senderId"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["recipientId"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["messageType"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["isRead"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["_needsSync"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["deletedAt"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["createdAt"],
        },
      })

      await this.db.createIndex({
        index: {
          fields: ["updatedAt"],
        },
      })
    } catch (error) {
      console.error("Error setting up conversation message indexes:", error)
    }
  }

  protected getApiEndpoint(): string {
    return "/api/v1/conversation-messages"
  }

  protected mapToApiFormat(doc: OfflineConversationMessage): any {
    // Remove PouchDB specific fields when sending to API
    const { _id, _rev, _syncStatus, _lastSyncAt, _needsSync, ...apiData } = doc
    return apiData
  }

  protected mapFromApiFormat(
    apiData: any,
  ): Omit<OfflineConversationMessage, "_id" | "_rev"> {
    // Map API response to local format
    return {
      id: apiData.id,
      conversationId: apiData.conversationId,
      content: apiData.content,
      messageType: apiData.messageType,
      sender: apiData.senderId,
      recipient: apiData.recipientId,
      metadata: apiData.metadata,
      isRead: apiData.isRead || false,
      isActive: apiData.isActive !== false,
      createdAt: apiData.createdAt,
      updatedAt: apiData.updatedAt,
      deletedAt: apiData.deletedAt,
      createdBy: apiData.createdBy,
      updatedBy: apiData.updatedBy,
      organizationId: apiData.organizationId,
    }
  }

  // Conversation message specific methods
  async getMessagesByConversationId(
    conversationId: string,
    limit = 50,
    skip = 0,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        conversationId,
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
      limit,
      skip,
    })
    return result.docs as OfflineConversationMessage[]
  }

  async getMessagesBySender(
    senderId: string,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        senderId,
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
    })
    return result.docs as OfflineConversationMessage[]
  }

  async getUnreadMessages(
    recipientId: string,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        recipientId,
        isRead: false,
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
    })
    return result.docs as OfflineConversationMessage[]
  }

  async markAsRead(
    messageId: string,
  ): Promise<OfflineConversationMessage | null> {
    return this.update(messageId, {
      isRead: true,
      updatedAt: new Date(),
    } as Partial<OfflineConversationMessage>)
  }

  async markConversationMessagesAsRead(
    conversationId: string,
    recipientId: string,
  ): Promise<number> {
    const unreadMessages = await this.db.find({
      selector: {
        conversationId,
        recipientId,
        isRead: false,
        deletedAt: { $exists: false },
      },
    })

    let updatedCount = 0
    for (const message of unreadMessages.docs) {
      const updated = await this.markAsRead(message._id)
      if (updated) updatedCount++
    }

    return updatedCount
  }

  async getLatestMessageForConversation(
    conversationId: string,
  ): Promise<OfflineConversationMessage | null> {
    const result = await this.db.find({
      selector: {
        conversationId,
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
      limit: 1,
    })

    return result.docs.length > 0
      ? (result.docs[0] as OfflineConversationMessage)
      : null
  }

  async searchMessages(
    conversationId: string,
    query: string,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        conversationId,
        content: { $regex: new RegExp(query, "i") },
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
    })
    return result.docs as OfflineConversationMessage[]
  }

  async getMessagesByType(
    conversationId: string,
    messageType: string,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        conversationId,
        messageType,
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "DESC" }],
    })
    return result.docs as OfflineConversationMessage[]
  }

  async getMessageCount(conversationId?: string): Promise<number> {
    const selector: any = {
      deletedAt: { $exists: false },
    }

    if (conversationId) {
      selector.conversationId = conversationId
    }

    const result = await this.db.find({
      selector,
      fields: ["_id"],
    })
    return result.docs.length
  }

  async getMessagesInDateRange(
    conversationId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<OfflineConversationMessage[]> {
    const result = await this.db.find({
      selector: {
        conversationId,
        createdAt: {
          $gte: startDate.toISOString(),
          $lte: endDate.toISOString(),
        },
        deletedAt: { $exists: false },
      },
      sort: [{ createdAt: "asc" }],
    })
    return result.docs as OfflineConversationMessage[]
  }

  // Bulk operations for better performance
  async bulkMarkAsRead(messageIds: string[]): Promise<number> {
    let updatedCount = 0
    for (const messageId of messageIds) {
      const updated = await this.markAsRead(messageId)
      if (updated) updatedCount++
    }
    return updatedCount
  }

  async deleteMessagesByConversationId(
    conversationId: string,
    hardDelete = false,
  ): Promise<number> {
    const messages = await this.getMessagesByConversationId(
      conversationId,
      1000,
    ) // Get all messages
    const messageIds = messages.map((msg) => msg.id)
    return this.bulkDelete(messageIds, hardDelete)
  }

  // Sync with optimistic updates
  async sendMessage(
    messageData: Omit<
      OfflineConversationMessage,
      "_id" | "_rev" | "id" | "createdAt" | "updatedAt"
    >,
  ): Promise<OfflineConversationMessage> {
    // Create message locally first (optimistic update)
    const message = await this.create(messageData)

    // Trigger immediate sync for this message
    await this.markForSync(message.id)
    this.backgroundSync()

    return message
  }

  // Sync with conflict resolution
  async syncWithConflictResolution(): Promise<void> {
    try {
      const syncResult = await this.sync()

      if (syncResult.conflicts && syncResult.conflicts.length > 0) {
        console.log("Resolving message conflicts:", syncResult.conflicts.length)

        for (const conflict of syncResult.conflicts) {
          // For messages, usually server wins (messages are immutable)
          await this.resolveConflict(conflict, "server-wins")
        }
      }
    } catch (error) {
      console.error("Error syncing messages:", error)
    }
  }

  private async resolveConflict(
    conflict: any,
    strategy: "server-wins" | "client-wins",
  ): Promise<void> {
    switch (strategy) {
      case "server-wins":
        await this.db.put(conflict.server)
        break
      case "client-wins":
        await this.markForSync(conflict.client._id)
        break
    }
  }
}
