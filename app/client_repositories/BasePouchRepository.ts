import PouchDB from "pouchdb"
import Pouch<PERSON><PERSON><PERSON> from "pouchdb-find"
import { v4 as uuidv4 } from "uuid"
import {
  OfflineEntity,
  OfflineRepository,
  OfflineQueryParams,
  SyncResult,
} from "./types"

// Enable PouchDB plugins
PouchDB.plugin(PouchDBFind)

export abstract class BasePouchRepository<T extends OfflineEntity>
  implements OfflineRepository<T>
{
  protected db: PouchDB.Database
  protected remoteUrl?: string
  protected syncHandler?: PouchDB.Replication.Sync<{}>

  constructor(dbName: string, remoteUrl?: string) {
    this.db = new PouchDB(dbName)
    this.remoteUrl = remoteUrl
    this.setupIndexes()
  }

  protected abstract setupIndexes(): Promise<void>
  protected abstract getApiEndpoint(): string
  protected abstract mapToApiFormat(doc: T): any
  protected abstract mapFromApiFormat(apiData: any): Omit<T, "_id" | "_rev">

  async getById(id: string): Promise<T | null> {
    try {
      const doc = (await this.db.get(id)) as T
      if (doc.deletedAt && !this.shouldIncludeDeleted()) {
        return null
      }
      return doc
    } catch (error: any) {
      if (error.status === 404) {
        return null
      }
      throw error
    }
  }

  async getAll(
    params: OfflineQueryParams = {},
  ): Promise<{ items: T[]; total: number }> {
    const selector: any = {}

    // Handle deleted items
    if (!params.includeDeleted) {
      selector.deletedAt = { $exists: false }
    }

    // Add custom selector
    if (params.selector) {
      Object.assign(selector, params.selector)
    }

    const findOptions: PouchDB.Find.FindRequest<T> = {
      selector,
      limit: params.limit || 1000,
      skip: params.skip || 0,
      fields: params.fields,
    }

    // Handle sorting
    if (params.sort && params.sort.length > 0) {
      findOptions.sort = params.sort
    }

    try {
      const result = await this.db.find(findOptions)
      return {
        items: result.docs as T[],
        total: result.docs.length,
      }
    } catch (error) {
      console.error("Error in getAll:", error)
      return { items: [], total: 0 }
    }
  }

  async create(
    data: Omit<T, "_id" | "_rev" | "id" | "createdAt" | "updatedAt">,
  ): Promise<T> {
    const now = new Date().toISOString()
    const id = uuidv4()

    const doc: T = {
      ...data,
      _id: id,
      id,
      createdAt: now,
      updatedAt: now,
      _syncStatus: "pending",
      _needsSync: true,
    } as T

    const result = await this.db.put(doc)
    const createdDoc = { ...doc, _rev: result.rev }

    // Trigger background sync
    this.backgroundSync()

    return createdDoc
  }

  async update(id: string, data: Partial<T>): Promise<T | null> {
    try {
      const existing = (await this.db.get(id)) as T
      if (existing.deletedAt && !this.shouldIncludeDeleted()) {
        return null
      }

      const updated: T = {
        ...existing,
        ...data,
        updatedAt: new Date().toISOString(),
        _syncStatus: "pending",
        _needsSync: true,
      }

      const result = await this.db.put(updated)
      const updatedDoc = { ...updated, _rev: result.rev }

      // Trigger background sync
      this.backgroundSync()

      return updatedDoc
    } catch (error: any) {
      if (error.status === 404) {
        return null
      }
      throw error
    }
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    try {
      const doc = (await this.db.get(id)) as T

      if (hardDelete) {
        await this.db.remove(doc)
      } else {
        const updated: T = {
          ...doc,
          deletedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          _syncStatus: "pending",
          _needsSync: true,
        }
        await this.db.put(updated)
      }

      // Trigger background sync
      this.backgroundSync()

      return true
    } catch (error: any) {
      if (error.status === 404) {
        return false
      }
      throw error
    }
  }

  async bulkCreate(
    items: Omit<T, "_id" | "_rev" | "id" | "createdAt" | "updatedAt">[],
  ): Promise<T[]> {
    const now = new Date().toISOString()
    const docs: T[] = items.map(
      (item) =>
        ({
          ...item,
          _id: uuidv4(),
          id: uuidv4(),
          createdAt: now,
          updatedAt: now,
          _syncStatus: "pending",
          _needsSync: true,
        }) as T,
    )

    const result = await this.db.bulkDocs(docs)
    const createdDocs = docs.map((doc, index) => ({
      ...doc,
      _rev: (result[index] as any).rev,
    }))

    // Trigger background sync
    this.backgroundSync()

    return createdDocs
  }

  async bulkUpdate(
    updates: { id: string; data: Partial<T> }[],
  ): Promise<number> {
    let successCount = 0

    for (const { id, data } of updates) {
      const updated = await this.update(id, data)
      if (updated) successCount++
    }

    return successCount
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    let successCount = 0

    for (const id of ids) {
      const deleted = await this.delete(id, hardDelete)
      if (deleted) successCount++
    }

    return successCount
  }

  async sync(): Promise<SyncResult> {
    if (!this.remoteUrl) {
      return { success: false, errors: ["No remote URL configured"] }
    }

    try {
      const startTime = new Date()

      // Get unsynced items
      const unsyncedItems = await this.getUnsyncedItems()

      // Push local changes to server
      await this.pushToServer(unsyncedItems)

      // Pull changes from server
      await this.pullFromServer()

      const endTime = new Date()

      return {
        success: true,
        docs_read: 0,
        docs_written: unsyncedItems.length,
        start_time: startTime,
        end_time: endTime,
      }
    } catch (error) {
      console.error("Sync error:", error)
      return {
        success: false,
        errors: [error],
      }
    }
  }

  async getUnsyncedItems(): Promise<T[]> {
    const result = await this.db.find({
      selector: {
        _needsSync: true,
      },
    })
    return result.docs as T[]
  }

  async markForSync(id: string): Promise<void> {
    try {
      const doc = (await this.db.get(id)) as T
      const updated: T = {
        ...doc,
        _needsSync: true,
        _syncStatus: "pending",
      }
      await this.db.put(updated)
    } catch (error) {
      console.error("Error marking for sync:", error)
    }
  }

  async clearCache(): Promise<void> {
    await this.db.destroy()
    this.db = new PouchDB(this.db.name)
    await this.setupIndexes()
  }

  protected shouldIncludeDeleted(): boolean {
    return false
  }

  protected async backgroundSync(): Promise<void> {
    // Debounced sync - only sync if not already syncing
    if (!this.syncHandler) {
      setTimeout(() => {
        this.sync().finally(() => {
          this.syncHandler = undefined
        })
      }, 1000)
    }
  }

  protected async pushToServer(items: T[]): Promise<void> {
    if (!this.remoteUrl || items.length === 0) return

    try {
      const apiData = items.map((item) => this.mapToApiFormat(item))

      // Push to server API
      const response = await fetch(this.getApiEndpoint(), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ items: apiData }),
      })

      if (response.ok) {
        // Mark items as synced
        for (const item of items) {
          const updated: T = {
            ...item,
            _syncStatus: "synced",
            _needsSync: false,
            _lastSyncAt: new Date().toISOString(),
          }
          await this.db.put(updated)
        }
      }
    } catch (error) {
      console.error("Error pushing to server:", error)
    }
  }

  protected async pullFromServer(): Promise<void> {
    if (!this.remoteUrl) return

    try {
      const response = await fetch(this.getApiEndpoint())
      if (response.ok) {
        const data = await response.json()
        const items = data.data?.items || data.items || []

        for (const apiItem of items) {
          const localFormat = this.mapFromApiFormat(apiItem)
          const doc: T = {
            ...localFormat,
            _id: localFormat.id,
            _syncStatus: "synced",
            _needsSync: false,
            _lastSyncAt: new Date().toISOString(),
          } as T

          try {
            // Try to update existing doc
            const existing = await this.db.get(doc._id)
            doc._rev = existing._rev
            await this.db.put(doc)
          } catch (error: any) {
            if (error.status === 404) {
              // Create new doc
              await this.db.put(doc)
            }
          }
        }
      }
    } catch (error) {
      console.error("Error pulling from server:", error)
    }
  }
}
