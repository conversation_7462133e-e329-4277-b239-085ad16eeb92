"use client"

import { useLocalization } from "@/localization/functions/client"
import { homeLocales } from "./locales"
import Link from "next/link"

export default function Home() {
  const { t } = useLocalization("home", homeLocales)

  return (
    <div className="min-h-screen flex flex-col justify-between bg-white">
      {/* Hero Section */}
      <main className="flex-1 flex flex-col items-center justify-center px-4 text-center">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">
          {t("hero_title")}
        </h1>
        <p className="text-lg text-gray-600 max-w-xl mb-8">{t("hero_desc")}</p>

        {/* Login Button */}
        <Link href="/auth/login">
          <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition">
            {t("login_button")}
          </button>
        </Link>
      </main>

      {/* Footer (optional) */}
      <footer className="text-center py-4 text-sm text-gray-500">
        © {new Date().getFullYear()} CS AI. {t("footer_copyright")}
      </footer>
    </div>
  )
}
