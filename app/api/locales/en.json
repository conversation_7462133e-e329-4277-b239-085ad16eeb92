{"api": {"success": {"created": "Resource created successfully", "updated": "Resource updated successfully", "deleted": "Resource deleted successfully", "fetched": "Resource fetched successfully", "logged_in": "Logged in successfully", "logged_out": "Logged out successfully", "password_changed": "Password changed successfully", "password_reset_sent": "Password reset email sent successfully", "email_verified": "Email verified successfully", "verification_sent": "Verification email sent successfully", "bulk_import_completed": "Bulk import completed successfully", "bulk_update_completed": "Bulk update completed successfully", "bulk_delete_completed": "Bulk delete completed successfully"}, "error": {"validation_failed": "Validation failed", "invalid_email": "Invalid email format", "invalid_phone": "Invalid phone number format", "invalid_id": "Invalid ID format", "invalid_token": "Invalid or expired token", "invalid_credentials": "Invalid credentials", "invalid_update_data": "Invalid update data", "not_found": "Resource not found", "duplicate_resource": "Resource already exists", "duplicate_email": "Email already exists", "duplicate_phone": "Phone number already exists", "duplicate_name": "Name already exists", "create_failed": "Failed to create resource. Please try again.", "update_failed": "Failed to update resource. Please try again.", "delete_failed": "Failed to delete resource. Please try again.", "fetch_failed": "Failed to fetch resource. Please try again.", "login_failed": "<PERSON><PERSON> failed. Please check your credentials.", "logout_failed": "<PERSON><PERSON><PERSON> failed. Please try again.", "internal_server_error": "Internal server error", "database_error": "Database error occurred", "network_error": "Network error occurred", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "session_expired": "Session has expired", "session_required": "Session required", "bulk_import_failed": "Bulk import failed", "bulk_update_failed": "Bulk update failed", "bulk_delete_failed": "Bulk delete failed", "invalid_data_format": "Invalid data format - expected array", "no_data_provided": "No data provided", "search_keyword_empty": "Search keyword cannot be empty", "filter_field_empty": "Filter field cannot be empty", "sort_field_empty": "Sort field cannot be empty", "invalid_sort_direction": "Sort direction must be 'asc' or 'desc'", "file_not_found": "File not found", "file_upload_failed": "File upload failed", "invalid_file_format": "Invalid file format", "file_too_large": "File too large", "atleast_2_characters": "Name must be at least 2 characters", "invalid_email_format": "Invalid email format", "invalid_phone_format": "Invalid phone number format", "name_must_not_contain_special_char": "Name must not contain special characters", "name_must_not_empty": "Name must not be empty"}, "contact": {"created": "Contact created successfully", "updated": "Contact updated successfully", "deleted": "Contact deleted successfully", "not_found": "Contact not found", "duplicate_phone": "Contact with this phone number already exists", "duplicate_name": "Contact with this name already exists", "create_failed": "Failed to create contact. Please try again.", "update_failed": "Failed to update contact. Please try again.", "delete_failed": "Failed to delete contact. Please try again.", "fetch_failed": "Failed to fetch contacts. Please try again."}, "message_template": {"created": "Message template created successfully", "updated": "Message template updated successfully", "deleted": "Message template deleted successfully", "not_found": "Message template not found", "duplicate_name": "Message template with this name already exists", "create_failed": "Failed to create message template. Please try again.", "update_failed": "Failed to update message template. Please try again.", "delete_failed": "Failed to delete message template. Please try again.", "fetch_failed": "Failed to fetch message templates. Please try again."}, "ai_rule": {"created": "AI rule created successfully", "updated": "AI rule updated successfully", "deleted": "AI rule deleted successfully", "not_found": "AI rule not found", "duplicate_name": "AI rule with this name already exists", "create_failed": "Failed to create AI rule. Please try again.", "update_failed": "Failed to update AI rule. Please try again.", "delete_failed": "Failed to delete AI rule. Please try again.", "fetch_failed": "Failed to fetch AI rules. Please try again."}, "user": {"created": "User created successfully", "updated": "User updated successfully", "deleted": "User deleted successfully", "not_found": "User not found", "duplicate_email": "User with this email already exists", "create_failed": "Failed to create user. Please try again.", "update_failed": "Failed to update user. Please try again.", "delete_failed": "Failed to delete user. Please try again.", "fetch_failed": "Failed to fetch users. Please try again."}, "search": {"invalid_query": "Invalid search query", "no_results": "No results found", "query_too_short": "Search query too short", "query_too_long": "Search query too long"}, "filter": {"invalid_value": "Invalid filter value", "unsupported_field": "Unsupported filter field", "invalid_operator": "Invalid filter operator"}, "search_config": {"contact": {"name": "Name", "phone": "Phone Number", "email": "Email Address", "has_email": "<PERSON>", "tags": "Tags", "status": "Status", "created_by": "Created By", "created_date": "Created Date", "updated_date": "Last Updated", "has_phone": "Has Phone Number", "has_notes": "Has Notes"}, "sales": {"customer_name": "Customer Name", "customer_email": "Customer <PERSON><PERSON>", "product_name": "Product Name", "amount": "Sale Amount", "status": "Order Status", "sales_rep": "Sales Representative", "payment_method": "Payment Method", "sale_date": "Sale Date", "delivery_date": "Delivery Date", "is_recurring": "Recurring Sale", "has_discount": "Has Discount", "region": "Sales Region"}, "sort": {"name": "Name", "email": "Email", "phone": "Phone", "created_date": "Created Date", "updated_date": "Updated Date", "status": "Status"}, "date": {"today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "this_year": "This Year", "last_year": "Last Year", "custom": "Custom Range", "all": "All Time", "today_desc": "Contacts created today", "yesterday_desc": "Contacts created yesterday", "this_week_desc": "Contacts created this week", "last_week_desc": "Contacts created last week", "this_month_desc": "Contacts created this month", "last_month_desc": "Contacts created last month", "this_year_desc": "Contacts created this year", "last_year_desc": "Contacts created last year", "custom_desc": "Select custom date range", "all_desc": "All contacts"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "archived": "Archived", "deleted": "Deleted"}, "created_by": {"system": "System", "admin": "Admin", "user": "User", "import": "Import", "api": "API"}, "boolean": {"yes": "Yes", "no": "No"}, "placeholder": {"name": "Search by contact name...", "phone": "Search by phone number...", "email": "Search by email address..."}}}}