import { NextRequest, NextResponse } from "next/server"
import { workflowExecutionsBusinessLogic } from "../../../../../lib/repositories/businessLogics"
import { implHandleGetAiWorkflowExecutionsStats } from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)

    // Parse basic parameters
    const search = searchParams.get("search") || undefined
    const includeDeleted = searchParams.get("includeDeleted") === "true"

    // Parse filters parameter (expects JSON array or field:value pairs)
    let filters: { field: string; value: any }[] = []
    const filtersParam = searchParams.get("filters")
    if (filtersParam) {
      try {
        filters = JSON.parse(filtersParam)
      } catch {
        filters = filtersParam
          .split(",")
          .map((filter) => {
            const [field, ...valueParts] = filter.trim().split(":")
            const value = valueParts.join(":").trim()
            return field.trim() && value ? { field: field.trim(), value } : null
          })
          .filter(Boolean) as { field: string; value: any }[]
      }
    }

    // Parse date range parameters
    const dateFrom = searchParams.get("dateFrom") || undefined
    const dateTo = searchParams.get("dateTo") || undefined

    const params = {
      search,
      includeDeleted,
      filters,
      dateFrom,
      dateTo,
    }

    const result = await implHandleGetAiWorkflowExecutionsStats(
      workflowExecutionsBusinessLogic,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecutions stats GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
