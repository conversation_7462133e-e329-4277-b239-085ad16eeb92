import { ERROR_CODES } from "@/app/api/error_codes"
import { workflowExecutionsBusinessLogic } from "@/lib/repositories/businessLogics"
import { NextRequest, NextResponse } from "next/server"
import { implHandleUpdateAiWorkflowExecution } from "../../impl"
import { AiWorkflowStepUpdateSchema } from "@/lib/validations/workflowExecution"
import { AiWorkflowStep } from "@/lib/repositories/aiWorkflowExecutions"
import { realtime } from "@/lib/realtime"

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await context.params
    const { newStep } = await req.json()

    if (!newStep || typeof newStep !== "object") {
      return NextResponse.json(
        { status: "failed", errors: ["Invalid or missing newStep"] },
        { status: 400 },
      )
    }

    const newStepFromRequest = AiWorkflowStepUpdateSchema.safeParse(newStep)

    // Fetch the existing workflow execution by id (pseudo code)
    const existingExecution = await workflowExecutionsBusinessLogic.getById(id)
    if (!existingExecution) {
      return NextResponse.json(
        { status: "failed", errors: ["Workflow execution not found"] },
        { status: 404 },
      )
    }

    if (!newStepFromRequest.success) {
      const errors = newStepFromRequest.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )
      return NextResponse.json(
        { status: "failed", errors: errors },
        { status: 400 },
      )
    }

    const durationFromLastStep =
      existingExecution.steps && existingExecution.steps.length > 0
        ? existingExecution.steps[existingExecution.steps.length - 1].duration
        : 0

    const newStepModel: AiWorkflowStep = {
      ...newStepFromRequest.data,
      id: Math.random().toString(36).substring(2, 11),
      timestamp: new Date().toISOString(),
      duration: durationFromLastStep,
      createdAt: new Date(),
    }

    await realtime.server.trigger("workflow-channel", "new-workflow-step", {
      workflowId: id,
      newStep: newStepModel,
    })

    const updatedSteps = [...(existingExecution.steps || []), newStepModel]

    // Prepare update body (only steps array updated)
    const updateBody = { steps: updatedSteps }

    // Call your existing update logic with the partial update
    const result = await implHandleUpdateAiWorkflowExecution(
      id,
      updateBody,
      workflowExecutionsBusinessLogic,
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecution Add Step route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
