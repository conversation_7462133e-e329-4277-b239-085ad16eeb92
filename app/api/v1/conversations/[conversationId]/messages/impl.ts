import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  ConversationMessageCreateSchema,
  ConversationMessageUpdateSchema,
} from "@/lib/validations/conversationMessage"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ConversationMessageBusinessLogicInterface } from "@/lib/repositories/conversationMessages"
import {
  ConversationMessage,
  ConversationMessageCreateInput,
  ConversationMessageUpdateInput,
} from "@/lib/repositories/conversationMessages"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations"
import { realtime } from "@/lib/realtime"

// Create ConversationMessage Implementation
export async function implHandleCreateConversationMessage(
  data: any,
  businessLogic: ConversationMessageBusinessLogicInterface,
  conversationBusinessLogic: ConversationBusinessLogicInterface,
  conversationId: string,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = ConversationMessageCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const conversationMessage = await businessLogic.create(
      {
        ...validationResult.data,
        senderId: context.user.id,
        messageType: "TEXT",
      },
      context,
    )

    const conversation = await conversationBusinessLogic.update(
      conversationId,
      {
        lastMessage: {
          body: validationResult.data.content,
          fromMe: true,
          _data: undefined,
          ack: 1,
        },
        lastMessageAt: new Date(),
      },
      context,
    )

    realtime.server.trigger("conversation-room-channel", "new-message", {
      conversationId,
      conversation,
      message: conversationMessage,
    })

    return {
      status: 201,
      body: new ResponseWrapper("success", conversationMessage),
    }
  } catch (error: any) {
    console.error("Create conversationMessage error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create conversationMessage. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update ConversationMessage Implementation
export async function implHandleUpdateConversationMessage(
  id: string,
  data: any,
  businessLogic: ConversationMessageBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = ConversationMessageUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const conversationMessage = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!conversationMessage) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["ConversationMessage not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", conversationMessage),
    }
  } catch (error: any) {
    console.error("Update conversationMessage error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update conversationMessage. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete ConversationMessage Implementation
export async function implHandleDeleteConversationMessage(
  id: string,
  businessLogic: ConversationMessageBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["ConversationMessage not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "ConversationMessage deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete conversationMessage error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete conversationMessage. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get ConversationMessage by ID Implementation
export async function implHandleGetConversationMessage(
  id: string,
  businessLogic: ConversationMessageBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const conversationMessage = await businessLogic.getById(
      id,
      context,
      includeDeleted,
    )

    if (!conversationMessage) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["ConversationMessage not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", conversationMessage),
    }
  } catch (error: any) {
    console.error("Get conversationMessage error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch conversationMessage. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Get All ConversationMessage Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllConversationMessages(
  conversationId: string,
  businessLogic: ConversationMessageBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof ConversationMessage | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof ConversationMessage | string
      value: ConversationMessage[keyof ConversationMessage] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<ConversationMessage>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<ConversationMessage>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<
              GetAllResultPaginated<ConversationMessage>
            >(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<
              GetAllResultPaginated<ConversationMessage>
            >(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<
              GetAllResultPaginated<ConversationMessage>
            >(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(conversationId, queryParams, context)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<ConversationMessage>>(
        "success",
        {
          ...result,
          page: queryParams.page,
        },
      ),
    }
  } catch (error: any) {
    console.error("Get conversationMessage error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<ConversationMessage>>(
        "failed",
        undefined,
        ["Failed to fetch conversationMessage. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}