import { NextRequest, NextResponse } from "next/server"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { ConversationDBRepository } from "@/lib/repositories/conversations/DBRepository"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { conversationBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

interface CloseConversationBody {
  reason?: string
  notes?: string
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ conversationId: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(request)
    if (response) {
      return response
    }

    const { conversationId } = await params
    const body: CloseConversationBody = await request.json()

    // Get the existing conversation
    const existingConversation = await conversationBusinessLogic.getById(conversationId, context)
    if (!existingConversation) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 }
      )
    }

    if (existingConversation.status === "CLOSED") {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation is already closed"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 }
      )
    }

    // Prepare close metadata
    const closeMetadata = {
      reason: body.reason || "Closed by user",
      closedAt: new Date(),
      closedBy: context.user.id,
      notes: body.notes,
      duration: existingConversation.createdAt
        ? Math.round((new Date().getTime() - new Date(existingConversation.createdAt).getTime()) / (1000 * 60))
        : undefined
    }

    // Update conversation with closed status and metadata
    const updatedConversation = await conversationBusinessLogic.update(
      conversationId,
      {
        status: "CLOSED",
        close_metadata: closeMetadata,
      },
      context
    )

    return NextResponse.json(
      new ResponseWrapper("success", updatedConversation),
      { status: 200 }
    )
  } catch (error: any) {
    console.error("Error closing conversation:", error)

    if (error.code) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [error.code],
        ),
        { status: error.statusCode || 400 }
      )
    }

    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 }
    )
  }
}
