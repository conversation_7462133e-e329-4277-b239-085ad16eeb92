import {
  ConversationBusinessLogicInterface,
  Conversation,
  ConversationCreateInput,
  ConversationUpdateInput,
} from "@/lib/repositories/conversations/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  ConversationCreateSchema,
  ConversationUpdateSchema,
} from "@/lib/validations/conversation"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import {
  contactsBusinessLogic,
  usersBusinessLogic,
} from "@/lib/repositories/businessLogics"

// Create Conversation Implementation
export async function implHandleCreateConversation(
  data: any,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = ConversationCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    // Add context fields to the validated data
    const conversationData: ConversationCreateInput = {
      ...validationResult.data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const conversation = await businessLogic.create(conversationData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Create conversation error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create conversation. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update Conversation Implementation
export async function implHandleUpdateConversation(
  id: string,
  data: any,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = ConversationUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const conversation = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!conversation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Update conversation error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update conversation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete Conversation Implementation
export async function implHandleDeleteConversation(
  id: string,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Conversation deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete conversation error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete conversation. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get Conversation by ID Implementation
export async function implHandleGetConversation(
  id: string,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const conversation = await businessLogic.getById(
      id,
      context,
      includeDeleted,
    )

    if (!conversation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Get conversation error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch conversation. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Unified Get All Conversations Handler with Optimized Participant Resolution
export async function implHandleGetAllConversations(
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof Conversation | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof Conversation | string
      value: Conversation[keyof Conversation] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Conversation> | undefined>
}> {
  try {
    // === Validations ===
    if (params?.search !== undefined && params.search.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Search keyword cannot be empty"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    if (params?.filters?.some((f) => !f.field || f.field.trim() === "")) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Filter field cannot be empty"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    if (
      params?.sort?.some(
        (s) => !s.field || !["ASC", "DESC"].includes(s.direction),
      )
    ) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Sort direction must be 'asc' or 'desc'"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // === Query Param Builder ===
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
      search: params?.search?.trim(),
      filters: params?.filters,
      sort: params?.sort,
    }

    const result = await businessLogic.getAll(queryParams, context)
    const conversations = result.items

    // === Optimized Participant Resolution ===
    const participantCache = new Map<string, { id: string; name: string }>()

    const resolveParticipant = async (participantId: string) => {
      if (participantCache.has(participantId)) {
        return participantCache.get(participantId)!
      }

      const [contact, user] = await Promise.all([
        contactsBusinessLogic.getById(participantId, context),
        usersBusinessLogic.getById(participantId, context),
      ])

      const name = contact?.name || user?.name || participantId
      const participant = { id: participantId, name }

      participantCache.set(participantId, participant)
      return participant
    }

    const enrichedConversations = await Promise.all(
      conversations.map(async (conversation) => {
        const participantsFull = await Promise.all(
          conversation.participants.map(resolveParticipant),
        )

        return {
          ...conversation,
          participantsFull,
        }
      }),
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        ...result,
        items: enrichedConversations,
        page: queryParams.page,
      }),
    }
  } catch (error: any) {
    console.error("Get conversation error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch conversation. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Bulk Create Conversation Implementation
export async function implHandleBulkCreateConversation(
  data: any[],
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each item in the array
    const validatedData: ConversationCreateInput[] = []
    for (let i = 0; i < data.length; i++) {
      const validationResult = ConversationCreateSchema.safeParse(data[i])
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Item ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }
      // Add context fields to each validated item
      const conversationData: ConversationCreateInput = {
        ...validationResult.data,
        createdBy: context.user.id,
        organizationId: context.organization?.id,
      }
      validatedData.push(conversationData)
    }

    const conversation = await businessLogic.bulkCreate(validatedData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Bulk create conversation error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create conversation. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Bulk Update Conversation Implementation
export async function implHandleBulkUpdateConversation(
  updates: { id: string; data: any }[],
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: ConversationUpdateInput }[] = []
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i]

      if (!update.id || typeof update.id !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }

      const validationResult = ConversationUpdateSchema.safeParse(update.data)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Update ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data,
      })
    }

    const updatedCount = await businessLogic.bulkUpdate(
      validatedUpdates,
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    }
  } catch (error: any) {
    console.error("Bulk update conversation error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update conversation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Bulk Delete Conversation Implementation
export async function implHandleBulkDeleteConversation(
  ids: string[],
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i]
      if (!id || typeof id !== "string" || id.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    const deletedCount = await businessLogic.bulkDelete(
      ids,
      context,
      hardDelete,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    }
  } catch (error: any) {
    console.error("Bulk delete conversation error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete conversation. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Restore Conversation Implementation
export async function implHandleRestoreConversation(
  id: string,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const restored = await businessLogic.restore(id, context)

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    }
  } catch (error: any) {
    console.error("Restore conversation error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore conversation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}
