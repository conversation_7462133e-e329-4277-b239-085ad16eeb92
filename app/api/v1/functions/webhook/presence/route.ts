import { v4 as uuid } from "uuid"
import { realtime } from "@/lib/realtime"
import { NextRequest, NextResponse } from "next/server"

export async function POST(req: NextRequest) {
  const body = await req.json()

  // Push to realtime
  if (body?.event === "presence.update") {
    await realtime.server.trigger("message-channel", "update-presence", {
      presences: body?.payload?.presences,
    })
  }

  return NextResponse.json({ success: true })
}
