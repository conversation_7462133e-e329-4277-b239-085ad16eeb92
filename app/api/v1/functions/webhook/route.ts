import { realtime } from "@/lib/realtime"
import {
  conversationBusinessLogic,
  workflowExecutionsBusinessLogic,
  conversationMessagesBusinessLogic,
  contactsBusinessLogic,
} from "@/lib/repositories/businessLogics"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import { NextRequest, NextResponse } from "next/server"
import { implHandleCreateAiWorkflowExecution } from "../../ai-workflow-executions/impl"
import { implHandleTryAnswer } from "../../ai/execution/impl"
import { aiBusinessLogic } from "../../ai/execution/shared"
import { WahaWebhookPayload } from "./wahaWebhookPayload"
import { SessionContext } from "@/lib/repositories/auth/types"
import {
  Conversation,
  ConversationCreateInput,
} from "@/lib/repositories/conversations/interface"
import { ConversationMessageCreateInput } from "@/lib/repositories/conversationMessages/interface"

export async function POST(req: NextRequest) {
  const body = (await req.json()) as WahaWebhookPayload

  const sessionToConversation = driver.getCollection(
    "session_to_conversationroom",
  )
  await sessionToConversation.createIndexes([
    { key: { id: 1 }, unique: true },
    { key: { waha_conversation_id: 1 }, unique: true },
  ])

  const conversationFromSession = await sessionToConversation.findOne({
    waha_conversation_id: body.payload.from,
  })

  const sessionToContextTable = driver.getCollection("session_to_context")
  const doc = await sessionToContextTable.findOne({
    session: body.session,
  })

  if (!doc) {
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Session not found"],
        errorCodes: ["SESSION_NOT_FOUND"],
      },
      { status: 400 },
    )
  }

  const context: SessionContext = {
    user: doc.user,
    organization: doc.organization,
  }

  const customerPhoneNumber = body.payload.from.replace("@c.us", "")

  let contact = await contactsBusinessLogic.getByPhone(
    customerPhoneNumber,
    context,
  )
  if (!contact) {
    contact = await contactsBusinessLogic.create(
      {
        name: body.payload._data?.pushName || customerPhoneNumber,
        phone: customerPhoneNumber,
      },
      context,
    )
  }

  const messagesTable = driver.getCollection("waha_message_webhook")
  const document = await messagesTable.insertOne(body)

  let conversation: Conversation

  if (body?.event === "message" && body.payload) {
    const payload = body.payload

    try {
      // 1. Load conversation if session mapped
      if (conversationFromSession) {
        conversation = (await conversationBusinessLogic.getById(
          conversationFromSession.id,
          context,
        )) as Conversation

        if (!conversation) {
          throw new Error(
            `Conversation ID ${conversationFromSession.id} not found for existing session mapping`,
          )
        }

        const updateData = {
          lastMessage: {
            body: payload.body,
            fromMe: false,
            _data: undefined,
            ack: payload.ack,
          },
          lastMessageAt: new Date(payload.timestamp * 1000),
        }

        conversation = (await conversationBusinessLogic.update(
          conversation.id,
          updateData,
          context,
        )) as Conversation
        console.log(`✅ Updated existing conversation room: ${conversation.id}`)
      } else {
        // 2. No session-to-conversation mapping → create new conversation & map it
        const conversationInput: ConversationCreateInput = {
          name: payload._data?.pushName || payload.from,
          description: `Conversation with ${payload._data?.pushName || payload.from}`,
          participants: [contact.id, context.user.id],
          tags: ["waha", "whatsapp"],
          isActive: true,
          createdBy: context.user.id,
          organizationId: context.organization?.id,
        }

        conversation = await conversationBusinessLogic.create(
          conversationInput,
          context,
        )
        await sessionToConversation.insertOne({
          session: body.session,
          id: conversation.id,
          waha_conversation_id: body.payload.from,
        })

        console.log(`✅ Created new conversation room: ${conversation.id}`)

        await realtime.server.trigger("conversation-room-channel", "new-room", {
          conversation,
          isNew: true,
        })
      }

      // 3. Create the message
      const messageInput: ConversationMessageCreateInput = {
        conversationId: conversation.id,
        content: payload.body,
        messageType: payload.hasMedia ? "IMAGE" : "TEXT",
        senderId: contact.id,
        metadata: {
          wahaMessageId: payload.id,
          wahaTimestamp: payload.timestamp,
          wahaSession: body.session,
          wahaAck: payload.ack,
          wahaAckName: payload.ackName,
          wahaSource: payload.source,
          wahaData: payload._data,
          hasMedia: payload.hasMedia,
          media: payload.media,
          replyTo: payload.replyTo,
          fromMe: payload.fromMe,
          ack: payload.ack,
        },
      }

      console.log("MessageInput", messageInput)

      const message = await conversationMessagesBusinessLogic.create(
        messageInput,
        context,
      )
      console.log(
        `✅ Stored WAHA message: ${message.id} in conversation: ${conversation.id}`,
      )

      // 4. AI Rule Inference
      const createAiWorkflowExecutionResult =
        await implHandleCreateAiWorkflowExecution(
          {
            customerId: payload.from,
            originalMessage: payload.body,
          },
          workflowExecutionsBusinessLogic,
        )

      if (!payload.fromMe && isPrivateMessage(payload)) {
        try {
          const result = await implHandleTryAnswer(
            conversation.id,
            createAiWorkflowExecutionResult.body.data?.id,
            { message: payload.body },
            aiBusinessLogic,
            context,
          )

          if (result.status === 200) {
            await messagesTable.updateOne(
              { _id: document.insertedId },
              {
                $set: {
                  cs_ai_extras: {
                    ruleInference: result.body.data,
                    waha_session: body.session,
                    waha_conversation: payload.from,
                    ai_workflow_execution_id:
                      createAiWorkflowExecutionResult.body.data?.id,
                    msg_room_id: conversation.id,
                  },
                },
              },
            )
          } else {
            console.error("❌ AI rule inference failed:", result.body.error)
          }
        } catch (error) {
          console.error("❌ Error during AI rule handling:", error)
        }
      }

      // 5. Final trigger
      await realtime.server.trigger(
        "conversation-room-channel",
        "new-message",
        conversation.id,
      )
    } catch (error) {
      console.error("❌ Failed to handle WAHA conversation/message:", error)
    }
  }

  return NextResponse.json({ success: true })
}

function isPrivateMessage(payload: any): boolean {
  return typeof payload.from === "string" && payload.from.includes("@c.us")
}
