import { NextRequest, NextResponse } from "next/server"
import { providers } from "@/lib/providers"
import { getAuth<PERSON>ookie, setAuth<PERSON><PERSON><PERSON> } from "@/lib/cookies"
import { realtime } from "@/lib/realtime"

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    let providerKey = searchParams.get("provider")
    let limit = searchParams.get("limit")
    let offset = searchParams.get("offset")
    let filter = searchParams.get("filter") as unknown as string[] | undefined

    if (!limit) limit = "20"
    if (!offset) offset = "0"
    if (!filter) filter = []

    if (!providerKey) {
      providerKey = (await getAuthCookie("preferred_provider")) || "waha"
    }

    const provider = providers[providerKey]
    if (!provider) {
      return NextResponse.json(
        { success: false, error: `Provider "${providerKey}" tidak tersedia.` },
        { status: 400 },
      )
    }

    if (!getAuthCookie("preferred_provider") && providerKey) {
      setAuthCookie("preferred_provider", providerKey)
    }

    let sessionId: string | undefined
    if (providerKey === "waha") {
      sessionId = await getAuthCookie("waha_session_id")
    }

    const conversations = await provider.listConversations(
      sessionId,
      Number(limit),
      Number(offset),
      filter,
    )

    // 🔔 Trigger realtime event
    await realtime.server.trigger(
      "conversation-updates",
      "conversations-listed",
      {
        provider: provider.name,
        session: sessionId,
        conversations: conversations.reverse()?.[0], // Reverse the order to show latest first
      },
    )

    return NextResponse.json({
      success: true,
      provider: provider.name,
      session: sessionId,
      conversations,
    })
  } catch (error: any) {
    console.error("get listConversations Error:", error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    )
  }
}
