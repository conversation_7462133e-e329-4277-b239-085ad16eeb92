import { NextRequest, NextResponse } from "next/server"
import { implHandleSendTyping } from "./impl"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const body = await req.json()
    const result = await implHandleSendTyping(body, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Send typing route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
