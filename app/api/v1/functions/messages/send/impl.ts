import { ERROR_CODES } from "@/app/api/error_codes"
import { providers } from "@/lib/providers"
import { SessionContext } from "@/lib/repositories/auth/types"
import {
  conversationMessagesBusinessLogic,
  conversationBusinessLogic,
} from "@/lib/repositories/businessLogics"
import { ConversationMessageCreateInput } from "@/lib/repositories/conversationMessages"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import { SendMessageSchema } from "@/lib/schemas/messages"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleSendMessage(
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = SendMessageSchema.safeParse(body)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { conversationId, text } = validationResult.data

    const provider = providers[process.env.WHATSAPP_PROVIDER!]

    const sessionToConversationTable = driver.getCollection(
      "session_to_conversationroom",
    )
    const doc = await sessionToConversationTable.findOne({ id: conversationId })
    if (!doc) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session for Conversation room not found."],
          [ERROR_CODES.SESSION_REQUIRED],
        ),
      }
    }
    const session = doc.session
    const waha_conversation_id = doc.waha_conversation_id

    if (!session) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session is required for sending messages."],
          [ERROR_CODES.SESSION_REQUIRED],
        ),
      }
    }

    const response = await provider.sendMessage(
      waha_conversation_id,
      text,
      session,
    )

    const messageInput: ConversationMessageCreateInput = {
      conversationId,
      content: text,
      messageType: "TEXT",
      sender: context.user.id,
    }

    const message = await conversationMessagesBusinessLogic.create(
      messageInput,
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        provider: provider.name,
        session,
        result: response,
      }),
    }
  } catch (error: any) {
    console.error("Send message error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to send message. Please try again."],
        [ERROR_CODES.SEND_FAILED],
      ),
    }
  }
}
