import { NextRequest, NextResponse } from "next/server"
import { ERROR_CODES } from "@/app/api/error_codes"
import {
  implHandleMessageTemplate,
  implHandleDatasource,
  implHandleReply,
  implHandleRuleInference,
} from "./impl"
import { aiBusinessLogic } from "./shared"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import { providers } from "@/lib/providers"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { SessionContext } from "@/lib/repositories/auth/types"

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()

    const sessionToContextTable = driver.getCollection("session_to_context")
    const doc = await sessionToContextTable.findOne({
      session: body.session,
    })

    if (!doc) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Session not found"],
          errorCodes: ["SESSION_NOT_FOUND"],
        },
        { status: 400 },
      )
    }

    const context: SessionContext = {
      user: doc.user,
      organization: doc.organization,
    }

    if (!body.context) {
      return NextResponse.json(
        {
          status: "failed",
          errors: ["context field is required"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED],
        },
        { status: 400 },
      )
    }

    let result
    const ctx = body.context.toUpperCase()
    const sessionId = body.session_id
    const executionId = body.execution_id
    switch (ctx) {
      case "INFER_MESSAGE_RULE":
        result = await implHandleRuleInference(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break

      case "SELECT_MESSAGE_TEMPLATE":
        result = await implHandleMessageTemplate(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break

      case "SUPPLY_WITH_DATASOURCE":
        result = await implHandleDatasource(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
          context,
        )
        break

      case "REPLY":
        result = await implHandleReply(
          sessionId,
          executionId,
          body,
          aiBusinessLogic,
        )

        // Store AI response in conversation system
        const messagesTable = driver.getCollection("waha_message_webhook")
        const lastMessage = await messagesTable.findOne(
          { "cs_ai_extras.msg_room_id": sessionId },
          { sort: { timestamp: -1 } },
        )

        if (lastMessage) {
          const conversationId = lastMessage.cs_ai_extras?.waha_conversation
          const session = lastMessage.cs_ai_extras?.waha_session
          const recipientId = lastMessage.payload?.from

          try {
            // const messageInput: ConversationMessageCreateInput = {
            //   conversationId: sessionId,
            //   content,
            //   messageType: "TEXT",
            //   metadata: {
            //     ...metadata,
            //     source: "ai",
            //     fromMe: true,
            //     isAIResponse: true
            //   },
            //   createdBy: context.user.id,
            //   organizationId: context.organization?.id
            // }

            // const message = await conversationMessagesBusinessLogic.create(messageInput, context)

            // const lastMessage: ConversationLastMessage = {
            //   body: content,
            //   fromMe: true,
            //   _data: { messageTimestamp: new Date() },
            //   ack: 1
            // }

            // await conversationBusinessLogic.update(sessionId, {
            //   lastMessage: lastMessage as any,
            //   lastMessageAt: new Date()
            // }, context)
            console.log(`✅ Stored AI response for session: ${sessionId}`)
          } catch (error) {
            console.error("❌ Failed to store AI response:", error)
          }

          // Send via WAHA
          providers[process.env.WHATSAPP_PROVIDER!].sendMessage(
            conversationId,
            body.final_message,
            session,
          )
        } else {
          console.error("Failed to find last message for session:", sessionId)
        }
        break

      default:
        return NextResponse.json(
          {
            status: "failed",
            errors: [`Unsupported context: ${body.context}`],
            errorCodes: [ERROR_CODES.VALIDATION_FAILED],
          },
          { status: 400 },
        )
    }

    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AI POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
