import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { AiBusinessLogic } from "./AIBusinessLogic"
import { SessionContext } from "@/lib/repositories/auth/types"

export async function implHandleTryAnswer(
  sessionId: string,
  executionId: string,
  body: { message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const rules = await businessLogic.tryAnswer(
    sessionId,
    executionId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed TRY_ANSWER",
      rules,
    }),
  }
}

export async function implHandleRuleInference(
  sessionId: string,
  executionId: string,
  body: { message: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const rules = await businessLogic.inferRulesForMessage(
    sessionId,
    executionId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed RULE_INFERENCE",
      rules,
    }),
  }
}

export async function implHandleMessageTemplate(
  sessionId: string,
  executionId: string,
  body: { message: string; templateId: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const template = await businessLogic.selectMessageTemplateForSelectedRule(
    sessionId,
    executionId,
    body.templateId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed MESSAGE_TEMPLATE",
      template,
    }),
  }
}

export async function implHandleDatasource(
  sessionId: string,
  executionId: string,
  body: { message: string; templateId: string },
  businessLogic: AiBusinessLogic,
  context: SessionContext,
) {
  const datasource = await businessLogic.supplyWithDatasourceForTemplate(
    sessionId,
    executionId,
    body.templateId,
    body.message,
    context,
  )
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed DATASOURCE",
      datasource,
    }),
  }
}

export async function implHandleReply(
  sessionId: string,
  executionId: string,
  body: { final_message: string },
  businessLogic: AiBusinessLogic,
) {
  // const provider = providers[process.env.WHATSAPP_PROVIDER!]
  // const result = await provider.sendMessage("<EMAIL>", body.final_message, "session_01k0q4azgt2tw5cvnd8s8ky0qk");
  // const context = await businessLogic.buildReplyContext(body.messageId, body.conversationId, body.message);
  return {
    status: 200,
    body: new ResponseWrapper("success", {
      message: "Executed REPLY",
      // context,
      // result
    }),
  }
}
