// export interface IAiRepository {
//   getRulesForMessage(message: string): Promise<Array<{ id: string; content: string }>>;
//   getMessageTemplateForRule(ruleId: string): Promise<Array<{ id: string; content: string }>>;
//   getMessageTemplateById(templateId: string): Promise<{ id: string; content: string, dataSource: string, variables: string[], fields: string[] }>;
//   getRAG(message: string): Promise<Array<any>>;
//   getTemplateForMessage(message: string): Promise<Array<{ id: string; content: string }>>;
// }

export interface IDatasourceRepository {
  getDataWithDataSourceAndVariables(
    dataSource: string,
    variables: string[],
    fieldsToRetrieve: string[],
  ): Promise<Array<Record<string, string>>>
  getReplyContext(messageId: string): Promise<{ replyContext: string }>
  getLastMessages(
    sessionId: string,
    limit: number,
  ): Promise<Array<{ from: string; content: string }>>
}
