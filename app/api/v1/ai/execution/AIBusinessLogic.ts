import { IDatasourceRepository } from "./repository-interface"
import { AIInferenceEngine } from "./AIInferenceEngine"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates"
import { SessionContext } from "@/lib/repositories/auth/types"

export class AiBusinessLogic {
  constructor(
    private runner: AIInferenceEngine,
    private aiRuleBusinessLogic: AiRuleBusinessLogic,
    private messageTemplatesBusinessLogic: MessageTemplateBusinessLogic,
    private datasourceRepository: IDatasourceRepository,
  ) {}

  async tryAnswer(
    sessionId: string,
    executionId: string,
    message: string,
    context: SessionContext,
  ) {
    const previous_messages = await this.datasourceRepository.getLastMessages(
      sessionId,
      10,
    )
    const rules = await this.aiRuleBusinessLogic.queryVectorDB(
      message,
      context,
      3,
    )
    const templates = await this.messageTemplatesBusinessLogic.queryVectorDB(
      message,
      context,
      3,
    )
    const rags = [""]
    await this.runner.execute(sessionId, executionId, "TRY_ANSWER", {
      message,
      previous_messages,
      data: { rules, templates, rags },
    })
    return { rules, templates, rags, previous_messages }
  }

  async inferRulesForMessage(
    sessionId: string,
    executionId: string,
    message: string,
    context: SessionContext,
  ) {
    const rules = await this.aiRuleBusinessLogic.queryVectorDB(
      message,
      context,
      3,
    )
    // enrich rules with information from the db if needed
    await this.runner.execute(sessionId, executionId, "INFER_MESSAGE_RULE", {
      message,
      data: rules,
    })
    return { rules }
  }

  async selectMessageTemplateForSelectedRule(
    sessionId: string,
    executionId: string,
    ruleId: string,
    message: string,
    context: SessionContext,
  ) {
    const templates = await this.messageTemplatesBusinessLogic.queryVectorDB(
      message,
      context,
      3,
    )
    // enrich templates with information from db if needed
    await this.runner.execute(
      sessionId,
      executionId,
      "SELECT_MESSAGE_TEMPLATE",
      { message, data: templates },
    )
    return { templates }
  }

  async supplyWithDatasourceForTemplate(
    sessionId: string,
    executionId: string,
    templateId: string,
    message: string,
    context: SessionContext,
  ) {
    const template = await this.messageTemplatesBusinessLogic.getById(
      templateId,
      context,
    )

    // const data = await this.datasourceRepository.getDataWithDataSourceAndVariables(dataSource, variables, fieldsToRetrieve);
    const data = {}
    await this.runner.execute(
      sessionId,
      executionId,
      "SUPPLY_WITH_DATASOURCE",
      { message, data: { selected_template: template, datasource: data } },
    )
    return {
      template,
      data,
    }
  }

  async buildReplyContext(
    messageId: string,
    conversationId: string,
    message: string,
  ) {
    const replyContext =
      await this.datasourceRepository.getReplyContext(messageId)
    // await this.runner.execute("REPLY", { message, conversationId, replyContext });
    return { replyContext, conversationId }
  }
}
