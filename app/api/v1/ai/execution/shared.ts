import { N8nRunnerService } from "./N8NRunnerService"
import { AiBusinessLogic } from "./AIBusinessLogic"
import { MongoDbRepository } from "./mongodb"
import { driver } from "@/lib/repositories/LiveMongoDriver"
import {
  aiRulesBusinessLogic,
  messageTemplatesBusinessLogic,
} from "@/lib/repositories/businessLogics"

export const n8nRunner = new N8nRunnerService()
const dataSourceRepo = new MongoDbRepository(driver)
export const aiBusinessLogic = new AiBusinessLogic(
  n8nRunner,
  aiRulesBusinessLogic,
  messageTemplatesBusinessLogic,
  dataSourceRepo,
)
