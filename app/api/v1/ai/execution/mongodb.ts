import { MongoDriver } from "@/lib/repositories/MongoDriver"
import { IDatasourceRepository } from "./repository-interface"
import { Collection, Document } from "mongodb"

export class MongoDbRepository implements IDatasourceRepository {
  private messageTable: Collection<Document>

  constructor(private driver: MongoDriver) {
    this.messageTable = driver.getCollection("waha_message_webhook")
  }

  async getDataWithDataSourceAndVariables(
    sourceId: string,
  ): Promise<Array<{ field: string; field2: string; field3: string }>> {
    return [
      {
        field: `value1 for ${sourceId}`,
        field2: `value2 for ${sourceId}`,
        field3: `value3 for ${sourceId}`,
      },
    ]
  }

  async getReplyContext(messageId: string): Promise<{ replyContext: string }> {
    return {
      replyContext: `reply_context_for_${messageId}`,
    }
  }

  async getLastMessages(
    sessionId: string,
    limit: number,
  ): Promise<Array<{ from: string; content: string }>> {
    const messages = await this.messageTable
      .find({ "cs_ai_extras.msg_room_id": sessionId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .project({ "payload.from": 1, "payload.body": 1 })
      .toArray()

    return messages.toReversed().map((msg: any) => ({
      from: msg.payload?.from || "unknown",
      content: msg.payload?.body || "",
    }))
  }
}
