import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { DeviceListSchema } from "@/lib/schemas/devices"
import { providers } from "@/lib/providers"
import { getAuthCookie } from "@/lib/cookies"
import { ERROR_CODES } from "../../error_codes"
import { devicesBusinessLogic } from "@/lib/repositories/businessLogics"
import { SessionContext } from "@/lib/repositories/auth/types"

export async function implHandleGetDevices(
  searchParams: URLSearchParams,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const page = parseInt(searchParams.get("page") || "1")
    const per_page = parseInt(searchParams.get("per_page") || "10")
    const search = searchParams.get("search") || undefined
    const status = searchParams.get("status") || undefined
    const isActive = searchParams.get("isActive") ? searchParams.get("isActive") === "true" : undefined

    const result = await devicesBusinessLogic.getAll({
      offset: page,
      limit: per_page,
      search,
      status: status as any,
      isActive,
      sort: [{ field: "createdAt", direction: "DESC" }],
    }, context)

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        items: result.items,
        total: result.total,
        page,
        per_page
      }),
    }
  } catch (error: any) {
    console.error("Get devices error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch devices. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

export async function implHandleCreateDevice(
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const device = await devicesBusinessLogic.create(body, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", device),
    }
  } catch (error: any) {
    console.error("Create device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create device. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

export async function implHandleUpdateDevice(
  deviceId: string,
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const device = await devicesBusinessLogic.update(deviceId, body, context)

    if (!device) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", device),
    }
  } catch (error: any) {
    console.error("Update device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update device. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

export async function implHandleDeleteDevice(
  deviceId: string,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await devicesBusinessLogic.delete(deviceId, context)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { deleted: true }),
    }
  } catch (error: any) {
    console.error("Delete device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete device. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

export async function implHandleSyncDevices(
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const syncResult = await devicesBusinessLogic.syncWithProvider(context)

    return {
      status: 200,
      body: new ResponseWrapper("success", syncResult),
    }
  } catch (error: any) {
    console.error("Sync devices error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to sync devices. Please try again."],
        [ERROR_CODES.SYNC_FAILED],
      ),
    }
  }
}
