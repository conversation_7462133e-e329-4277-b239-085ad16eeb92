import { NextRequest, NextResponse } from "next/server"
import { implHandleUpdateDevice, implHandleDeleteDevice } from "../impl"
import { devicesBusinessLogic } from "@/lib/repositories/businessLogics"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { id } = await params

    const device = await devicesBusinessLogic.getById(id, context)

    if (!device) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          error: "Device not found",
          errorCodes: ["NOT_FOUND"],
        },
        { status: 404 }
      )
    }

    return NextResponse.json(
      {
        status: "success",
        data: device,
        messages: [],
        errorCodes: [],
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Get device route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const body = await req.json()
    const result = await implHandleUpdateDevice(params.id, body, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Update device route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const result = await implHandleDeleteDevice(params.id, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Delete device route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 }
    )
  }
}
