// File: app/api/whatsapp/qr/route.ts

import { NextRequest, NextResponse } from "next/server"
import { providers } from "@/lib/providers"
import { ResponseWrapper } from "@/lib/services/apiService"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { driver } from "@/lib/repositories/LiveMongoDriver"

export async function POST(req: NextRequest) {
  const { context, response } = await buildSessionContext(req)
  if (response) {
    return response
  }

  const provider = providers[process.env.WHATSAPP_PROVIDER!]

  const result = await provider.createSession()

  const sessionToContextTable = driver.getCollection("session_to_context")
  sessionToContextTable.createIndex
  await sessionToContextTable.insertOne({
    session: result,
    user: context.user,
    organization: context.organization,
  })

  const qr = await provider.getQr(result)

  return NextResponse.json(
    new ResponseWrapper("success", { sessionId: result, qr: qr }, [], []),
    { status: 200 },
  )
}
