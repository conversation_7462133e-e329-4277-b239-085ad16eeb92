import { NextRequest, NextResponse } from "next/server"
import { implHandleGetDevices, implHandleCreateDevice, implHandleSyncDevices } from "./impl"
import { buildSessionContext } from "../../sharedFunction"

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const result = await implHandleGetDevices(searchParams, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Devices route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const body = await req.json()

    // Check if this is a sync request
    if (body.action === "sync") {
      const result = await implHandleSyncDevices(context)
      return NextResponse.json(result.body, { status: result.status })
    }

    // Otherwise, create a new device
    const result = await implHandleCreateDevice(body, context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Devices POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
