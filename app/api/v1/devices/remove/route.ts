// File: app/api/whatsapp/qr/route.ts

import { NextRequest, NextResponse } from "next/server"
import { providers } from "@/lib/providers"
import { ResponseWrapper } from "@/lib/services/apiService"

export async function POST(req: NextRequest) {
  const sessionId = req.nextUrl.searchParams.get("session")!
  const provider = providers[process.env.WHATSAPP_PROVIDER!]

  const result = await provider.logoutSession(sessionId)

  return NextResponse.json(
    new ResponseWrapper("success", { sessionId: result }, [], []),
    { status: 200 },
  )
}
