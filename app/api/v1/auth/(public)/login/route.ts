import { NextRequest, NextResponse } from "next/server"
import { implHandleLogin } from "./impl"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function POST(req: NextRequest) {
  const body = await req.json()
  const result = await implHandleLogin(body, authBusinessLogic)

  if (result.status !== 200) {
    return NextResponse.json(result.body, { status: result.status })
  }

  const accessToken = result.body.data?.token
  const refreshToken = result.body.data?.refresh_token

  const response = NextResponse.json(
    new ResponseWrapper("success", {
      success: true,
    }),
    { status: result.status },
  )

  if (accessToken) {
    response.cookies.set("token", accessToken, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 15 * 60,
    })
  }

  if (refreshToken) {
    response.cookies.set("refresh_token", refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
      path: "/",
      maxAge: 30 * 24 * 60 * 60,
    })
  }

  return response
}
