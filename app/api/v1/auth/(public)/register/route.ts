import { NextRequest, NextResponse } from "next/server"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleRegister } from "./impl"
import { questionAccessManager } from "@/lib/repositories/accessManagers"

export async function POST(req: NextRequest) {
  const body = await req.json()
  const result = await implHandleRegister(body, authBusinessLogic, {
    questions: (groupId) => questionAccessManager(groupId),
  })

  return NextResponse.json(result.body, { status: result.status })
}
