import { NextRequest, NextResponse } from "next/server"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleResendVerification } from "./impl"

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const result = await implHandleResendVerification(body, authBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Resend verification route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
