import { NextRequest, NextResponse } from "next/server"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleDeleteAccount } from "./impl"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function DELETE(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleDeleteAccount(
      body,
      authBusinessLogic,
      context.user.id!,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Delete account route error:", error)
    return NextResponse.json(
      new ResponseWrapper("failed", null, ["Internal server error"]),
      { status: 500 },
    )
  }
}
