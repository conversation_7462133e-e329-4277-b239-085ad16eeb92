import { NextRequest, NextResponse } from "next/server"
import { datasourcesBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetDatasource,
  implHandleUpdateDatasource,
  implHandleDeleteDatasource,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "../../../sharedFunction"

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const result = await implHandleGetDatasource(
      id,
      datasourcesBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Datasource GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const body = await req.json()
    const result = await implHandleUpdateDatasource(
      id,
      body,
      datasourcesBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Datasource PUT route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const result = await implHandleDeleteDatasource(
      id,
      datasourcesBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Datasource DELETE route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
