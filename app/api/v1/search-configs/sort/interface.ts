import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"

export abstract class Sort {
  protected field: string
  protected label: string
  protected defaultDirection: "ASC" | "DESC"

  constructor(
    field: string,
    label: string,
    defaultDirection: "ASC" | "DESC" = "ASC",
  ) {
    this.field = field
    this.label = label
    this.defaultDirection = defaultDirection
  }

  abstract buildForApiResponse(visitor: SearchConfigApiResponseVisitor): void

  parseFromQuery(
    query: URLSearchParams,
  ): { field: string; direction: "ASC" | "DESC" } | null {
    const value = query.get("sort." + this.field)
    if (value) {
      const direction = value.toLowerCase() === "DESC" ? "DESC" : "ASC"
      return { field: this.field, direction }
    }
    return null
  }
}
