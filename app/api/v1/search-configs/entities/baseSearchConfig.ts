import { SearchFilter, QueryValueHolder } from "../filters/base"
import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { Sort } from "../sort/interface"
import { MongoQueryBuilder } from "./mongoQueryBuilder"

export class BaseSearchConfig {
  constructor(
    protected filters: SearchFilter[],
    protected sort: Sort[],
    public searchableFields: string[],
    protected dateFilters: any[],
  ) {
    this.filters = filters
    this.sort = sort
    this.searchableFields = searchableFields
    this.dateFilters = dateFilters
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    for (const filter of this.filters) {
      filter.buildForApiResponse(visitor)
    }

    for (const sort of this.sort) {
      sort.buildForApiResponse(visitor)
    }

    for (const dateFilter of this.dateFilters) {
      dateFilter.buildForApiResponse(visitor)
    }
  }

  buildMongoQuery(params: {
    sort: { field: string; direction: "ASC" | "DESC" }[]
    filters: { field: string; value: QueryValueHolder | any }[]
  }): { query: any; sort: any } {
    const mongoQueryBuilder = new MongoQueryBuilder()

    for (const filter of params.filters) {
      const value = filter.value

      if (typeof value === "object" && "visit" in value) {
        value.visit(mongoQueryBuilder)
      } else {
        mongoQueryBuilder.addStringFilter(
          filter.field,
          value?.toString?.() || "",
        )
      }
    }

    const query = mongoQueryBuilder.getQuery()
    const sort: Record<string, 1 | -1> = {}

    for (const s of params.sort) {
      sort[s.field] = s.direction === "ASC" ? 1 : -1
    }

    return { query, sort }
  }

  parseParams(searchParams: URLSearchParams): {
    result?: {
      sort: { field: string; direction: "ASC" | "DESC" }[]
      filters: { field: string; value: QueryValueHolder }[]
    }
    errors?: string[]
  } {
    try {
      const params: {
        sort: { field: string; direction: "ASC" | "DESC" }[]
        filters: { field: string; value: QueryValueHolder }[]
      } = {
        sort: [],
        filters: [],
      }

      for (const filter of this.filters) {
        const parsed = filter.parseFromQuery(searchParams)
        if (parsed) {
          params.filters.push(parsed)
        }
      }

      for (const dateFilter of this.dateFilters) {
        const parsed = dateFilter.parseFromQuery(searchParams)
        if (parsed) {
          params.filters.push(parsed)
        }
      }

      for (const sort of this.sort) {
        const parsed = sort.parseFromQuery(searchParams)
        if (parsed) {
          params.sort = params.sort || []
          params.sort.push(parsed)
        }
      }

      return { result: params }
    } catch (error: any) {
      return { errors: [error.message] }
    }
  }
}
