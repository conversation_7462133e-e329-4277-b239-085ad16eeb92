import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { StringSort } from "../sort/StringSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class CustomerProfilesSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity {
  constructor() {
    const filters = [
      new StringSearch("firstName", "First Name"),
      new StringSearch("lastName", "Last Name"),
      new StringSearch("email", "Email"),
      new StringSearch("phone", "Phone"),
      new StringSearch("company", "Company"),
      new StringSearch("jobTitle", "Job Title"),

      new MultipleSelectSearch("loyaltyLevel", "Loyalty Level", [
        { value: "bronze", label: "Bronze" },
        { value: "silver", label: "Silver" },
        { value: "gold", label: "Gold" },
        { value: "platinum", label: "Platinum" },
      ]),

      new BooleanSearch("isActive", "Is Active"),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["firstName", "lastName", "email", "phone", "company", "jobTitle"]

    const sort = [
      new StringSort("firstName", "First Name"),
      new StringSort("lastName", "Last Name"),
      new StringSort("email", "Email"),
      new StringSort("company", "Company"),
      new StringSort("totalSpent", "Total Spent"),
      new StringSort("totalOrders", "Total Orders"),
      new StringSort("lastContactDate", "Last Contact Date"),
      new StringSort("createdAt", "Created Date"),
      new StringSort("updatedAt", "Updated Date"),
    ]

    super(filters, sort, searchableFields, dateFilters)
  }
}

export const customerProfilesSearchConfig = new CustomerProfilesSearchConfig()
