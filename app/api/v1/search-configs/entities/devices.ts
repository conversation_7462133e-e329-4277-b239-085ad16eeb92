import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { StringSort } from "../sort/StringSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class DevicesSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity {
  constructor() {
    const filters = [
      new StringSearch("name", "Device Name"),
      new StringSearch("sessionId", "Session ID"),
      new StringSearch("platform", "Platform"),
      new StringSearch("providerName", "Provider Name"),
      new StringSearch("me.pushName", "Push Name"),

      new MultipleSelectSearch("status", "Status", [
        { value: "CONNECTED", label: "Connected" },
        { value: "DISCONNECTED", label: "Disconnected" },
        { value: "SCANNING", label: "Scanning" },
        { value: "FAILED", label: "Failed" },
        { value: "STARTING", label: "Starting" },
        { value: "STOPPING", label: "Stopping" },
      ]),

      new BooleanSearch("isActive", "Is Active"),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["name", "sessionId", "platform", "providerName", "me.pushName"]

    const sort = [
      new StringSort("name", "Device Name"),
      new StringSort("sessionId", "Session ID"),
      new StringSort("platform", "Platform"),
      new StringSort("status", "Status"),
      new StringSort("linkedAt", "Linked Date"),
      new StringSort("lastSeenAt", "Last Seen"),
      new StringSort("createdAt", "Created Date"),
      new StringSort("updatedAt", "Updated Date"),
    ]

    super(filters, sort, searchableFields, dateFilters)
  }
}

export const devicesSearchConfig = new DevicesSearchConfig()
