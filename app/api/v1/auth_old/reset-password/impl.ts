import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { ResetPasswordSchema } from "@/lib/schemas/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleResetPassword(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = ResetPasswordSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
        ),
      }
    }

    const { token, newPassword } = validation.data

    // Reset password
    const result = await authBusinessLogic.resetPassword(token, newPassword)

    if (!result.success) {
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, [result.message]),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: result.message }),
    }
  } catch (error) {
    console.error("Reset password error:", error)
    return {
      status: 500,
      body: new ResponseWrapper("failed", null, ["Internal server error"]),
    }
  }
}
