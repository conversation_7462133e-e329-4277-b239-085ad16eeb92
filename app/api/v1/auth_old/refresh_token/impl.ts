import { AuthBusinessLogicInterface } from "@/lib/repositories/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { RefreshTokenSchema } from "@/lib/schemas/auth"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleRefreshToken(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validationResult = RefreshTokenSchema.safeParse(body)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { token, refresh_token } = validationResult.data

    // Attempt token refresh
    const result = await authBusinessLogic.refreshToken({
      token,
      refresh_token,
    })

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Token refresh error:", error)

    if (error.code === "INVALID_TOKEN") {
      return {
        status: 401,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invalid or expired refresh token"],
          [ERROR_CODES.INVALID_TOKEN],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Token refresh failed. Please login again."],
        [ERROR_CODES.REFRESH_FAILED],
      ),
    }
  }
}
