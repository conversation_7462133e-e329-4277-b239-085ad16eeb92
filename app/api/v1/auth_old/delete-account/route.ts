import { NextRequest, NextResponse } from "next/server"
import { authBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleDeleteAccount } from "./impl"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function DELETE(req: NextRequest) {
  try {
    // Extract user ID from token
    const authHeader = req.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        new ResponseWrapper("failed", null, ["Authorization token required"]),
        { status: 401 },
      )
    }

    const token = authHeader.substring(7)
    const validateResult = await authBusinessLogic.validateToken(token)
    if (!validateResult.user) {
      return NextResponse.json(
        new ResponseWrapper("failed", validateResult.error, [
          ERROR_CODES.INVALID_TOKEN,
        ]),
        { status: 401 },
      )
    }

    const body = await req.json()
    const result = await implHandleDeleteAccount(
      body,
      authBusinessLogic,
      validateResult.user.id!,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Delete account route error:", error)
    return NextResponse.json(
      new ResponseWrapper("failed", null, ["Internal server error"]),
      { status: 500 },
    )
  }
}
