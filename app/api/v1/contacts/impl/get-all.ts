import {
  ContactBusinessLogicInterface,
  Contact,
} from "@/lib/repositories/contacts/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { GetAllResultPaginated, ContactQueryParams } from "./types"
import {
  ContactResponses,
  SearchResponses,
  createSuccessResponse,
} from "@/lib/utils/api-response-helper"
import { SessionContext } from "@/lib/repositories/auth/types"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { ContactAccessResource } from "./access_manager_resource"

// Get All Contacts Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllContacts(
  businessLogic: ContactBusinessLogicInterface,
  params: ContactQueryParams,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Contact>>
}> {
  const isAuthorized = await accessManager?.isAllowed({
    resource: "GET /contacts",
    action: "read",
    userId: context?.user.id ?? "",
  })

  if (!isAuthorized) {
    return ContactResponses.unauthorizedRole()
  }

  try {
    // Validate search paramseter if provided
    if (params.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return SearchResponses.emptyKeyword()
      }
    }

    // Validate filters if provided
    if (params.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return SearchResponses.emptyFilterField()
        }
      }
    }

    // Validate sort if provided
    if (params.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return SearchResponses.emptySortField()
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return SearchResponses.invalidSortDirection()
        }
      }
    }

    // Build query paramseters for the repository
    const queryParams: any = {
      includeDeleted: params.includeDeleted,
      page: params.page,
      limit: params.limit,
    }

    // Add search if provided
    if (params.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return createSuccessResponse<GetAllResultPaginated<Contact>>({
      ...result,
      page: queryParams.page,
    })
  } catch (error: any) {
    console.error("Get contacts error:", error)

    return ContactResponses.fetchFailed()
  }
}
