import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { BulkDeleteRequest, BulkOperationResult } from "./types"
import { ContactResponses } from "@/lib/utils/api-response-helper"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ContactAccessResource } from "./access_manager_resource"

// Bulk delete implementation
export async function implBulkDeleteContacts(
  request: BulkDeleteRequest,
  businessLogic: ContactBusinessLogicInterface,
  hardDelete: boolean = false,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<BulkOperationResult>
}> {
  const isAuthorized = await accessManager?.isAllowed({
    resource: "DELETE /bulk",
    action: "delete",
    userId: context?.user.id ?? "",
  })

  if (!isAuthorized) {
    return ContactResponses.unauthorizedRole()
  }

  try {
    const { ids } = request

    if (!Array.isArray(ids)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid IDs format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    if (ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No IDs provided for deletion"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const results: BulkOperationResult = {
      total: ids.length,
      successful: 0,
      failed: 0,
      errors: [],
    }

    // Perform bulk delete operation
    try {
      const deletedCount = await businessLogic.bulkDelete(ids, context, false)
      results.successful = deletedCount

      // If some deletes failed (deletedCount < ids.length)
      if (deletedCount < ids.length) {
        const failedCount = ids.length - deletedCount
        results.failed = failedCount
        results.errors.push({
          id: "bulk",
          message: `${failedCount} contacts could not be deleted (may not exist or already deleted)`,
        })
      }
    } catch (error: any) {
      // If bulk operation fails, mark all as failed
      results.failed = ids.length
      results.successful = 0
      results.errors = [
        {
          id: "bulk",
          message:
            error instanceof Error
              ? error.message
              : "Bulk delete operation failed",
        },
      ]
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    }
  } catch (error: any) {
    console.error("Bulk delete contacts error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}
