import { contactsBusinessLogic } from "@/lib/repositories/businessLogics"
import { NextRequest, NextResponse } from "next/server"
import {
  implHandleGetContact,
  implHandleUpdateContact,
  implHandleDeleteContact,
} from "../impl"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { contactAccessManager } from "@/lib/repositories/accessManagers"

export async function GET(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { id } = await ctx.params
    const result = await implHandleGetContact(
      id,
      contactsBusinessLogic,
      context,
      contactAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contact GET route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await ctx.params
    const body = await req.json()
    const result = await implHandleUpdateContact(
      id,
      body,
      contactsBusinessLogic,
      context,
      contactAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contact PUT route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await ctx.params
    const result = await implHandleDeleteContact(
      id,
      contactsBusinessLogic,
      false,
      context,
      contactAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contact DELETE route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
