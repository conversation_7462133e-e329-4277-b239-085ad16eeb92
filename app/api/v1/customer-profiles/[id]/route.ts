import { NextRequest, NextResponse } from "next/server"
import { customerProfilesBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleGetCustomerProfile } from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "../../../sharedFunction"

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const { searchParams } = new URL(req.url)
    const includeDeleted = searchParams.get("includeDeleted") === "true"

    const result = await implHandleGetCustomerProfile(
      id,
      customerProfilesBusinessLogic,
      sessionContext,
      includeDeleted,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Customer profile GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
