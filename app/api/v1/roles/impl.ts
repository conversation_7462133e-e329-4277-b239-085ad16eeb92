import {
  RoleBusinessLogicInterface,
  Role,
  RoleCreateInput,
  RoleUpdateInput,
} from "@/lib/repositories/roles/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { RoleCreateSchema, RoleUpdateSchema } from "@/lib/validations/role"
import { ERROR_CODES } from "@/app/api/error_codes"

// Create Role Implementation
export async function implHandleCreateRole(
  data: any,
  businessLogic: RoleBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = RoleCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const role = await businessLogic.create(validationResult.data)

    return {
      status: 201,
      body: new ResponseWrapper("success", role),
    }
  } catch (error: any) {
    console.error("Create role error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create role. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update Role Implementation
export async function implHandleUpdateRole(
  id: string,
  data: any,
  businessLogic: RoleBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = RoleUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const role = await businessLogic.update(id, validationResult.data)

    if (!role) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Role not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", role),
    }
  } catch (error: any) {
    console.error("Update role error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update role. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete Role Implementation
export async function implHandleDeleteRole(
  id: string,
  businessLogic: RoleBusinessLogicInterface,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Role not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Role deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete role error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete role. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get Role by ID Implementation
export async function implHandleGetRole(
  id: string,
  businessLogic: RoleBusinessLogicInterface,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const role = await businessLogic.getById(id, includeDeleted)

    if (!role) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Role not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", role),
    }
  } catch (error: any) {
    console.error("Get role error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch role. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Get All Role Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllRoles(
  businessLogic: RoleBusinessLogicInterface,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof Role | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof Role | string
      value: Role[keyof Role] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Role>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Role>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Role>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Role>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Role>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Role>>("success", {
        ...result,
        page: queryParams.page,
      }),
    }
  } catch (error: any) {
    console.error("Get role error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Role>>(
        "failed",
        undefined,
        ["Failed to fetch role. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Bulk Create Role Implementation
export async function implHandleBulkCreateRole(
  data: any[],
  businessLogic: RoleBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each item in the array
    const validatedData: RoleCreateInput[] = []
    for (let i = 0; i < data.length; i++) {
      const validationResult = RoleCreateSchema.safeParse(data[i])
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Item ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }
      validatedData.push(validationResult.data)
    }

    const role = await businessLogic.bulkCreate(validatedData)

    return {
      status: 201,
      body: new ResponseWrapper("success", role),
    }
  } catch (error: any) {
    console.error("Bulk create role error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create role. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Bulk Update Role Implementation
export async function implHandleBulkUpdateRole(
  updates: { id: string; data: any }[],
  businessLogic: RoleBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: RoleUpdateInput }[] = []
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i]

      if (!update.id || typeof update.id !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }

      const validationResult = RoleUpdateSchema.safeParse(update.data)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Update ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data,
      })
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates)

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    }
  } catch (error: any) {
    console.error("Bulk update role error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update role. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Bulk Delete Role Implementation
export async function implHandleBulkDeleteRole(
  ids: string[],
  businessLogic: RoleBusinessLogicInterface,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i]
      if (!id || typeof id !== "string" || id.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete)

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    }
  } catch (error: any) {
    console.error("Bulk delete role error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete role. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Restore Role Implementation
export async function implHandleRestoreRole(
  id: string,
  businessLogic: RoleBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Role ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const restored = await businessLogic.restore(id)

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Role not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    }
  } catch (error: any) {
    console.error("Restore role error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore role. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}
