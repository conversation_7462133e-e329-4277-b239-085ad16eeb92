import { NextRequest, NextResponse } from "next/server"
import { rolesBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetRole,
  implHandleUpdateRole,
  implHandleDeleteRole,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await context.params
    const result = await implHandleGetRole(id, rolesBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("role GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await context.params
    const body = await req.json()
    const result = await implHandleUpdateRole(id, body, rolesBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("role PUT route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await context.params
    const result = await implHandleDeleteRole(id, rolesBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("role DELETE route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
