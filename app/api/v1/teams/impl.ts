import {
  TeamBusinessLogicInterface,
  Team,
  TeamCreateInput,
  TeamUpdateInput,
} from "@/lib/repositories/teams/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { TeamCreateSchema, TeamUpdateSchema } from "@/lib/validations/team"
import { ERROR_CODES } from "@/app/api/error_codes"

// Create Team Implementation
export async function implHandleCreateTeam(
  data: any,
  businessLogic: TeamBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = TeamCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const team = await businessLogic.create(validationResult.data)

    return {
      status: 201,
      body: new ResponseWrapper("success", team),
    }
  } catch (error: any) {
    console.error("Create team error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create team. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update Team Implementation
export async function implHandleUpdateTeam(
  id: string,
  data: any,
  businessLogic: TeamBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = TeamUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const team = await businessLogic.update(id, validationResult.data)

    if (!team) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Team not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", team),
    }
  } catch (error: any) {
    console.error("Update team error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update team. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete Team Implementation
export async function implHandleDeleteTeam(
  id: string,
  businessLogic: TeamBusinessLogicInterface,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Team not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Team deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete team error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete team. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get Team by ID Implementation
export async function implHandleGetTeam(
  id: string,
  businessLogic: TeamBusinessLogicInterface,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const team = await businessLogic.getById(id, includeDeleted)

    if (!team) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Team not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", team),
    }
  } catch (error: any) {
    console.error("Get team error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch team. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Get All Team Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllTeams(
  businessLogic: TeamBusinessLogicInterface,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof Team | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof Team | string
      value: Team[keyof Team] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Team>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Team>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Team>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Team>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Team>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Team>>("success", {
        ...result,
        page: queryParams.page,
      }),
    }
  } catch (error: any) {
    console.error("Get team error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Team>>(
        "failed",
        undefined,
        ["Failed to fetch team. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Bulk Create Team Implementation
export async function implHandleBulkCreateTeam(
  data: any[],
  businessLogic: TeamBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each item in the array
    const validatedData: TeamCreateInput[] = []
    for (let i = 0; i < data.length; i++) {
      const validationResult = TeamCreateSchema.safeParse(data[i])
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Item ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }
      validatedData.push(validationResult.data)
    }

    const team = await businessLogic.bulkCreate(validatedData)

    return {
      status: 201,
      body: new ResponseWrapper("success", team),
    }
  } catch (error: any) {
    console.error("Bulk create team error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create team. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Bulk Update Team Implementation
export async function implHandleBulkUpdateTeam(
  updates: { id: string; data: any }[],
  businessLogic: TeamBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: TeamUpdateInput }[] = []
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i]

      if (!update.id || typeof update.id !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }

      const validationResult = TeamUpdateSchema.safeParse(update.data)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Update ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data,
      })
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates)

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    }
  } catch (error: any) {
    console.error("Bulk update team error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update team. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Bulk Delete Team Implementation
export async function implHandleBulkDeleteTeam(
  ids: string[],
  businessLogic: TeamBusinessLogicInterface,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i]
      if (!id || typeof id !== "string" || id.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete)

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    }
  } catch (error: any) {
    console.error("Bulk delete team error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete team. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Restore Team Implementation
export async function implHandleRestoreTeam(
  id: string,
  businessLogic: TeamBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Team ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const restored = await businessLogic.restore(id)

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Team not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    }
  } catch (error: any) {
    console.error("Restore team error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore team. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}
