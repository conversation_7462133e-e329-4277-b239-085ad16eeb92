import { NextRequest, NextResponse } from "next/server"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleGetKnowledgeBaseStats } from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/knowledge-base/stats - Get knowledge base statistics
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const result = await implHandleGetKnowledgeBaseStats(
      knowledgeBaseBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/knowledge-base/stats:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
