import { NextRequest, NextResponse } from "next/server"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllKnowledgeBaseDocuments,
  implHandleCreateKnowledgeBaseDocument,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/knowledge-base/documents - Get all knowledge base documents
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    // Parse query parameters
    const params = {
      search: searchParams.get("search") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      includeDeleted: searchParams.get("includeDeleted") === "true",
      isProcessed: searchParams.get("isProcessed")
        ? searchParams.get("isProcessed") === "true"
        : undefined,
      isActive: searchParams.get("isActive")
        ? searchParams.get("isActive") === "true"
        : undefined,
      mimeType: searchParams.get("mimeType") || undefined,
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "DESC",
    }

    const result = await implHandleGetAllKnowledgeBaseDocuments(
      knowledgeBaseBusinessLogic,
      context,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/knowledge-base/documents:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// POST /api/v1/knowledge-base/documents - Create knowledge base document
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateKnowledgeBaseDocument(
      body,
      knowledgeBaseBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/knowledge-base/documents:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
