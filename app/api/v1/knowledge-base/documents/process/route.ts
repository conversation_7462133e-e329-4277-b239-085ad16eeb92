import { NextRequest, NextResponse } from "next/server"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleProcessKnowledgeBaseDocument } from "../../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// POST /api/v1/knowledge-base/documents/process - Process a knowledge base document
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleProcessKnowledgeBaseDocument(
      body,
      knowledgeBaseBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error(
      "Error in POST /api/v1/knowledge-base/documents/process:",
      error,
    )
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
