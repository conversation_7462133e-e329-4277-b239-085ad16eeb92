import { NextRequest, NextResponse } from "next/server"
import { broadcastBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleStartBroadcast } from "../../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// POST /api/v1/broadcast/[id]/start - Start a broadcast
export async function POST(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleStartBroadcast(
      id,
      broadcastBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/broadcast/[id]/start:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
