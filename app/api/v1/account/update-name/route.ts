import { NextRequest, NextResponse } from "next/server"
import { usersBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleUpdateAccountName } from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// PUT /api/v1/account/update-name - Update account name
export async function PUT(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleUpdateAccountName(
      body,
      usersBusinessLogic,
      context,
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in PUT /api/v1/account/update-name:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
