import { NextRequest, NextResponse } from "next/server"
import { accountBusinessLogic } from "@/lib/repositories/businessLogics"
import { implHandleUpdateAccountName, implHandleGetAccount } from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/account - Get account information
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const result = await implHandleGetAccount(accountBusinessLogic, context)

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/account:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// PUT /api/v1/account - Update account information
export async function PUT(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleUpdateAccountName(
      body,
      accountBusinessLogic,
      context,
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in PUT /api/v1/account:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
