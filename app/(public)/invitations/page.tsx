"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON>ertTitle, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { CollaborationAPI } from "@/lib/services/collaborationApi"
import { useLocalization } from "@/localization/functions/client"

const invitationLocales = {
  loading: "Loading invitation...",
  accept_loading: "Accepting invitation...",
  decline_loading: "Declining invitation...",
  success_accept: "Invitation accepted! Redirecting...",
  success_decline: "Invitation declined.",
  error_title: "Error",
  error_missing_token: "Missing invitation token.",
  error_accept: "Failed to accept invitation.",
  error_decline: "Failed to decline invitation.",
  unknown_error: "Something went wrong.",
  go_home: "Go to Home",
  accept: "Accept Invitation",
  decline: "Decline Invitation",
  invitation_message:
    "{{inviter}} invited you to join {{organization}} as {{role}}.",
}

export default function InvitationPage() {
  const { t } = useLocalization("invitation", { en: invitationLocales })
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")

  const [loading, setLoading] = useState(true)
  const [status, setStatus] = useState<
    "idle" | "accepted" | "declined" | "error"
  >("idle")
  const [errorMessage, setErrorMessage] = useState("")

  const [inviterName, setInviterName] = useState("")
  const [organizationName, setOrganizationName] = useState("")
  const [role, setRole] = useState("")

  useEffect(() => {
    if (!token) {
      setErrorMessage(t("error_missing_token"))
      setStatus("error")
      setLoading(false)
      return
    }

    const fetchInvitation = async () => {
      try {
        const res = await CollaborationAPI.GetInvitationInfo(token).request()
        setInviterName(res.inviterName)
        setOrganizationName(res.organizationName)
        setRole(res.role)
        setLoading(false)
      } catch (err: any) {
        setErrorMessage(err?.message || t("unknown_error"))
        setStatus("error")
        setLoading(false)
      }
    }

    fetchInvitation()
  }, [token, t])

  const handleAccept = async () => {
    if (!token) return
    setLoading(true)
    try {
      const res = await CollaborationAPI.AcceptInvitation(token).request()
      if (res.success) {
        setStatus("accepted")
        setTimeout(() => router.push("/dashboard"), 2000)
      } else {
        setErrorMessage(t("error_accept"))
        setStatus("error")
      }
    } catch (err: any) {
      setErrorMessage(err?.message || t("unknown_error"))
      setStatus("error")
    } finally {
      setLoading(false)
    }
  }

  const handleDecline = async () => {
    if (!token) return
    setLoading(true)
    try {
      const res = await CollaborationAPI.DeclineInvitation(token).request()
      if (res.success) {
        setStatus("declined")
      } else {
        setErrorMessage(t("error_decline"))
        setStatus("error")
      }
    } catch (err: any) {
      setErrorMessage(err?.message || t("unknown_error"))
      setStatus("error")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-lg mx-auto mt-24 px-4 space-y-4">
      {loading && (
        <div className="flex justify-center items-center gap-2">
          <Loader2 className="animate-spin w-5 h-5" />
          <span>{t("loading")}</span>
        </div>
      )}

      {!loading && status === "idle" && (
        <>
          <Alert variant="default">
            <AlertTitle>
              {t("invitation_message")
                .replace("{{inviter}}", inviterName)
                .replace("{{organization}}", organizationName)
                .replace("{{role}}", role)}
            </AlertTitle>
          </Alert>

          <div className="flex justify-center gap-4">
            <Button onClick={handleAccept}>{t("accept")}</Button>
            <Button variant="outline" onClick={handleDecline}>
              {t("decline")}
            </Button>
          </div>
        </>
      )}

      {!loading && status === "accepted" && (
        <Alert variant="default">
          <AlertTitle>{t("success_accept")}</AlertTitle>
        </Alert>
      )}

      {!loading && status === "declined" && (
        <Alert variant="default">
          <AlertTitle>{t("success_decline")}</AlertTitle>
        </Alert>
      )}

      {!loading && status === "error" && (
        <Alert variant="destructive">
          <AlertTitle>{t("error_title")}</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {status !== "idle" && (
        <div className="flex justify-center mt-4">
          <Button onClick={() => router.push("/")}>{t("go_home")}</Button>
        </div>
      )}
    </div>
  )
}
