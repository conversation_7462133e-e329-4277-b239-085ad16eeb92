"use client"

import { useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"

import { useLocalization } from "@/localization/functions/client"
import { authLocales } from "../locales"
import { AuthAPI } from "@/lib/services/authApi"
import { secureStorage, StorageKeys } from "@/lib/utils/SecureStorage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"

export default function LoginPage() {
  const { t } = useLocalization("auth", authLocales)
  const searchParams = useSearchParams()

  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      const { success } = await AuthAPI.Login({
        email,
        password,
      }).request()

      if (!success) {
        setError(t("auth.login_error"))
        setLoading(false)
        return
      }
      secureStorage.setItem(StorageKeys.IsLoggedIn, "true")
      const redirectTo = searchParams.get("redirectTo")
      window.location.href = redirectTo || "/dashboard"
    } catch (err: any) {
      setError(err?.message || t("auth.login_error"))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20" data-testid="login-page">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.login")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleLogin}
            className="space-y-4"
            data-testid="login-form"
          >
            <div className="space-y-2">
              <Label htmlFor="email">{t("auth.email")}</Label>
              <Input
                id="email"
                name="email"
                data-testid="email-input"
                placeholder={t("auth.email")}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{t("auth.password")}</Label>
              <Input
                id="password"
                name="password"
                data-testid="password-input"
                placeholder={t("auth.password")}
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <Button
              type="submit"
              data-testid="login-button"
              className="w-full"
              disabled={loading}
            >
              {loading
                ? t("auth.logging_in") || "Logging in..."
                : t("auth.login")}
            </Button>
            {error && (
              <p
                className="text-sm text-destructive"
                data-testid="error-message"
              >
                {error}
              </p>
            )}
          </form>
        </CardContent>
        <CardFooter>
          <div className="flex justify-between items-center w-full text-sm">
            <Link
              href="/auth/register"
              className="text-primary hover:underline"
              data-testid="register-link"
            >
              {t("auth.register")}
            </Link>
            <Link
              href="/auth/forgot-password"
              className="text-primary hover:underline"
              data-testid="forgot-password-link"
            >
              {t("auth.forgot_password")}
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
