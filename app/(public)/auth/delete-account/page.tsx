"use client"

import { useLocalization } from "@/localization/functions/client"
import { AuthAPI } from "@/lib/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function DeleteAccountPage() {
  const { t } = useLocalization("auth", authLocales)
  const router = useRouter()
  const [password, setPassword] = useState("")
  const [message, setMessage] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await AuthAPI.DeleteAccount({ password }).request()
      localStorage.clear()
      setMessage(t("auth.deleted_success"))
      setTimeout(() => router.push("/auth/register"), 1500)
    } catch (err: any) {
      setMessage(err.message || t("auth.deleted_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-destructive">
            {t("auth.delete_account")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">{t("auth.confirm_password")}</Label>
              <Input
                id="password"
                type="password"
                placeholder={t("auth.confirm_password")}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <Button type="submit" variant="destructive" className="w-full">
              {t("auth.delete")}
            </Button>
            {message && (
              <p className="text-sm text-muted-foreground">{message}</p>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
