"use client"

import { useLocalization } from "@/localization/functions/client"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { authLocales } from "../locales"
import { AuthAPI } from "@/lib/services/authApi"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface VerifyEmailPageProps {
  searchParams: {
    token?: string
  }
}

export default function VerifyEmailPage({
  searchParams,
}: VerifyEmailPageProps) {
  const { t } = useLocalization("auth", authLocales)
  const router = useRouter()
  const token = searchParams.token || ""

  const [email, setEmail] = useState("")
  const [message, setMessage] = useState("")
  const [error, setError] = useState("")
  const [verified, setVerified] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!token) return

    const verify = async () => {
      setLoading(true)
      setError("")
      setMessage("")

      try {
        await AuthAPI.VerifyEmail({ token }).request()
        setVerified(true)
        setMessage(t("auth.verification_success"))
      } catch (err: any) {
        setError(err?.message || t("auth.verification_failed"))
      } finally {
        setLoading(false)
      }
    }

    verify()
  }, [])

  const handleResend = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage("")
    setError("")

    try {
      await AuthAPI.ResendVerification({ email }).request()
      setMessage(t("auth.verification_sent"))
    } catch (err: any) {
      setError(err?.message || t("auth.verification_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.verify_email")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading && (
            <p className="text-sm text-muted-foreground">
              {t("auth.verifying")}
            </p>
          )}

          {!loading && verified && (
            <>
              <p className="text-green-600">{message}</p>
              <Button
                onClick={() => router.push("/auth/login")}
                className="w-full"
              >
                {t("auth.login")}
              </Button>
            </>
          )}

          {!loading && !verified && (
            <>
              {error && <p className="text-destructive">{error}</p>}

              <form onSubmit={handleResend} className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {t("auth.verification_description")}
                </p>
                <div className="space-y-2">
                  <Label htmlFor="email">{t("auth.email")}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("auth.email")}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">
                  {t("auth.resend_verification")}
                </Button>
              </form>

              {message && <p className="text-green-600">{message}</p>}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
