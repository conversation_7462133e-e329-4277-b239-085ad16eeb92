"use client"

import { useLocalization } from "@/localization/functions/client"
import { useState } from "react"
import { authLocales } from "../locales"
import { AuthAPI } from "@/lib/services/authApi"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ChangePasswordPage() {
  const { t } = useLocalization("auth", authLocales)
  const [oldPassword, setOldPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [message, setMessage] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await AuthAPI.ChangePassword({
        currentPassword: oldPassword,
        newPassword,
      }).request()
      setMessage(t("auth.change_success"))
    } catch (err: any) {
      setMessage(err.message || t("auth.change_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.change_password")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="old-password">{t("auth.old_password")}</Label>
              <Input
                id="old-password"
                type="password"
                placeholder={t("auth.old_password")}
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">{t("auth.new_password")}</Label>
              <Input
                id="new-password"
                type="password"
                placeholder={t("auth.new_password")}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <Button type="submit" className="w-full">
              {t("auth.change")}
            </Button>
            {message && (
              <p className="text-sm text-muted-foreground">{message}</p>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
