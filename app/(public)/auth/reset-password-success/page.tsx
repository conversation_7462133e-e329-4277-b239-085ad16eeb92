"use client"

import Link from "next/link"
import { useLocalization } from "@/localization/functions/client"
import { authLocales } from "../locales"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ResetPasswordSuccessPage() {
  const { t } = useLocalization("auth", authLocales)

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-semibold text-green-600">
            {t("auth.reset_success_title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-muted-foreground">
            {t("auth.reset_success_message")}
          </p>
          <Link href="/auth/login">
            <Button className="w-full">{t("auth.go_to_login")}</Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  )
}
