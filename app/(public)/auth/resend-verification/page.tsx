"use client"

import { useLocalization } from "@/localization/functions/client"
import { AuthAPI } from "@/lib/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ResendVerificationPage() {
  const { t } = useLocalization("auth", authLocales)
  const [email, setEmail] = useState("")
  const [message, setMessage] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await AuthAPI.ResendVerification({ email }).request()
      setMessage(t("auth.verification_sent"))
    } catch (err: any) {
      setMessage(err.message || t("auth.verification_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.resend_verification")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">{t("auth.email")}</Label>
              <Input
                id="email"
                type="email"
                placeholder={t("auth.email")}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <Button type="submit" className="w-full">
              {t("auth.send")}
            </Button>
            {message && (
              <p className="text-sm text-muted-foreground">{message}</p>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
