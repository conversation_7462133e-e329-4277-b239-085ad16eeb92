"use client"

import { use<PERSON>out<PERSON> } from "next/navigation"
import { useLocalization } from "@/localization/functions/client"
import { AuthAPI } from "@/lib/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"

export default function ResetPasswordPageClient({
  params,
}: {
  params: { token: string }
}) {
  const { t } = useLocalization("auth", authLocales)
  const token = params.token
  const router = useRouter()

  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState("")

  const validate = () => {
    if (!password || !confirmPassword) {
      return t("auth.password_required")
    }
    if (password.length < 8) {
      return t("auth.password_too_short")
    }
    if (password !== confirmPassword) {
      return t("auth.password_mismatch")
    }
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const validationError = validate()
    if (validationError) {
      setError(validationError)
      return
    }

    setError("")
    try {
      await AuthAPI.ResetPassword({ token, newPassword: password }).request()
      router.push("/auth/reset-password-success")
    } catch (err: any) {
      setError(err.message || t("auth.reset_error"))
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.reset_password")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">{t("auth.new_password")}</Label>
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder={t("auth.new_password")}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-password"
                  checked={showPassword}
                  onCheckedChange={(checked) =>
                    setShowPassword(checked === true)
                  }
                />
                <Label htmlFor="show-password" className="text-sm">
                  {t("auth.show_password")}
                </Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-password">
                {t("auth.confirm_password")}
              </Label>
              <Input
                id="confirm-password"
                type="password"
                placeholder={t("auth.confirm_password")}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>

            {error && <p className="text-sm text-destructive">{error}</p>}

            <Button type="submit" className="w-full">
              {t("auth.reset")}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
