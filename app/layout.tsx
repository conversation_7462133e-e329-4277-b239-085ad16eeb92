import "./globals.css"
import type { Metadata } from "next"
import { Suspense } from "react"
import { cookies } from "next/headers"

import { Toaster } from "@/components/ui/toaster"
import { Toaster as ToasterSonner } from "@/components/ui/sonner"
import { LocalizationProvider } from "@/localization/functions/localization-context"
import { LoadingIndicator } from "@/components/loading-indicator"

export const metadata: Metadata = {
  title: "CS CRM",
  description: "A CRM with AI capabilities",
}

const kDEFAULT_LANG = "id"

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = await cookies()
  const localeFromCookie = cookieStore.get("locale")?.value || kDEFAULT_LANG

  return (
    <html lang={localeFromCookie}>
      <body>
        <Suspense fallback={<LoadingIndicator />}>
          <LocalizationProvider initialLocale={localeFromCookie}>
            {children}
          </LocalizationProvider>
          <Toaster />
          <ToasterSonner richColors />
        </Suspense>
      </body>
    </html>
  )
}
